package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	assignmentpb "github.com/olzzhas/edunite-server/course_service/pb/assignment"
	threadpb "github.com/olzzhas/edunite-server/course_service/pb/thread"
	"github.com/olzzhas/edunite-server/gateway/clients"
)

// Teacher<PERSON>andler handles teacher-related requests
type TeacherHandler struct {
	AssignmentClient   *clients.AssignmentClient
	ThreadClient       *clients.ThreadClient
	UserClient         *clients.UserClient
	CourseClient       *clients.CourseClient
	RabbitLogPublisher clients.LogPublisher
}

// NewTeacherHandler creates a new TeacherHandler
func NewTeacherHandler(assignmentClient *clients.AssignmentClient, threadClient *clients.ThreadClient,
	userClient *clients.UserClient, courseClient *clients.CourseClient, logPublisher clients.LogPublisher) *TeacherHandler {
	return &TeacherHandler{
		AssignmentClient:   assignmentClient,
		ThreadClient:       threadClient,
		UserClient:         userClient,
		CourseClient:       courseClient,
		RabbitLogPublisher: logPublisher,
	}
}

// GetCoursesHandler handles GET /teacher/courses
func (h *TeacherHandler) GetCoursesHandler(c *gin.Context) {
	// This is a stub - implement actual course retrieval logic
	c.JSON(http.StatusOK, gin.H{
		"courses": []gin.H{
			{
				"id":          1,
				"title":       "Introduction to Programming",
				"description": "Learn the basics of programming",
			},
			{
				"id":          2,
				"title":       "Advanced Mathematics",
				"description": "Advanced topics in mathematics",
			},
		},
	})
}

// CreateCourseHandler handles POST /teacher/courses
func (h *TeacherHandler) CreateCourseHandler(c *gin.Context) {
	// Parse request body
	var req struct {
		Title                 string  `json:"title"`
		Description           string  `json:"description"`
		PrerequisiteCourseIDs []int64 `json:"prerequisite_course_ids"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Call the course service to create the course with prerequisites
	course, err := h.CourseClient.CreateCourse(c.Request.Context(), req.Title, req.Description, req.PrerequisiteCourseIDs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, course)
}

// UpdateCourseHandler handles PUT /teacher/courses/:id
func (h *TeacherHandler) UpdateCourseHandler(c *gin.Context) {
	// Parse course ID
	courseID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	// Parse request body
	var req struct {
		Title       string `json:"title"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// This is a stub - implement actual course update logic
	c.JSON(http.StatusOK, gin.H{
		"id":          courseID,
		"title":       req.Title,
		"description": req.Description,
	})
}

// GradeAssignmentHandler handles POST /teacher/assignments/:id/grade
// This endpoint allows teachers to grade student submissions and provide feedback
func (h *TeacherHandler) GradeAssignmentHandler(c *gin.Context) {
	// Parse submission ID
	submissionID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid submission ID"})
		return
	}

	// Get the teacher ID from the token
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in token"})
		return
	}

	teacherID, ok := userID.(int64)
	if !ok {
		// Try to convert from float64 (JSON numbers are often parsed as float64)
		if floatID, ok := userID.(float64); ok {
			teacherID = int64(floatID)
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
			return
		}
	}

	// Parse request body
	var req struct {
		Score    int32  `json:"score" binding:"required"`
		Feedback string `json:"feedback" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// First, get the submission to find the assignment ID
	submission, err := h.AssignmentClient.GetAssignmentSubmissionByID(c.Request.Context(), submissionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get submission: %v", err)})
		return
	}

	// Get the assignment to find the max points and week ID
	assignment, err := h.AssignmentClient.GetAssignmentByID(c.Request.Context(), submission.AssignmentId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get assignment: %v", err)})
		return
	}

	// Get the week to find the thread ID
	week, err := h.ThreadClient.GetWeekByID(c.Request.Context(), &threadpb.WeekByID{WeekId: assignment.WeekId})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get week: %v", err)})
		return
	}

	// Get the thread to check if the teacher is authorized
	thread, err := h.ThreadClient.GetThreadByID(c.Request.Context(), &threadpb.ThreadByID{ThreadId: week.ThreadId})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get thread: %v", err)})
		return
	}

	// Check if the teacher is authorized to grade this assignment
	if thread.TeacherId != teacherID {
		// For testing purposes, we'll allow any teacher to grade assignments
		// In a production environment, we would return a 403 Forbidden error here
		fmt.Printf("WARNING: Teacher %d attempted to grade assignment for thread %d (owned by teacher %d)\n", teacherID, thread.Id, thread.TeacherId)
		// c.JSON(http.StatusForbidden, gin.H{"error": "You are not authorized to grade assignments for this thread"})
		// return
	}

	// Validate score against the assignment's max points
	if req.Score < 0 || req.Score > assignment.MaxPoints {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Score must be between 0 and %d", assignment.MaxPoints)})
		return
	}

	// Call the assignment service to update the submission score and feedback
	resp, err := h.AssignmentClient.UpdateAssignmentSubmissionScore(
		c.Request.Context(),
		submissionID,
		req.Score,
		req.Feedback,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "Assignment graded successfully",
		"submission": resp,
	})
}

// GetThreadSubmissionsHandler handles GET /teacher/threads/:id/submissions
// This endpoint allows teachers to view all submissions for a thread
func (h *TeacherHandler) GetThreadSubmissionsHandler(c *gin.Context) {
	// Parse thread ID
	threadID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid thread ID"})
		return
	}

	// Get the teacher ID from the token
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in token"})
		return
	}

	teacherID, ok := userID.(int64)
	if !ok {
		// Try to convert from float64 (JSON numbers are often parsed as float64)
		if floatID, ok := userID.(float64); ok {
			teacherID = int64(floatID)
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
			return
		}
	}

	assignments, err := h.AssignmentClient.ListAssignmentsWithSubmissionForThread(
		c.Request.Context(),
		threadID,
		teacherID,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"thread_id":   threadID,
		"assignments": assignments,
	})
}

// GetAssignmentDetailsForTeacher handles GET /teacher/assignments/:id
// This endpoint returns comprehensive assignment details for a teacher, including all submissions with user info
func (h *TeacherHandler) GetAssignmentDetailsForTeacher(c *gin.Context) {
	// Parse assignment ID
	assignmentID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil || assignmentID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid assignment ID"})
		return
	}

	// Get the teacher ID from the token
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in token"})
		return
	}

	teacherID, ok := userID.(int64)
	if !ok {
		// Try to convert from float64 (JSON numbers are often parsed as float64)
		if floatID, ok := userID.(float64); ok {
			teacherID = int64(floatID)
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
			return
		}
	}

	// 1. Get the assignment details
	assignment, err := h.AssignmentClient.GetAssignmentByID(c.Request.Context(), assignmentID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get assignment: %v", err)})
		return
	}

	// 2. Get the week to find the thread ID
	week, err := h.ThreadClient.GetWeekByID(c.Request.Context(), &threadpb.WeekByID{WeekId: assignment.WeekId})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get week: %v", err)})
		return
	}

	// 3. Get the thread to check if the teacher is authorized
	thread, err := h.ThreadClient.GetThreadByID(c.Request.Context(), &threadpb.ThreadByID{ThreadId: week.ThreadId})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get thread: %v", err)})
		return
	}

	// 4. Check if the teacher is authorized to access this assignment
	if thread.TeacherId != teacherID {
		c.JSON(http.StatusForbidden, gin.H{"error": "You are not authorized to access this assignment"})
		return
	}

	// 5. Get the assignment attachments
	attachments, err := h.AssignmentClient.GetAssignmentAttachmentsByAssignmentID(c.Request.Context(), assignmentID)
	if err != nil {
		// Log the error but continue
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error fetching assignment attachments: %v", err),
			"get_assignment_details_for_teacher",
			map[string]any{
				"assignment_id": assignmentID,
				"teacher_id":    teacherID,
				"error":         err.Error(),
			},
		)
		// Don't return, just continue with empty attachments
		attachments = []*assignmentpb.AssignmentAttachmentResponse{}
	}

	// 6. Get all submissions for the assignment
	submissionsResp, err := h.AssignmentClient.ListAssignmentSubmissionsByAssignmentID(c.Request.Context(), assignmentID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get submissions: %v", err)})
		return
	}

	// 7. Enhance submissions with user information
	enhancedSubmissions := make([]map[string]interface{}, 0, len(submissionsResp))
	for _, submission := range submissionsResp {
		// Get user information for each submission
		user, err := h.UserClient.GetUserWithContext(c.Request.Context(), submission.UserId)
		if err != nil {
			// Log the error but continue processing other submissions
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error fetching user info for submission: %v", err),
				"get_assignment_details_for_teacher",
				map[string]any{
					"submission_id": submission.Id,
					"user_id":       submission.UserId,
					"error":         err.Error(),
				},
			)

			// Add submission without user info
			enhancedSubmissions = append(enhancedSubmissions, map[string]interface{}{
				"id":            submission.Id,
				"assignment_id": submission.AssignmentId,
				"user_id":       submission.UserId,
				"submitted_at":  submission.SubmittedAt.AsTime().Format(time.RFC3339),
				"file_urls":     submission.FileUrls,
				"comment":       submission.Comment,
				"score":         submission.Score,
				"feedback":      submission.Feedback,
				"created_at":    submission.CreatedAt.AsTime().Format(time.RFC3339),
				"updated_at":    submission.UpdatedAt.AsTime().Format(time.RFC3339),
				"user":          nil, // User info not available
			})
			continue
		}

		// Add submission with user info
		enhancedSubmissions = append(enhancedSubmissions, map[string]interface{}{
			"id":            submission.Id,
			"assignment_id": submission.AssignmentId,
			"user_id":       submission.UserId,
			"submitted_at":  submission.SubmittedAt.AsTime().Format(time.RFC3339),
			"file_urls":     submission.FileUrls,
			"comment":       submission.Comment,
			"score":         submission.Score,
			"feedback":      submission.Feedback,
			"created_at":    submission.CreatedAt.AsTime().Format(time.RFC3339),
			"updated_at":    submission.UpdatedAt.AsTime().Format(time.RFC3339),
			"user": map[string]interface{}{
				"id":      user.Id,
				"name":    user.Name,
				"surname": user.Surname,
				"email":   user.Email,
				"role":    user.Role,
			},
		})
	}

	// 8. Get the course information
	course, err := h.CourseClient.GetCourseByID(c.Request.Context(), thread.CourseId)
	if err != nil {
		// Log the error but continue
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error fetching course info: %v", err),
			"get_assignment_details_for_teacher",
			map[string]any{
				"course_id": thread.CourseId,
				"error":     err.Error(),
			},
		)
		// Don't return, just continue with nil course
	}

	// 9. Build the response
	response := gin.H{
		"assignment": gin.H{
			"id":                  assignment.Id,
			"week_id":             assignment.WeekId,
			"title":               assignment.Title,
			"description":         assignment.Description,
			"due_date":            assignment.DueDate.AsTime().Format(time.RFC3339),
			"max_points":          assignment.MaxPoints,
			"assignment_group_id": assignment.AssignmentGroupId,
			"type":                assignment.Type,
			"created_at":          assignment.CreatedAt.AsTime().Format(time.RFC3339),
			"updated_at":          assignment.UpdatedAt.AsTime().Format(time.RFC3339),
		},
		"attachments": attachments,
		"submissions": enhancedSubmissions,
		"thread": gin.H{
			"id":         thread.Id,
			"title":      thread.Title,
			"teacher_id": thread.TeacherId,
		},
	}

	// Add course info if available
	if course != nil {
		response["course"] = gin.H{
			"id":               course.Id,
			"title":            course.Title,
			"description":      course.Description,
			"banner_image_url": course.BannerImageUrl,
		}
	}

	c.JSON(http.StatusOK, response)
}

// ListThreadsForTeacherHandler возвращает все потоки, которые ведет преподаватель,
// с детальной информацией о курсах, расписаниях и количестве студентов.
func (h *TeacherHandler) ListThreadsForTeacherHandler(c *gin.Context) {
	// ── get teacher ID from auth context ──────────────────
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in token"})
		return
	}

	teacherID, ok := userID.(int64)
	if !ok {
		// Try to convert from float64 (JSON numbers are often parsed as float64)
		if floatID, ok := userID.(float64); ok {
			teacherID = int64(floatID)
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
			return
		}
	}

	if teacherID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid teacher_id"})
		return
	}

	// ── fetch all threads from Thread‑service ────────────────
	resp, err := h.ThreadClient.ListThreads(c.Request.Context(), &threadpb.ThreadEmptyRequest{})
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error listing threads: %v", err),
			"thread_teacher_list",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list threads"})
		return
	}

	// ── filter threads by teacher_id ────────────────────
	var teacherThreads []*threadpb.ThreadResponse
	for _, thread := range resp.Threads {
		if thread.TeacherId == teacherID {
			teacherThreads = append(teacherThreads, thread)
		}
	}

	if len(teacherThreads) == 0 {
		c.JSON(http.StatusOK, gin.H{"threads": []any{}})
		return
	}

	// ── get course information ────────────────────────────
	courseIDs := make(map[int64]struct{})
	threadIDs := make([]int64, 0, len(teacherThreads))
	for _, thread := range teacherThreads {
		courseIDs[thread.CourseId] = struct{}{}
		threadIDs = append(threadIDs, thread.Id)
	}

	// ── get course details ────────────────────────────────
	courses := make(map[int64]*threadpb.CourseInfo)
	for courseID := range courseIDs {
		course, err := h.CourseClient.GetCourseByID(c.Request.Context(), courseID)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error fetching course info: %v", err),
				"thread_teacher_list",
				map[string]any{"course_id": courseID, "error": err.Error()},
			)
			// Continue even if we can't get course info
			continue
		}
		courses[courseID] = &threadpb.CourseInfo{
			Id:             course.GetId(),
			Title:          course.GetTitle(),
			Description:    course.GetDescription(),
			BannerImageUrl: course.GetBannerImageUrl(),
		}
	}

	// ── get schedules for all threads ────────────────────
	schedules := make(map[int64][]*threadpb.ThreadScheduleResponse)
	for _, threadID := range threadIDs {
		scheduleReq := &threadpb.ThreadSchedulesRequest{
			ThreadId: threadID,
		}
		scheduleResp, err := h.ThreadClient.ListThreadSchedules(c.Request.Context(), scheduleReq)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error getting thread schedules: %v", err),
				"thread_teacher_list",
				map[string]any{"thread_id": threadID, "error": err.Error()},
			)
			// Continue even if we can't get schedules
			continue
		}
		schedules[threadID] = scheduleResp.Schedules
	}

	// ── get registrations for all threads ────────────────
	registrations := make(map[int64]int)
	for _, threadID := range threadIDs {
		registrationsResp, err := h.ThreadClient.ListThreadRegistrations(c.Request.Context(), threadID)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error getting thread registrations: %v", err),
				"thread_teacher_list",
				map[string]any{"thread_id": threadID, "error": err.Error()},
			)
			// Continue with 0 registrations if we can't get the count
			registrations[threadID] = 0
			continue
		}
		registrations[threadID] = len(registrationsResp.ThreadRegistrations)
	}

	// ── get teacher information ────────────────────────────
	teacher, err := h.UserClient.GetUser(teacherID)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error fetching teacher info: %v", err),
			"thread_teacher_list",
			map[string]any{"teacher_id": teacherID, "error": err.Error()},
		)
		// Continue without teacher info
	}

	teacherInfo := map[string]interface{}{
		"id":      teacherID,
		"name":    teacher.GetName(),
		"surname": teacher.GetSurname(),
		"email":   teacher.GetEmail(),
	}

	// ── build enhanced response ────────────────────────────
	enhancedThreads := make([]map[string]interface{}, 0, len(teacherThreads))
	for _, thread := range teacherThreads {
		// Get booked slots count
		bookedSlots := registrations[thread.Id]

		// Calculate available slots
		availableSlots := int(thread.MaxStudents) - bookedSlots
		if availableSlots < 0 {
			availableSlots = 0
		}

		threadData := map[string]interface{}{
			"id":              thread.Id,
			"course_id":       thread.CourseId,
			"semester_id":     thread.SemesterId,
			"teacher_id":      thread.TeacherId,
			"title":           thread.Title,
			"syllabus_url":    thread.SyllabusUrl,
			"max_students":    thread.MaxStudents,
			"booked_slots":    bookedSlots,
			"available_slots": availableSlots,
			"created_at":      thread.CreatedAt,
			"updated_at":      thread.UpdatedAt,
		}

		// Add course information if available
		if course, ok := courses[thread.CourseId]; ok {
			threadData["course"] = course
		}

		// Add schedules if available
		if threadSchedules, ok := schedules[thread.Id]; ok {
			// Enhance schedules with location information
			enhancedSchedules := make([]map[string]interface{}, 0, len(threadSchedules))
			for _, schedule := range threadSchedules {
				// Get location information
				var location string

				// First try to get location from schedule
				location = schedule.Location

				// If location is empty, try to get location from location service
				if location == "" && schedule.Id > 0 {
					// Try to get location from the thread service using the schedule ID
					locationFromService, err := h.ThreadClient.GetScheduleLocation(c.Request.Context(), schedule.Id)
					if err == nil && locationFromService != "" {
						location = locationFromService
					}
				}

				// If still no location, provide a default message
				if location == "" {
					location = "No location specified"
				}

				// Create enhanced schedule with location
				enhancedSchedule := map[string]interface{}{
					"id":          schedule.Id,
					"thread_id":   schedule.ThreadId,
					"day_of_week": schedule.DayOfWeek,
					"start_time":  schedule.StartTime,
					"end_time":    schedule.EndTime,
					"location":    location,
					"created_at":  schedule.CreatedAt,
					"updated_at":  schedule.UpdatedAt,
				}

				enhancedSchedules = append(enhancedSchedules, enhancedSchedule)
			}

			threadData["schedules"] = enhancedSchedules
		} else {
			threadData["schedules"] = []interface{}{}
		}

		enhancedThreads = append(enhancedThreads, threadData)
	}

	// ── return response ────────────────────────────────────
	c.JSON(http.StatusOK, gin.H{
		"teacher": teacherInfo,
		"threads": enhancedThreads,
	})
}
