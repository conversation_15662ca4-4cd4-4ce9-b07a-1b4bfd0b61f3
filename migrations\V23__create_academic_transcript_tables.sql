-- Create academic standing enum
CREATE TYPE academic_standing AS ENUM (
    'good_standing',
    'academic_warning',
    'academic_probation',
    'academic_suspension',
    'dismissed'
);

-- Create degree level enum
CREATE TYPE degree_level AS ENUM (
    'bachelor',
    'master',
    'phd',
    'certificate',
    'diploma'
);

-- Create degree status enum
CREATE TYPE degree_status AS ENUM (
    'in_progress',
    'completed',
    'withdrawn',
    'transferred'
);

-- Create grade scale enum
CREATE TYPE grade_scale AS ENUM (
    'letter', -- A, B, C, D, F
    'numeric', -- 0-100
    'gpa_4_0', -- 0.0-4.0
    'pass_fail'
);

-- Create degrees table
CREATE TABLE IF NOT EXISTS degrees (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    level degree_level NOT NULL,
    description TEXT,
    required_credits INT NOT NULL DEFAULT 120,
    min_gpa NUMERIC(3,2) DEFAULT 2.0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create academic transcripts table
CREATE TABLE IF NOT EXISTS academic_transcripts (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    degree_id BIGINT REFERENCES degrees(id) ON DELETE SET NULL,
    cumulative_gpa NUMERIC(3,2) DEFAULT 0.0,
    total_credits_attempted INT DEFAULT 0,
    total_credits_earned INT DEFAULT 0,
    academic_standing academic_standing DEFAULT 'good_standing',
    graduation_date DATE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, degree_id)
);

-- Create transcript entries table
CREATE TABLE IF NOT EXISTS transcript_entries (
    id BIGSERIAL PRIMARY KEY,
    transcript_id BIGINT NOT NULL REFERENCES academic_transcripts(id) ON DELETE CASCADE,
    course_id BIGINT NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
    thread_id BIGINT REFERENCES threads(id) ON DELETE SET NULL,
    semester_id BIGINT NOT NULL REFERENCES semesters(id) ON DELETE CASCADE,
    grade_letter VARCHAR(2), -- A+, A, A-, B+, etc.
    grade_numeric NUMERIC(5,2), -- 0.00-100.00
    grade_points NUMERIC(3,2), -- 0.00-4.00
    credits INT NOT NULL DEFAULT 3,
    is_transfer BOOLEAN DEFAULT FALSE,
    is_repeated BOOLEAN DEFAULT FALSE,
    completion_date DATE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(transcript_id, course_id, semester_id)
);

-- Create student degrees table (for tracking multiple degrees)
CREATE TABLE IF NOT EXISTS student_degrees (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    degree_id BIGINT NOT NULL REFERENCES degrees(id) ON DELETE CASCADE,
    status degree_status DEFAULT 'in_progress',
    start_date DATE NOT NULL,
    expected_graduation_date DATE,
    actual_graduation_date DATE,
    final_gpa NUMERIC(3,2),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, degree_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_academic_transcripts_user_id ON academic_transcripts(user_id);
CREATE INDEX IF NOT EXISTS idx_academic_transcripts_degree_id ON academic_transcripts(degree_id);
CREATE INDEX IF NOT EXISTS idx_transcript_entries_transcript_id ON transcript_entries(transcript_id);
CREATE INDEX IF NOT EXISTS idx_transcript_entries_course_id ON transcript_entries(course_id);
CREATE INDEX IF NOT EXISTS idx_transcript_entries_semester_id ON transcript_entries(semester_id);
CREATE INDEX IF NOT EXISTS idx_student_degrees_user_id ON student_degrees(user_id);
CREATE INDEX IF NOT EXISTS idx_student_degrees_degree_id ON student_degrees(degree_id);

-- Create triggers for updated_at
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_degrees_updated
    BEFORE UPDATE ON degrees
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER trg_academic_transcripts_updated
    BEFORE UPDATE ON academic_transcripts
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER trg_transcript_entries_updated
    BEFORE UPDATE ON transcript_entries
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER trg_student_degrees_updated
    BEFORE UPDATE ON student_degrees
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

-- Insert some default degrees
INSERT INTO degrees (name, level, description, required_credits, min_gpa) VALUES
('Bachelor of Science in Computer Science', 'bachelor', 'Undergraduate degree in Computer Science', 120, 2.0),
('Bachelor of Arts in Mathematics', 'bachelor', 'Undergraduate degree in Mathematics', 120, 2.0),
('Master of Science in Computer Science', 'master', 'Graduate degree in Computer Science', 36, 3.0),
('Master of Business Administration', 'master', 'Graduate degree in Business Administration', 48, 3.0),
('Doctor of Philosophy in Computer Science', 'phd', 'Doctoral degree in Computer Science', 72, 3.5);
