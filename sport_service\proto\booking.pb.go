// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/booking.proto

package sportpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Booking status enum
type BookingStatus int32

const (
	BookingStatus_PENDING   BookingStatus = 0
	BookingStatus_CONFIRMED BookingStatus = 1
	BookingStatus_CANCELLED BookingStatus = 2
	BookingStatus_COMPLETED BookingStatus = 3
)

// Enum value maps for BookingStatus.
var (
	BookingStatus_name = map[int32]string{
		0: "PENDING",
		1: "CONFIRMED",
		2: "CANCELLED",
		3: "COMPLETED",
	}
	BookingStatus_value = map[string]int32{
		"PENDING":   0,
		"CONFIRMED": 1,
		"CANCELLED": 2,
		"COMPLETED": 3,
	}
)

func (x BookingStatus) Enum() *BookingStatus {
	p := new(BookingStatus)
	*p = x
	return p
}

func (x BookingStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BookingStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_booking_proto_enumTypes[0].Descriptor()
}

func (BookingStatus) Type() protoreflect.EnumType {
	return &file_proto_booking_proto_enumTypes[0]
}

func (x BookingStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BookingStatus.Descriptor instead.
func (BookingStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_booking_proto_rawDescGZIP(), []int{0}
}

// Create booking request
type CreateBookingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ScheduleId    int64                  `protobuf:"varint,2,opt,name=schedule_id,json=scheduleId,proto3" json:"schedule_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateBookingRequest) Reset() {
	*x = CreateBookingRequest{}
	mi := &file_proto_booking_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateBookingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingRequest) ProtoMessage() {}

func (x *CreateBookingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_booking_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingRequest.ProtoReflect.Descriptor instead.
func (*CreateBookingRequest) Descriptor() ([]byte, []int) {
	return file_proto_booking_proto_rawDescGZIP(), []int{0}
}

func (x *CreateBookingRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CreateBookingRequest) GetScheduleId() int64 {
	if x != nil {
		return x.ScheduleId
	}
	return 0
}

// Get booking request
type GetBookingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBookingRequest) Reset() {
	*x = GetBookingRequest{}
	mi := &file_proto_booking_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBookingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookingRequest) ProtoMessage() {}

func (x *GetBookingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_booking_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookingRequest.ProtoReflect.Descriptor instead.
func (*GetBookingRequest) Descriptor() ([]byte, []int) {
	return file_proto_booking_proto_rawDescGZIP(), []int{1}
}

func (x *GetBookingRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Cancel booking request
type CancelBookingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelBookingRequest) Reset() {
	*x = CancelBookingRequest{}
	mi := &file_proto_booking_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelBookingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelBookingRequest) ProtoMessage() {}

func (x *CancelBookingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_booking_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelBookingRequest.ProtoReflect.Descriptor instead.
func (*CancelBookingRequest) Descriptor() ([]byte, []int) {
	return file_proto_booking_proto_rawDescGZIP(), []int{2}
}

func (x *CancelBookingRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CancelBookingRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// List user bookings request
type ListUserBookingsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Status        BookingStatus          `protobuf:"varint,4,opt,name=status,proto3,enum=sportpb.BookingStatus" json:"status,omitempty"`
	Page          int32                  `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserBookingsRequest) Reset() {
	*x = ListUserBookingsRequest{}
	mi := &file_proto_booking_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserBookingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserBookingsRequest) ProtoMessage() {}

func (x *ListUserBookingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_booking_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserBookingsRequest.ProtoReflect.Descriptor instead.
func (*ListUserBookingsRequest) Descriptor() ([]byte, []int) {
	return file_proto_booking_proto_rawDescGZIP(), []int{3}
}

func (x *ListUserBookingsRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ListUserBookingsRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ListUserBookingsRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ListUserBookingsRequest) GetStatus() BookingStatus {
	if x != nil {
		return x.Status
	}
	return BookingStatus_PENDING
}

func (x *ListUserBookingsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListUserBookingsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// List schedule bookings request
type ListScheduleBookingsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ScheduleId    int64                  `protobuf:"varint,1,opt,name=schedule_id,json=scheduleId,proto3" json:"schedule_id,omitempty"`
	Status        BookingStatus          `protobuf:"varint,2,opt,name=status,proto3,enum=sportpb.BookingStatus" json:"status,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListScheduleBookingsRequest) Reset() {
	*x = ListScheduleBookingsRequest{}
	mi := &file_proto_booking_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListScheduleBookingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListScheduleBookingsRequest) ProtoMessage() {}

func (x *ListScheduleBookingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_booking_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListScheduleBookingsRequest.ProtoReflect.Descriptor instead.
func (*ListScheduleBookingsRequest) Descriptor() ([]byte, []int) {
	return file_proto_booking_proto_rawDescGZIP(), []int{4}
}

func (x *ListScheduleBookingsRequest) GetScheduleId() int64 {
	if x != nil {
		return x.ScheduleId
	}
	return 0
}

func (x *ListScheduleBookingsRequest) GetStatus() BookingStatus {
	if x != nil {
		return x.Status
	}
	return BookingStatus_PENDING
}

func (x *ListScheduleBookingsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListScheduleBookingsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Booking response
type BookingResponse struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	Id         int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ScheduleId int64                  `protobuf:"varint,2,opt,name=schedule_id,json=scheduleId,proto3" json:"schedule_id,omitempty"`
	UserId     int64                  `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Status     BookingStatus          `protobuf:"varint,4,opt,name=status,proto3,enum=sportpb.BookingStatus" json:"status,omitempty"`
	CreatedAt  *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt  *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Additional information
	Schedule      *ScheduleInfo `protobuf:"bytes,7,opt,name=schedule,proto3" json:"schedule,omitempty"`
	User          *UserInfo     `protobuf:"bytes,8,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BookingResponse) Reset() {
	*x = BookingResponse{}
	mi := &file_proto_booking_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BookingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookingResponse) ProtoMessage() {}

func (x *BookingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_booking_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookingResponse.ProtoReflect.Descriptor instead.
func (*BookingResponse) Descriptor() ([]byte, []int) {
	return file_proto_booking_proto_rawDescGZIP(), []int{5}
}

func (x *BookingResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BookingResponse) GetScheduleId() int64 {
	if x != nil {
		return x.ScheduleId
	}
	return 0
}

func (x *BookingResponse) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *BookingResponse) GetStatus() BookingStatus {
	if x != nil {
		return x.Status
	}
	return BookingStatus_PENDING
}

func (x *BookingResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BookingResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *BookingResponse) GetSchedule() *ScheduleInfo {
	if x != nil {
		return x.Schedule
	}
	return nil
}

func (x *BookingResponse) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

// List bookings response
type ListBookingsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Bookings      []*BookingResponse     `protobuf:"bytes,1,rep,name=bookings,proto3" json:"bookings,omitempty"`
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListBookingsResponse) Reset() {
	*x = ListBookingsResponse{}
	mi := &file_proto_booking_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListBookingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookingsResponse) ProtoMessage() {}

func (x *ListBookingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_booking_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookingsResponse.ProtoReflect.Descriptor instead.
func (*ListBookingsResponse) Descriptor() ([]byte, []int) {
	return file_proto_booking_proto_rawDescGZIP(), []int{6}
}

func (x *ListBookingsResponse) GetBookings() []*BookingResponse {
	if x != nil {
		return x.Bookings
	}
	return nil
}

func (x *ListBookingsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListBookingsResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListBookingsResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Get booking stats request
type GetBookingStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ScheduleId    int64                  `protobuf:"varint,2,opt,name=schedule_id,json=scheduleId,proto3" json:"schedule_id,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBookingStatsRequest) Reset() {
	*x = GetBookingStatsRequest{}
	mi := &file_proto_booking_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBookingStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookingStatsRequest) ProtoMessage() {}

func (x *GetBookingStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_booking_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookingStatsRequest.ProtoReflect.Descriptor instead.
func (*GetBookingStatsRequest) Descriptor() ([]byte, []int) {
	return file_proto_booking_proto_rawDescGZIP(), []int{7}
}

func (x *GetBookingStatsRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetBookingStatsRequest) GetScheduleId() int64 {
	if x != nil {
		return x.ScheduleId
	}
	return 0
}

func (x *GetBookingStatsRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetBookingStatsRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

// Booking stats response
type BookingStatsResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TotalBookings     int32                  `protobuf:"varint,1,opt,name=total_bookings,json=totalBookings,proto3" json:"total_bookings,omitempty"`
	PendingBookings   int32                  `protobuf:"varint,2,opt,name=pending_bookings,json=pendingBookings,proto3" json:"pending_bookings,omitempty"`
	ConfirmedBookings int32                  `protobuf:"varint,3,opt,name=confirmed_bookings,json=confirmedBookings,proto3" json:"confirmed_bookings,omitempty"`
	CancelledBookings int32                  `protobuf:"varint,4,opt,name=cancelled_bookings,json=cancelledBookings,proto3" json:"cancelled_bookings,omitempty"`
	CompletedBookings int32                  `protobuf:"varint,5,opt,name=completed_bookings,json=completedBookings,proto3" json:"completed_bookings,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BookingStatsResponse) Reset() {
	*x = BookingStatsResponse{}
	mi := &file_proto_booking_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BookingStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookingStatsResponse) ProtoMessage() {}

func (x *BookingStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_booking_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookingStatsResponse.ProtoReflect.Descriptor instead.
func (*BookingStatsResponse) Descriptor() ([]byte, []int) {
	return file_proto_booking_proto_rawDescGZIP(), []int{8}
}

func (x *BookingStatsResponse) GetTotalBookings() int32 {
	if x != nil {
		return x.TotalBookings
	}
	return 0
}

func (x *BookingStatsResponse) GetPendingBookings() int32 {
	if x != nil {
		return x.PendingBookings
	}
	return 0
}

func (x *BookingStatsResponse) GetConfirmedBookings() int32 {
	if x != nil {
		return x.ConfirmedBookings
	}
	return 0
}

func (x *BookingStatsResponse) GetCancelledBookings() int32 {
	if x != nil {
		return x.CancelledBookings
	}
	return 0
}

func (x *BookingStatsResponse) GetCompletedBookings() int32 {
	if x != nil {
		return x.CompletedBookings
	}
	return 0
}

// Get user semester stats request
type GetUserSemesterStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SemesterId    int64                  `protobuf:"varint,2,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserSemesterStatsRequest) Reset() {
	*x = GetUserSemesterStatsRequest{}
	mi := &file_proto_booking_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserSemesterStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSemesterStatsRequest) ProtoMessage() {}

func (x *GetUserSemesterStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_booking_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSemesterStatsRequest.ProtoReflect.Descriptor instead.
func (*GetUserSemesterStatsRequest) Descriptor() ([]byte, []int) {
	return file_proto_booking_proto_rawDescGZIP(), []int{9}
}

func (x *GetUserSemesterStatsRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetUserSemesterStatsRequest) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

// User semester stats response
type UserSemesterStatsResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	UserId          int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SemesterId      int64                  `protobuf:"varint,2,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	TotalBookings   int32                  `protobuf:"varint,3,opt,name=total_bookings,json=totalBookings,proto3" json:"total_bookings,omitempty"`
	TotalAttendance int32                  `protobuf:"varint,4,opt,name=total_attendance,json=totalAttendance,proto3" json:"total_attendance,omitempty"`
	AttendanceRate  float64                `protobuf:"fixed64,5,opt,name=attendance_rate,json=attendanceRate,proto3" json:"attendance_rate,omitempty"`
	SemesterLimit   int32                  `protobuf:"varint,6,opt,name=semester_limit,json=semesterLimit,proto3" json:"semester_limit,omitempty"`
	SemesterMinimum int32                  `protobuf:"varint,7,opt,name=semester_minimum,json=semesterMinimum,proto3" json:"semester_minimum,omitempty"`
	Passed          bool                   `protobuf:"varint,8,opt,name=passed,proto3" json:"passed,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UserSemesterStatsResponse) Reset() {
	*x = UserSemesterStatsResponse{}
	mi := &file_proto_booking_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserSemesterStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSemesterStatsResponse) ProtoMessage() {}

func (x *UserSemesterStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_booking_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSemesterStatsResponse.ProtoReflect.Descriptor instead.
func (*UserSemesterStatsResponse) Descriptor() ([]byte, []int) {
	return file_proto_booking_proto_rawDescGZIP(), []int{10}
}

func (x *UserSemesterStatsResponse) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserSemesterStatsResponse) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *UserSemesterStatsResponse) GetTotalBookings() int32 {
	if x != nil {
		return x.TotalBookings
	}
	return 0
}

func (x *UserSemesterStatsResponse) GetTotalAttendance() int32 {
	if x != nil {
		return x.TotalAttendance
	}
	return 0
}

func (x *UserSemesterStatsResponse) GetAttendanceRate() float64 {
	if x != nil {
		return x.AttendanceRate
	}
	return 0
}

func (x *UserSemesterStatsResponse) GetSemesterLimit() int32 {
	if x != nil {
		return x.SemesterLimit
	}
	return 0
}

func (x *UserSemesterStatsResponse) GetSemesterMinimum() int32 {
	if x != nil {
		return x.SemesterMinimum
	}
	return 0
}

func (x *UserSemesterStatsResponse) GetPassed() bool {
	if x != nil {
		return x.Passed
	}
	return false
}

// Schedule info (simplified)
type ScheduleInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Location      string                 `protobuf:"bytes,4,opt,name=location,proto3" json:"location,omitempty"`
	FacilityTitle string                 `protobuf:"bytes,5,opt,name=facility_title,json=facilityTitle,proto3" json:"facility_title,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScheduleInfo) Reset() {
	*x = ScheduleInfo{}
	mi := &file_proto_booking_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScheduleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleInfo) ProtoMessage() {}

func (x *ScheduleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_booking_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleInfo.ProtoReflect.Descriptor instead.
func (*ScheduleInfo) Descriptor() ([]byte, []int) {
	return file_proto_booking_proto_rawDescGZIP(), []int{11}
}

func (x *ScheduleInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ScheduleInfo) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ScheduleInfo) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *ScheduleInfo) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *ScheduleInfo) GetFacilityTitle() string {
	if x != nil {
		return x.FacilityTitle
	}
	return ""
}

// User info (simplified)
type UserInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	mi := &file_proto_booking_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_booking_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_proto_booking_proto_rawDescGZIP(), []int{12}
}

func (x *UserInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_proto_booking_proto protoreflect.FileDescriptor

const file_proto_booking_proto_rawDesc = "" +
	"\n" +
	"\x13proto/booking.proto\x12\asportpb\x1a\x1fgoogle/protobuf/timestamp.proto\"P\n" +
	"\x14CreateBookingRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1f\n" +
	"\vschedule_id\x18\x02 \x01(\x03R\n" +
	"scheduleId\"#\n" +
	"\x11GetBookingRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"?\n" +
	"\x14CancelBookingRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\"\x85\x02\n" +
	"\x17ListUserBookingsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x129\n" +
	"\n" +
	"start_date\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\x12.\n" +
	"\x06status\x18\x04 \x01(\x0e2\x16.sportpb.BookingStatusR\x06status\x12\x12\n" +
	"\x04page\x18\x05 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x06 \x01(\x05R\bpageSize\"\x9f\x01\n" +
	"\x1bListScheduleBookingsRequest\x12\x1f\n" +
	"\vschedule_id\x18\x01 \x01(\x03R\n" +
	"scheduleId\x12.\n" +
	"\x06status\x18\x02 \x01(\x0e2\x16.sportpb.BookingStatusR\x06status\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"\xdb\x02\n" +
	"\x0fBookingResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vschedule_id\x18\x02 \x01(\x03R\n" +
	"scheduleId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\x03R\x06userId\x12.\n" +
	"\x06status\x18\x04 \x01(\x0e2\x16.sportpb.BookingStatusR\x06status\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x121\n" +
	"\bschedule\x18\a \x01(\v2\x15.sportpb.ScheduleInfoR\bschedule\x12%\n" +
	"\x04user\x18\b \x01(\v2\x11.sportpb.UserInfoR\x04user\"\x93\x01\n" +
	"\x14ListBookingsResponse\x124\n" +
	"\bbookings\x18\x01 \x03(\v2\x18.sportpb.BookingResponseR\bbookings\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"\xc4\x01\n" +
	"\x16GetBookingStatsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1f\n" +
	"\vschedule_id\x18\x02 \x01(\x03R\n" +
	"scheduleId\x129\n" +
	"\n" +
	"start_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\"\xf5\x01\n" +
	"\x14BookingStatsResponse\x12%\n" +
	"\x0etotal_bookings\x18\x01 \x01(\x05R\rtotalBookings\x12)\n" +
	"\x10pending_bookings\x18\x02 \x01(\x05R\x0fpendingBookings\x12-\n" +
	"\x12confirmed_bookings\x18\x03 \x01(\x05R\x11confirmedBookings\x12-\n" +
	"\x12cancelled_bookings\x18\x04 \x01(\x05R\x11cancelledBookings\x12-\n" +
	"\x12completed_bookings\x18\x05 \x01(\x05R\x11completedBookings\"W\n" +
	"\x1bGetUserSemesterStatsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1f\n" +
	"\vsemester_id\x18\x02 \x01(\x03R\n" +
	"semesterId\"\xba\x02\n" +
	"\x19UserSemesterStatsResponse\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1f\n" +
	"\vsemester_id\x18\x02 \x01(\x03R\n" +
	"semesterId\x12%\n" +
	"\x0etotal_bookings\x18\x03 \x01(\x05R\rtotalBookings\x12)\n" +
	"\x10total_attendance\x18\x04 \x01(\x05R\x0ftotalAttendance\x12'\n" +
	"\x0fattendance_rate\x18\x05 \x01(\x01R\x0eattendanceRate\x12%\n" +
	"\x0esemester_limit\x18\x06 \x01(\x05R\rsemesterLimit\x12)\n" +
	"\x10semester_minimum\x18\a \x01(\x05R\x0fsemesterMinimum\x12\x16\n" +
	"\x06passed\x18\b \x01(\bR\x06passed\"\xd3\x01\n" +
	"\fScheduleInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x129\n" +
	"\n" +
	"start_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12\x1a\n" +
	"\blocation\x18\x04 \x01(\tR\blocation\x12%\n" +
	"\x0efacility_title\x18\x05 \x01(\tR\rfacilityTitle\".\n" +
	"\bUserInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name*I\n" +
	"\rBookingStatus\x12\v\n" +
	"\aPENDING\x10\x00\x12\r\n" +
	"\tCONFIRMED\x10\x01\x12\r\n" +
	"\tCANCELLED\x10\x02\x12\r\n" +
	"\tCOMPLETED\x10\x032\xdd\x04\n" +
	"\x0eBookingService\x12J\n" +
	"\rCreateBooking\x12\x1d.sportpb.CreateBookingRequest\x1a\x18.sportpb.BookingResponse\"\x00\x12D\n" +
	"\n" +
	"GetBooking\x12\x1a.sportpb.GetBookingRequest\x1a\x18.sportpb.BookingResponse\"\x00\x12J\n" +
	"\rCancelBooking\x12\x1d.sportpb.CancelBookingRequest\x1a\x18.sportpb.BookingResponse\"\x00\x12U\n" +
	"\x10ListUserBookings\x12 .sportpb.ListUserBookingsRequest\x1a\x1d.sportpb.ListBookingsResponse\"\x00\x12]\n" +
	"\x14ListScheduleBookings\x12$.sportpb.ListScheduleBookingsRequest\x1a\x1d.sportpb.ListBookingsResponse\"\x00\x12S\n" +
	"\x0fGetBookingStats\x12\x1f.sportpb.GetBookingStatsRequest\x1a\x1d.sportpb.BookingStatsResponse\"\x00\x12b\n" +
	"\x14GetUserSemesterStats\x12$.sportpb.GetUserSemesterStatsRequest\x1a\".sportpb.UserSemesterStatsResponse\"\x00B<Z:github.com/olzzhas/edunite-server/sport_service/pb/sportpbb\x06proto3"

var (
	file_proto_booking_proto_rawDescOnce sync.Once
	file_proto_booking_proto_rawDescData []byte
)

func file_proto_booking_proto_rawDescGZIP() []byte {
	file_proto_booking_proto_rawDescOnce.Do(func() {
		file_proto_booking_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_booking_proto_rawDesc), len(file_proto_booking_proto_rawDesc)))
	})
	return file_proto_booking_proto_rawDescData
}

var file_proto_booking_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_booking_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_proto_booking_proto_goTypes = []any{
	(BookingStatus)(0),                  // 0: sportpb.BookingStatus
	(*CreateBookingRequest)(nil),        // 1: sportpb.CreateBookingRequest
	(*GetBookingRequest)(nil),           // 2: sportpb.GetBookingRequest
	(*CancelBookingRequest)(nil),        // 3: sportpb.CancelBookingRequest
	(*ListUserBookingsRequest)(nil),     // 4: sportpb.ListUserBookingsRequest
	(*ListScheduleBookingsRequest)(nil), // 5: sportpb.ListScheduleBookingsRequest
	(*BookingResponse)(nil),             // 6: sportpb.BookingResponse
	(*ListBookingsResponse)(nil),        // 7: sportpb.ListBookingsResponse
	(*GetBookingStatsRequest)(nil),      // 8: sportpb.GetBookingStatsRequest
	(*BookingStatsResponse)(nil),        // 9: sportpb.BookingStatsResponse
	(*GetUserSemesterStatsRequest)(nil), // 10: sportpb.GetUserSemesterStatsRequest
	(*UserSemesterStatsResponse)(nil),   // 11: sportpb.UserSemesterStatsResponse
	(*ScheduleInfo)(nil),                // 12: sportpb.ScheduleInfo
	(*UserInfo)(nil),                    // 13: sportpb.UserInfo
	(*timestamppb.Timestamp)(nil),       // 14: google.protobuf.Timestamp
}
var file_proto_booking_proto_depIdxs = []int32{
	14, // 0: sportpb.ListUserBookingsRequest.start_date:type_name -> google.protobuf.Timestamp
	14, // 1: sportpb.ListUserBookingsRequest.end_date:type_name -> google.protobuf.Timestamp
	0,  // 2: sportpb.ListUserBookingsRequest.status:type_name -> sportpb.BookingStatus
	0,  // 3: sportpb.ListScheduleBookingsRequest.status:type_name -> sportpb.BookingStatus
	0,  // 4: sportpb.BookingResponse.status:type_name -> sportpb.BookingStatus
	14, // 5: sportpb.BookingResponse.created_at:type_name -> google.protobuf.Timestamp
	14, // 6: sportpb.BookingResponse.updated_at:type_name -> google.protobuf.Timestamp
	12, // 7: sportpb.BookingResponse.schedule:type_name -> sportpb.ScheduleInfo
	13, // 8: sportpb.BookingResponse.user:type_name -> sportpb.UserInfo
	6,  // 9: sportpb.ListBookingsResponse.bookings:type_name -> sportpb.BookingResponse
	14, // 10: sportpb.GetBookingStatsRequest.start_date:type_name -> google.protobuf.Timestamp
	14, // 11: sportpb.GetBookingStatsRequest.end_date:type_name -> google.protobuf.Timestamp
	14, // 12: sportpb.ScheduleInfo.start_time:type_name -> google.protobuf.Timestamp
	14, // 13: sportpb.ScheduleInfo.end_time:type_name -> google.protobuf.Timestamp
	1,  // 14: sportpb.BookingService.CreateBooking:input_type -> sportpb.CreateBookingRequest
	2,  // 15: sportpb.BookingService.GetBooking:input_type -> sportpb.GetBookingRequest
	3,  // 16: sportpb.BookingService.CancelBooking:input_type -> sportpb.CancelBookingRequest
	4,  // 17: sportpb.BookingService.ListUserBookings:input_type -> sportpb.ListUserBookingsRequest
	5,  // 18: sportpb.BookingService.ListScheduleBookings:input_type -> sportpb.ListScheduleBookingsRequest
	8,  // 19: sportpb.BookingService.GetBookingStats:input_type -> sportpb.GetBookingStatsRequest
	10, // 20: sportpb.BookingService.GetUserSemesterStats:input_type -> sportpb.GetUserSemesterStatsRequest
	6,  // 21: sportpb.BookingService.CreateBooking:output_type -> sportpb.BookingResponse
	6,  // 22: sportpb.BookingService.GetBooking:output_type -> sportpb.BookingResponse
	6,  // 23: sportpb.BookingService.CancelBooking:output_type -> sportpb.BookingResponse
	7,  // 24: sportpb.BookingService.ListUserBookings:output_type -> sportpb.ListBookingsResponse
	7,  // 25: sportpb.BookingService.ListScheduleBookings:output_type -> sportpb.ListBookingsResponse
	9,  // 26: sportpb.BookingService.GetBookingStats:output_type -> sportpb.BookingStatsResponse
	11, // 27: sportpb.BookingService.GetUserSemesterStats:output_type -> sportpb.UserSemesterStatsResponse
	21, // [21:28] is the sub-list for method output_type
	14, // [14:21] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_proto_booking_proto_init() }
func file_proto_booking_proto_init() {
	if File_proto_booking_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_booking_proto_rawDesc), len(file_proto_booking_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_booking_proto_goTypes,
		DependencyIndexes: file_proto_booking_proto_depIdxs,
		EnumInfos:         file_proto_booking_proto_enumTypes,
		MessageInfos:      file_proto_booking_proto_msgTypes,
	}.Build()
	File_proto_booking_proto = out.File
	file_proto_booking_proto_goTypes = nil
	file_proto_booking_proto_depIdxs = nil
}
