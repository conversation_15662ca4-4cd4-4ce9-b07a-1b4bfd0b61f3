package handlers

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
)

// LogHandler handles HTTP requests related to logs
type LogHandler struct {
	RabbitLogPublisher clients.LogPublisher
	LoggerClient       *clients.LoggerClient
}

// NewLogHandler creates a new LogHandler
func NewLogHandler(logPublisher clients.LogPublisher, loggerClient *clients.LoggerClient) *LogHandler {
	return &LogHandler{
		RabbitLogPublisher: logPublisher,
		LoggerClient:       loggerClient,
	}
}

// GetLogs handles GET /api/logs
func (h *LogHandler) GetLogs(c *gin.Context) {
	// Parse query parameters
	level := c.Query("level")
	service := c.Query("service")
	
	// Parse limit and skip
	limitStr := c.DefaultQuery("limit", "100")
	skipStr := c.DefaultQuery("skip", "0")
	
	limit, err := strconv.ParseInt(limitStr, 10, 64)
	if err != nil {
		limit = 100
	}
	
	skip, err := strconv.ParseInt(skipStr, 10, 64)
	if err != nil {
		skip = 0
	}
	
	// Parse date range
	startDateStr := c.Query("startDate")
	endDateStr := c.Query("endDate")
	
	var startDate, endDate time.Time
	
	if startDateStr != "" {
		startDate, err = time.Parse(time.RFC3339, startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start date format. Use RFC3339 format (e.g., 2025-04-25T00:00:00Z)"})
			return
		}
	}
	
	if endDateStr != "" {
		endDate, err = time.Parse(time.RFC3339, endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end date format. Use RFC3339 format (e.g., 2025-04-25T23:59:59Z)"})
			return
		}
	}
	
	// Create filter
	filter := clients.LogFilter{
		Level:     level,
		Service:   service,
		StartDate: startDate,
		EndDate:   endDate,
		Limit:     limit,
		Skip:      skip,
	}
	
	// Get logs
	logs, err := h.LoggerClient.GetLogs(context.Background(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, logs)
}

// GetLogByID handles GET /api/logs/:id
func (h *LogHandler) GetLogByID(c *gin.Context) {
	id := c.Param("id")
	
	log, err := h.LoggerClient.GetLogByID(context.Background(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, log)
}

// CountLogs handles GET /api/logs/count
func (h *LogHandler) CountLogs(c *gin.Context) {
	// Parse query parameters
	level := c.Query("level")
	service := c.Query("service")
	
	// Parse date range
	startDateStr := c.Query("startDate")
	endDateStr := c.Query("endDate")
	
	var startDate, endDate time.Time
	var err error
	
	if startDateStr != "" {
		startDate, err = time.Parse(time.RFC3339, startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start date format. Use RFC3339 format (e.g., 2025-04-25T00:00:00Z)"})
			return
		}
	}
	
	if endDateStr != "" {
		endDate, err = time.Parse(time.RFC3339, endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end date format. Use RFC3339 format (e.g., 2025-04-25T23:59:59Z)"})
			return
		}
	}
	
	// Create filter
	filter := clients.LogFilter{
		Level:     level,
		Service:   service,
		StartDate: startDate,
		EndDate:   endDate,
	}
	
	// Count logs
	count, err := h.LoggerClient.CountLogs(context.Background(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"count": count})
}

// GetLogLevels handles GET /api/logs/levels
func (h *LogHandler) GetLogLevels(c *gin.Context) {
	levels, err := h.LoggerClient.GetLogLevels(context.Background())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"levels": levels})
}

// GetLogServices handles GET /api/logs/services
func (h *LogHandler) GetLogServices(c *gin.Context) {
	services, err := h.LoggerClient.GetLogServices(context.Background())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"services": services})
}
