// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: proto/facility.proto

package sportpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	FacilityService_CreateFacility_FullMethodName = "/sportpb.FacilityService/CreateFacility"
	FacilityService_GetFacility_FullMethodName    = "/sportpb.FacilityService/GetFacility"
	FacilityService_UpdateFacility_FullMethodName = "/sportpb.FacilityService/UpdateFacility"
	FacilityService_DeleteFacility_FullMethodName = "/sportpb.FacilityService/DeleteFacility"
	FacilityService_ListFacilities_FullMethodName = "/sportpb.FacilityService/ListFacilities"
)

// FacilityServiceClient is the client API for FacilityService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FacilityServiceClient interface {
	// Create a new facility
	CreateFacility(ctx context.Context, in *CreateFacilityRequest, opts ...grpc.CallOption) (*FacilityResponse, error)
	// Get a facility by ID
	GetFacility(ctx context.Context, in *GetFacilityRequest, opts ...grpc.CallOption) (*FacilityResponse, error)
	// Update an existing facility
	UpdateFacility(ctx context.Context, in *UpdateFacilityRequest, opts ...grpc.CallOption) (*FacilityResponse, error)
	// Delete a facility
	DeleteFacility(ctx context.Context, in *DeleteFacilityRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// List facilities with filtering
	ListFacilities(ctx context.Context, in *ListFacilitiesRequest, opts ...grpc.CallOption) (*ListFacilitiesResponse, error)
}

type facilityServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFacilityServiceClient(cc grpc.ClientConnInterface) FacilityServiceClient {
	return &facilityServiceClient{cc}
}

func (c *facilityServiceClient) CreateFacility(ctx context.Context, in *CreateFacilityRequest, opts ...grpc.CallOption) (*FacilityResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FacilityResponse)
	err := c.cc.Invoke(ctx, FacilityService_CreateFacility_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *facilityServiceClient) GetFacility(ctx context.Context, in *GetFacilityRequest, opts ...grpc.CallOption) (*FacilityResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FacilityResponse)
	err := c.cc.Invoke(ctx, FacilityService_GetFacility_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *facilityServiceClient) UpdateFacility(ctx context.Context, in *UpdateFacilityRequest, opts ...grpc.CallOption) (*FacilityResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FacilityResponse)
	err := c.cc.Invoke(ctx, FacilityService_UpdateFacility_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *facilityServiceClient) DeleteFacility(ctx context.Context, in *DeleteFacilityRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, FacilityService_DeleteFacility_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *facilityServiceClient) ListFacilities(ctx context.Context, in *ListFacilitiesRequest, opts ...grpc.CallOption) (*ListFacilitiesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListFacilitiesResponse)
	err := c.cc.Invoke(ctx, FacilityService_ListFacilities_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FacilityServiceServer is the server API for FacilityService service.
// All implementations must embed UnimplementedFacilityServiceServer
// for forward compatibility.
type FacilityServiceServer interface {
	// Create a new facility
	CreateFacility(context.Context, *CreateFacilityRequest) (*FacilityResponse, error)
	// Get a facility by ID
	GetFacility(context.Context, *GetFacilityRequest) (*FacilityResponse, error)
	// Update an existing facility
	UpdateFacility(context.Context, *UpdateFacilityRequest) (*FacilityResponse, error)
	// Delete a facility
	DeleteFacility(context.Context, *DeleteFacilityRequest) (*emptypb.Empty, error)
	// List facilities with filtering
	ListFacilities(context.Context, *ListFacilitiesRequest) (*ListFacilitiesResponse, error)
	mustEmbedUnimplementedFacilityServiceServer()
}

// UnimplementedFacilityServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFacilityServiceServer struct{}

func (UnimplementedFacilityServiceServer) CreateFacility(context.Context, *CreateFacilityRequest) (*FacilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFacility not implemented")
}
func (UnimplementedFacilityServiceServer) GetFacility(context.Context, *GetFacilityRequest) (*FacilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFacility not implemented")
}
func (UnimplementedFacilityServiceServer) UpdateFacility(context.Context, *UpdateFacilityRequest) (*FacilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateFacility not implemented")
}
func (UnimplementedFacilityServiceServer) DeleteFacility(context.Context, *DeleteFacilityRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteFacility not implemented")
}
func (UnimplementedFacilityServiceServer) ListFacilities(context.Context, *ListFacilitiesRequest) (*ListFacilitiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFacilities not implemented")
}
func (UnimplementedFacilityServiceServer) mustEmbedUnimplementedFacilityServiceServer() {}
func (UnimplementedFacilityServiceServer) testEmbeddedByValue()                         {}

// UnsafeFacilityServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FacilityServiceServer will
// result in compilation errors.
type UnsafeFacilityServiceServer interface {
	mustEmbedUnimplementedFacilityServiceServer()
}

func RegisterFacilityServiceServer(s grpc.ServiceRegistrar, srv FacilityServiceServer) {
	// If the following call pancis, it indicates UnimplementedFacilityServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&FacilityService_ServiceDesc, srv)
}

func _FacilityService_CreateFacility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateFacilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FacilityServiceServer).CreateFacility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FacilityService_CreateFacility_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FacilityServiceServer).CreateFacility(ctx, req.(*CreateFacilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FacilityService_GetFacility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFacilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FacilityServiceServer).GetFacility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FacilityService_GetFacility_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FacilityServiceServer).GetFacility(ctx, req.(*GetFacilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FacilityService_UpdateFacility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFacilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FacilityServiceServer).UpdateFacility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FacilityService_UpdateFacility_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FacilityServiceServer).UpdateFacility(ctx, req.(*UpdateFacilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FacilityService_DeleteFacility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteFacilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FacilityServiceServer).DeleteFacility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FacilityService_DeleteFacility_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FacilityServiceServer).DeleteFacility(ctx, req.(*DeleteFacilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FacilityService_ListFacilities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListFacilitiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FacilityServiceServer).ListFacilities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FacilityService_ListFacilities_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FacilityServiceServer).ListFacilities(ctx, req.(*ListFacilitiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FacilityService_ServiceDesc is the grpc.ServiceDesc for FacilityService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FacilityService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sportpb.FacilityService",
	HandlerType: (*FacilityServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateFacility",
			Handler:    _FacilityService_CreateFacility_Handler,
		},
		{
			MethodName: "GetFacility",
			Handler:    _FacilityService_GetFacility_Handler,
		},
		{
			MethodName: "UpdateFacility",
			Handler:    _FacilityService_UpdateFacility_Handler,
		},
		{
			MethodName: "DeleteFacility",
			Handler:    _FacilityService_DeleteFacility_Handler,
		},
		{
			MethodName: "ListFacilities",
			Handler:    _FacilityService_ListFacilities_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/facility.proto",
}
