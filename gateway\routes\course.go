package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

func SetupCourseRoutes(r *gin.Engine, authClient *clients.AuthClient, courseHandler *handlers.CourseHandler) {
	courseGroup := r.Group("/course")
	//courseGroup.Use(AuthMiddleware(authClient))
	{
		// Create
		courseGroup.POST("", courseHandler.Create)                     // POST /course
		courseGroup.POST("/with-image", courseHandler.CreateWithImage) // POST /course/with-image

		// Read all
		courseGroup.GET("", courseHandler.GetAll) // GET /course

		// Read one
		courseGroup.GET("/:id", courseHandler.GetOne) // GET /course/:id

		// Update
		courseGroup.PUT("/:id", courseHandler.Update)                     // PUT /course/:id
		courseGroup.PUT("/:id/with-image", courseHandler.UpdateWithImage) // PUT /course/:id/with-image

		// Delete
		courseGroup.DELETE("/:id", courseHandler.Delete) // DELETE /course/:id
	}
}
