package server

import (
	"fmt"
	"log"
	"net"

	"github.com/olzzhas/edunite-server/auth_service/internal/auth"
	"github.com/olzzhas/edunite-server/auth_service/internal/config"
	"github.com/olzzhas/edunite-server/auth_service/internal/database"
	"github.com/olzzhas/edunite-server/auth_service/pb/generated/pb"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

// Server represents the gRPC server
type Server struct {
	cfg        *config.Config
	grpcServer *grpc.Server
}

// NewServer creates a new server instance
func NewServer(cfg *config.Config) *Server {
	return &Server{
		cfg:        cfg,
		grpcServer: grpc.NewServer(),
	}
}

// Start starts the gRPC server
func (s *Server) Start() error {
	// Connect to database
	db := database.ConnectDBCockroach(s.cfg)
	defer db.Close()

	// Create repositories
	userRepo := database.NewUserRepository(db)

	// Create JWT manager
	jwtManager := auth.NewJWTManager(s.cfg)

	// Create auth service
	authService := auth.NewAuthService(userRepo, jwtManager)

	// Register services
	pb.RegisterAuthServiceServer(s.grpcServer, authService)

	// Enable reflection for debugging
	reflection.Register(s.grpcServer)

	// Start listening
	lis, err := net.Listen("tcp", fmt.Sprintf(":%s", s.cfg.Server.Port))
	if err != nil {
		return fmt.Errorf("failed to listen: %w", err)
	}

	log.Printf("Auth service is running on port %s", s.cfg.Server.Port)

	// Start server
	if err := s.grpcServer.Serve(lis); err != nil {
		return fmt.Errorf("failed to serve: %w", err)
	}

	return nil
}

// Stop stops the gRPC server
func (s *Server) Stop() {
	s.grpcServer.GracefulStop()
}
