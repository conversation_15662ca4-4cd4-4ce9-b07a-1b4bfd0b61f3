# Test script to verify the database scanning fix

Write-Host "Testing the transcript API endpoints..." -ForegroundColor Green

# Test 1: Test the degrees endpoint (public, no auth required)
Write-Host "`n1. Testing GET /degrees (public endpoint)..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/degrees" -Method GET -ContentType "application/json"
    Write-Host "✅ SUCCESS: Degrees endpoint working!" -ForegroundColor Green
    Write-Host "Found $($response.degrees.Count) degrees" -ForegroundColor Cyan
    foreach ($degree in $response.degrees) {
        Write-Host "  - $($degree.name) ($($degree.level))" -ForegroundColor White
    }
} catch {
    Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Message -like "*OID 17100*") {
        Write-Host "🚨 OID 17100 error detected - database scanning issue still exists!" -ForegroundColor Red
    }
}

# Test 2: Test the transcript endpoint (should require auth)
Write-Host "`n2. Testing GET /transcripts/user/4 (protected endpoint)..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/transcripts/user/4" -Method GET -ContentType "application/json"
    Write-Host "✅ Unexpected success (should require auth)" -ForegroundColor Yellow
} catch {
    $errorMessage = $_.Exception.Message
    Write-Host "Response: $errorMessage" -ForegroundColor Cyan
    
    if ($errorMessage -like "*Authorization header required*") {
        Write-Host "✅ SUCCESS: Expected auth error (no OID scanning error)" -ForegroundColor Green
    } elseif ($errorMessage -like "*OID 17100*") {
        Write-Host "❌ FAILED: OID 17100 error still exists!" -ForegroundColor Red
    } else {
        Write-Host "⚠️  Different error than expected" -ForegroundColor Yellow
    }
}

# Test 3: Test a specific degree endpoint
Write-Host "`n3. Testing GET /degrees/1 (public endpoint)..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/degrees/1" -Method GET -ContentType "application/json"
    Write-Host "✅ SUCCESS: Specific degree endpoint working!" -ForegroundColor Green
    Write-Host "Degree: $($response.degree.name)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Message -like "*OID 17100*") {
        Write-Host "🚨 OID 17100 error detected - database scanning issue still exists!" -ForegroundColor Red
    }
}

Write-Host "`n🎯 Test Summary:" -ForegroundColor Green
Write-Host "If you see 'Authorization header required' instead of OID 17100 errors," -ForegroundColor White
Write-Host "then the database scanning fix is working correctly!" -ForegroundColor White
