package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	threadpb "github.com/olzzhas/edunite-server/course_service/pb/thread"
	"github.com/olzzhas/edunite-server/gateway/clients"
)

// LocationHandler handles HTTP requests for locations
type LocationHandler struct {
	ThreadService      *clients.ThreadClient
	RabbitLogPublisher clients.LogPublisher
}

// CreateLocationHandler creates a new location
// POST /locations
func (h *LocationHandler) CreateLocationHandler(c *gin.Context) {
	var req threadpb.LocationRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing CreateLocation request: %v", err),
			"location",
			map[string]any{"error": err.Error()},
		)
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	resp, err := h.ThreadService.CreateLocation(c.Request.Context(), &req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error creating location: %v", err),
			"location",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create location"})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

// GetLocationByIDHandler gets a location by ID
// GET /locations/:id
func (h *LocationHandler) GetLocationByIDHandler(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid location id"})
		return
	}

	resp, err := h.ThreadService.GetLocationByID(c.Request.Context(), id)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error fetching location: %v", err),
			"location",
			map[string]any{"location_id": idStr, "error": err.Error()},
		)
		c.JSON(http.StatusNotFound, gin.H{"error": "location not found"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// ListLocationsHandler lists all locations with pagination
// GET /locations?page=1&page_size=10
func (h *LocationHandler) ListLocationsHandler(c *gin.Context) {
	// Get pagination parameters
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// Get all locations
	resp, err := h.ThreadService.ListLocations(c.Request.Context())
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error listing locations: %v", err),
			"location",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list locations"})
		return
	}

	// Apply pagination
	totalLocations := len(resp.Locations)
	startIndex := (page - 1) * pageSize
	endIndex := startIndex + pageSize

	// Check if startIndex is out of bounds
	if startIndex >= totalLocations {
		c.JSON(http.StatusOK, gin.H{
			"locations":     []interface{}{},
			"total":         totalLocations,
			"page":          page,
			"page_size":     pageSize,
			"total_pages":   (totalLocations + pageSize - 1) / pageSize,
			"has_more":      false,
		})
		return
	}

	// Adjust endIndex if it's out of bounds
	if endIndex > totalLocations {
		endIndex = totalLocations
	}

	// Get the paginated subset of locations
	paginatedLocations := resp.Locations[startIndex:endIndex]

	c.JSON(http.StatusOK, gin.H{
		"locations":     paginatedLocations,
		"total":         totalLocations,
		"page":          page,
		"page_size":     pageSize,
		"total_pages":   (totalLocations + pageSize - 1) / pageSize,
		"has_more":      endIndex < totalLocations,
	})
}

// UpdateLocationHandler updates an existing location
// PUT /locations/:id
func (h *LocationHandler) UpdateLocationHandler(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid location id"})
		return
	}

	var req threadpb.LocationUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing UpdateLocation request: %v", err),
			"location",
			map[string]any{"location_id": idStr, "error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// Set the ID from the URL
	req.Id = id

	resp, err := h.ThreadService.UpdateLocation(c.Request.Context(), &req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error updating location: %v", err),
			"location",
			map[string]any{"location_id": idStr, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update location"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// DeleteLocationHandler deletes a location
// DELETE /locations/:id
func (h *LocationHandler) DeleteLocationHandler(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid location id"})
		return
	}

	_, err = h.ThreadService.DeleteLocation(c.Request.Context(), id)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error deleting location: %v", err),
			"location",
			map[string]any{"location_id": idStr, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete location"})
		return
	}

	c.Status(http.StatusNoContent)
}
