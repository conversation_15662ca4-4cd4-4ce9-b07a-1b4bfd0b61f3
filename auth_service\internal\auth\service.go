package auth

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/olzzhas/edunite-server/auth_service/internal/database"
	"github.com/olzzhas/edunite-server/auth_service/pb/generated/pb"
)

// Service implements the auth service logic
type Service struct {
	userRepo   database.UserRepository
	jwtManager *JWTManager
	pb.UnimplementedAuthServiceServer
}

// NewAuthService creates a new auth service
func NewAuthService(userRepo database.UserRepository, jwtManager *JWTManager) *Service {
	return &Service{
		userRepo:   userRepo,
		jwtManager: jwtManager,
	}
}

// Register creates a new user account
func (s *Service) Register(ctx context.Context, req *pb.RegisterRequest) (*pb.AuthResponse, error) {
	// Create user object
	user := &database.User{
		Username: req.GetUsername(),
		Email:    req.GetEmail(),
		Name:     req.GetName(),
		Surname:  req.<PERSON>(),
		Role:     database.RoleStudent, // Default role
	}

	// Set role if provided in the request
	if req.Role != "" {
		switch req.Role {
		case "admin":
			user.Role = database.RoleAdmin
		case "teacher":
			user.Role = database.RoleTeacher
		case "moderator":
			user.Role = database.RoleModerator
		}
	}

	// Save user to database
	err := s.userRepo.CreateUser(ctx, user, req.GetPassword())
	if err != nil {
		if errors.Is(err, database.ErrDuplicateEmail) {
			return nil, fmt.Errorf("email already exists: %w", err)
		}
		if errors.Is(err, database.ErrDuplicateUsername) {
			return nil, fmt.Errorf("username already exists: %w", err)
		}
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Generate tokens
	accessToken, accessExpiry, err := s.jwtManager.GenerateAccessToken(
		user.ID, user.Username, user.Email, string(user.Role),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, refreshExpiry, err := s.jwtManager.GenerateRefreshToken(
		user.ID, user.Username,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	// Create response
	return &pb.AuthResponse{
		AccessToken:      accessToken,
		RefreshToken:     refreshToken,
		ExpiresIn:        int64(time.Until(accessExpiry).Seconds()),
		RefreshExpiresIn: int64(time.Until(refreshExpiry).Seconds()),
		User: &pb.UserInfo{
			Id:      user.ID,
			Name:    user.Name,
			Surname: user.Surname,
			Email:   user.Email,
			Role:    string(user.Role),
		},
	}, nil
}

// Login authenticates a user and returns tokens
func (s *Service) Login(ctx context.Context, req *pb.LoginRequest) (*pb.AuthResponse, error) {
	// Authenticate user
	user, err := s.userRepo.Authenticate(ctx, req.GetUsername(), req.GetPassword())
	if err != nil {
		if errors.Is(err, database.ErrInvalidCredentials) {
			return nil, fmt.Errorf("invalid credentials: %w", err)
		}
		return nil, fmt.Errorf("authentication failed: %w", err)
	}

	// Generate tokens
	accessToken, accessExpiry, err := s.jwtManager.GenerateAccessToken(
		user.ID, user.Username, user.Email, string(user.Role),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, refreshExpiry, err := s.jwtManager.GenerateRefreshToken(
		user.ID, user.Username,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	// Create response
	return &pb.AuthResponse{
		AccessToken:      accessToken,
		RefreshToken:     refreshToken,
		ExpiresIn:        int64(time.Until(accessExpiry).Seconds()),
		RefreshExpiresIn: int64(time.Until(refreshExpiry).Seconds()),
		User: &pb.UserInfo{
			Id:      user.ID,
			Name:    user.Name,
			Surname: user.Surname,
			Email:   user.Email,
			Role:    string(user.Role),
		},
	}, nil
}

// ValidateToken checks if a token is valid
func (s *Service) ValidateToken(ctx context.Context, req *pb.ValidateTokenRequest) (*pb.ValidateTokenResponse, error) {
	claims, err := s.jwtManager.ValidateToken(req.GetToken())
	if err != nil {
		if errors.Is(err, ErrExpiredToken) {
			return &pb.ValidateTokenResponse{Valid: false}, nil
		}
		return &pb.ValidateTokenResponse{Valid: false}, fmt.Errorf("token validation failed: %w", err)
	}

	return &pb.ValidateTokenResponse{
		Valid:  true,
		UserId: fmt.Sprintf("%d", claims.UserID),
		Role:   claims.Role,
	}, nil
}

// RefreshToken generates a new access token using a refresh token
func (s *Service) RefreshToken(ctx context.Context, req *pb.RefreshTokenRequest) (*pb.AuthResponse, error) {
	// Validate refresh token
	claims, err := s.jwtManager.ValidateToken(req.GetRefreshToken())
	if err != nil {
		if errors.Is(err, ErrExpiredToken) {
			return nil, fmt.Errorf("refresh token has expired: %w", err)
		}
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	// Get user from database
	user, err := s.userRepo.GetUserByID(ctx, claims.UserID)
	if err != nil {
		if errors.Is(err, database.ErrUserNotFound) {
			return nil, fmt.Errorf("user not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Generate new tokens
	accessToken, accessExpiry, err := s.jwtManager.GenerateAccessToken(
		user.ID, user.Username, user.Email, string(user.Role),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, refreshExpiry, err := s.jwtManager.GenerateRefreshToken(
		user.ID, user.Username,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	// Create response
	return &pb.AuthResponse{
		AccessToken:      accessToken,
		RefreshToken:     refreshToken,
		ExpiresIn:        int64(time.Until(accessExpiry).Seconds()),
		RefreshExpiresIn: int64(time.Until(refreshExpiry).Seconds()),
		User: &pb.UserInfo{
			Id:      user.ID,
			Name:    user.Name,
			Surname: user.Surname,
			Email:   user.Email,
			Role:    string(user.Role),
		},
	}, nil
}
