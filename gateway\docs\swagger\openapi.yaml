openapi: 3.0.0
info:
  title: Edunite API
  version: 1.0.0
  description: API documentation for Edunite educational platform

servers:
  - url: http://localhost:8080
    description: Local development server

tags:
  - name: auth
    description: Authentication operations
  - name: users
    description: User management
  - name: courses
    description: Course management
  - name: threads
    description: Thread management
  - name: weeks
    description: Week management
  - name: assignments
    description: Assignment management
  - name: storage
    description: File storage operations
  - name: attendance
    description: Attendance management
  - name: semesters
    description: Semester management

paths:
  /auth/login:
    post:
      tags:
        - auth
      summary: Login user
      operationId: loginUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                password:
                  type: string
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                  refresh_token:
                    type: string

  /auth/refresh:
    post:
      tags:
        - auth
      summary: Refresh access token
      operationId: refreshToken
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                refresh_token:
                  type: string
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string

  /users:
    post:
      tags:
        - users
      summary: Create a new user
      operationId: createUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                password:
                  type: string
                first_name:
                  type: string
                last_name:
                  type: string
                role:
                  type: string
                  enum: [student, teacher, admin]
      responses:
        '201':
          description: User created successfully

    get:
      tags:
        - users
      summary: Get all users
      operationId: getAllUsers
      parameters:
        - name: role
          in: query
          schema:
            type: string
            enum: [student, teacher, admin]
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: List of users retrieved successfully

  /users/{id}:
    get:
      tags:
        - users
      summary: Get user by ID
      operationId: getUserById
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: User retrieved successfully

    put:
      tags:
        - users
      summary: Update user
      operationId: updateUser
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                first_name:
                  type: string
                last_name:
                  type: string
                role:
                  type: string
                  enum: [student, teacher, admin]
      responses:
        '200':
          description: User updated successfully

    delete:
      tags:
        - users
      summary: Delete user
      operationId: deleteUser
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: User deleted successfully

  /storage/upload:
    post:
      tags:
        - storage
      summary: Upload a file
      operationId: uploadFile
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                bucket:
                  type: string
                object:
                  type: string
                file:
                  type: string
                  format: binary
      responses:
        '200':
          description: File uploaded successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  url:
                    type: string

  /storage/download/{bucket}/{object}:
    get:
      tags:
        - storage
      summary: Download a file
      operationId: downloadFile
      parameters:
        - name: bucket
          in: path
          required: true
          schema:
            type: string
        - name: object
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: File downloaded successfully
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary

  /course:
    post:
      tags:
        - courses
      summary: Create a new course
      operationId: createCourse
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                title:
                  type: string
                description:
                  type: string
                banner_image:
                  type: string
                  format: binary
      responses:
        '201':
          description: Course created successfully

    get:
      tags:
        - courses
      summary: Get all courses
      operationId: getAllCourses
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: List of courses retrieved successfully

  /course/{id}:
    get:
      tags:
        - courses
      summary: Get course by ID
      operationId: getCourseById
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Course retrieved successfully

    put:
      tags:
        - courses
      summary: Update course
      operationId: updateCourse
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                title:
                  type: string
                description:
                  type: string
                banner_image:
                  type: string
                  format: binary
      responses:
        '200':
          description: Course updated successfully

    delete:
      tags:
        - courses
      summary: Delete course
      operationId: deleteCourse
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Course deleted successfully

  /thread:
    post:
      tags:
        - threads
      summary: Create a new thread
      operationId: createThread
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                title:
                  type: string
                description:
                  type: string
                course_id:
                  type: integer
                semester_id:
                  type: integer
                syllabus:
                  type: string
                  format: binary
      responses:
        '201':
          description: Thread created successfully

    get:
      tags:
        - threads
      summary: Get all threads
      operationId: getAllThreads
      parameters:
        - name: course_id
          in: query
          schema:
            type: integer
        - name: semester_id
          in: query
          schema:
            type: integer
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: List of threads retrieved successfully

  /thread/{id}:
    get:
      tags:
        - threads
      summary: Get thread by ID
      operationId: getThreadById
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Thread retrieved successfully

    put:
      tags:
        - threads
      summary: Update thread
      operationId: updateThread
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                title:
                  type: string
                description:
                  type: string
                syllabus:
                  type: string
                  format: binary
      responses:
        '200':
          description: Thread updated successfully

    delete:
      tags:
        - threads
      summary: Delete thread
      operationId: deleteThread
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Thread deleted successfully

  /thread/{id}/schedule:
    post:
      tags:
        - threads
      summary: Create thread schedule
      operationId: createThreadSchedule
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                day_of_week:
                  type: integer
                start_time:
                  type: string
                  format: time
                end_time:
                  type: string
                  format: time
      responses:
        '201':
          description: Schedule created successfully

  /thread/{id}/register:
    post:
      tags:
        - threads
      summary: Register user to thread
      operationId: registerUserToThread
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id:
                  type: integer
      responses:
        '200':
          description: User registered successfully

  /thread/{id}/register-bulk:
    post:
      tags:
        - threads
      summary: Register multiple users to thread
      operationId: registerUsersToThread
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_ids:
                  type: array
                  items:
                    type: integer
      responses:
        '200':
          description: Users registered successfully

  /thread/{id}/unregister:
    post:
      tags:
        - threads
      summary: Unregister users from thread
      operationId: unregisterUsersFromThread
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                user_ids:
                  type: array
                  items:
                    type: integer
      responses:
        '200':
          description: Users unregistered successfully
  
  /threads/{id}/weeks/homework:
    get:
      tags:
        - threads
      summary: List weeks with homework information for a specific user in a thread
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Thread ID
        - name: user_id
          in: query
          required: true
          schema:
            type: integer
          description: User ID
      responses:
        '200':
          description: Weeks with homework retrieved successfully
        '400':
          description: Invalid thread_id or user_id

  /weeks:
    post:
      tags:
        - weeks
      summary: Create a new week
      operationId: createWeek
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                thread_id:
                  type: integer
                week_number:
                  type: integer
                topic:
                  type: string
                description:
                  type: string
      responses:
        '201':
          description: Week created successfully

    get:
      tags:
        - weeks
      summary: Get all weeks
      operationId: getAllWeeks
      parameters:
        - name: thread_id
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: List of weeks retrieved successfully

  /weeks/{id}:
    get:
      tags:
        - weeks
      summary: Get week by ID
      operationId: getWeekById
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Week retrieved successfully

    put:
      tags:
        - weeks
      summary: Update week
      operationId: updateWeek
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                topic:
                  type: string
                description:
                  type: string
      responses:
        '200':
          description: Week updated successfully

    delete:
      tags:
        - weeks
      summary: Delete week
      operationId: deleteWeek
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Week deleted successfully

  /assignments/groups:
    post:
      tags:
        - assignments
      summary: Create assignment group
      operationId: createAssignmentGroup
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                thread_id:
                  type: integer
                name:
                  type: string
                group_type:
                  type: string
                weight:
                  type: number
                  format: float
      responses:
        '201':
          description: Assignment group created successfully

  /assignments/groups/thread/{threadId}:
    get:
      tags:
        - assignments
      summary: List assignment groups for thread
      operationId: listAssignmentGroupsForThread
      parameters:
        - name: threadId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Assignment groups retrieved successfully

  /weeks/{weekId}/assignments:
    post:
      tags:
        - assignments
      summary: Create assignment for week
      operationId: createAssignment
      parameters:
        - name: weekId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                description:
                  type: string
                due_date:
                  type: string
                  format: date-time
                max_points:
                  type: number
                group_id:
                  type: integer
      responses:
        '201':
          description: Assignment created successfully

    get:
      tags:
        - assignments
      summary: List assignments for week
      operationId: listAssignmentsForWeek
      parameters:
        - name: weekId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Assignments retrieved successfully

  /assignments/{id}:
    get:
      tags:
        - assignments
      summary: Get assignment by ID
      operationId: getAssignmentById
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Assignment retrieved successfully

    put:
      tags:
        - assignments
      summary: Update assignment
      operationId: updateAssignment
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                title:
                  type: string
                description:
                  type: string
                due_date:
                  type: string
                  format: date-time
                max_points:
                  type: number
                group_id:
                  type: integer
      responses:
        '200':
          description: Assignment updated successfully

    delete:
      tags:
        - assignments
      summary: Delete assignment
      operationId: deleteAssignment
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Assignment deleted successfully

  /assignments/{id}/submit:
    post:
      tags:
        - assignments
      summary: Submit assignment
      operationId: submitAssignment
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                comment:
                  type: string
      responses:
        '200':
          description: Assignment submitted successfully

  /attendance:
    post:
      tags:
        - attendance
      summary: Create attendance record
      operationId: createAttendance
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                thread_id:
                  type: integer
                user_id:
                  type: integer
                date:
                  type: string
                  format: date
                status:
                  type: string
                  enum: [present, absent, late]
      responses:
        '201':
          description: Attendance record created successfully

    get:
      tags:
        - attendance
      summary: List attendance records
      operationId: listAttendance
      parameters:
        - name: thread_id
          in: query
          schema:
            type: integer
        - name: user_id
          in: query
          schema:
            type: integer
        - name: date
          in: query
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Attendance records retrieved successfully

  /semester:
    post:
      tags:
        - semesters
      summary: Create a new semester
      operationId: createSemester
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                start_date:
                  type: string
                  format: date
                end_date:
                  type: string
                  format: date
      responses:
        '201':
          description: Semester created successfully

    get:
      tags:
        - semesters
      summary: Get all semesters
      operationId: getAllSemesters
      responses:
        '200':
          description: List of semesters retrieved successfully

  /semester/{id}:
    get:
      tags:
        - semesters
      summary: Get semester by ID
      operationId: getSemesterById
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Semester retrieved successfully

    put:
      tags:
        - semesters
      summary: Update semester
      operationId: updateSemester
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                start_date:
                  type: string
                  format: date
                end_date:
                  type: string
                  format: date
      responses:
        '200':
          description: Semester updated successfully

    delete:
      tags:
        - semesters
      summary: Delete semester
      operationId: deleteSemester
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Semester deleted successfully

  /threads/{thread_id}/groupmates:
    get:
      tags:
        - threads
      summary: List all students in a thread with their detailed information
      parameters:
        - name: thread_id
          in: path
          required: true
          schema:
            type: integer
          description: Thread ID
      responses:
        '200':
          description: List of groupmates retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  thread_id:
                    type: integer
                  members:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                        name:
                          type: string
                        surname:
                          type: string
                        email:
                          type: string
                        role:
                          type: string
        '400':
          description: Invalid thread_id

  /users/{id}/role:
    put:
      tags:
        - users
      summary: Update user role
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                role:
                  $ref: '#/components/schemas/UserRole'
      responses:
        '200':
          description: Role updated successfully
        '403':
          description: Insufficient permissions
        '404':
          description: User not found

components:
  schemas:
    UserRole:
      type: string
      enum:
        - student
        - teacher
        - moderator
        - admin
    
    User:
      type: object
      properties:
        id:
          type: integer
        keycloak_id:
          type: string
          format: uuid
        name:
          type: string
        surname:
          type: string
        email:
          type: string
          format: email
        role:
          $ref: '#/components/schemas/UserRole'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        version:
          type: integer

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - bearerAuth: []
