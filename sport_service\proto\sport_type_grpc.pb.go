// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: proto/sport_type.proto

package sportpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SportTypeService_CreateSportType_FullMethodName            = "/sportpb.SportTypeService/CreateSportType"
	SportTypeService_GetSportType_FullMethodName               = "/sportpb.SportTypeService/GetSportType"
	SportTypeService_UpdateSportType_FullMethodName            = "/sportpb.SportTypeService/UpdateSportType"
	SportTypeService_DeleteSportType_FullMethodName            = "/sportpb.SportTypeService/DeleteSportType"
	SportTypeService_ListSportTypes_FullMethodName             = "/sportpb.SportTypeService/ListSportTypes"
	SportTypeService_AssignTeacherToSportType_FullMethodName   = "/sportpb.SportTypeService/AssignTeacherToSportType"
	SportTypeService_RemoveTeacherFromSportType_FullMethodName = "/sportpb.SportTypeService/RemoveTeacherFromSportType"
	SportTypeService_ListTeachersForSportType_FullMethodName   = "/sportpb.SportTypeService/ListTeachersForSportType"
	SportTypeService_ListSportTypesForTeacher_FullMethodName   = "/sportpb.SportTypeService/ListSportTypesForTeacher"
)

// SportTypeServiceClient is the client API for SportTypeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SportTypeServiceClient interface {
	// Create a new sport type
	CreateSportType(ctx context.Context, in *CreateSportTypeRequest, opts ...grpc.CallOption) (*SportTypeResponse, error)
	// Get a sport type by ID
	GetSportType(ctx context.Context, in *GetSportTypeRequest, opts ...grpc.CallOption) (*SportTypeResponse, error)
	// Update an existing sport type
	UpdateSportType(ctx context.Context, in *UpdateSportTypeRequest, opts ...grpc.CallOption) (*SportTypeResponse, error)
	// Delete a sport type
	DeleteSportType(ctx context.Context, in *DeleteSportTypeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// List sport types with filtering
	ListSportTypes(ctx context.Context, in *ListSportTypesRequest, opts ...grpc.CallOption) (*ListSportTypesResponse, error)
	// Assign a teacher to a sport type
	AssignTeacherToSportType(ctx context.Context, in *AssignTeacherRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Remove a teacher from a sport type
	RemoveTeacherFromSportType(ctx context.Context, in *RemoveTeacherRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// List teachers for a sport type
	ListTeachersForSportType(ctx context.Context, in *ListTeachersForSportTypeRequest, opts ...grpc.CallOption) (*ListTeachersResponse, error)
	// List sport types for a teacher
	ListSportTypesForTeacher(ctx context.Context, in *ListSportTypesForTeacherRequest, opts ...grpc.CallOption) (*ListSportTypesResponse, error)
}

type sportTypeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSportTypeServiceClient(cc grpc.ClientConnInterface) SportTypeServiceClient {
	return &sportTypeServiceClient{cc}
}

func (c *sportTypeServiceClient) CreateSportType(ctx context.Context, in *CreateSportTypeRequest, opts ...grpc.CallOption) (*SportTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SportTypeResponse)
	err := c.cc.Invoke(ctx, SportTypeService_CreateSportType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportTypeServiceClient) GetSportType(ctx context.Context, in *GetSportTypeRequest, opts ...grpc.CallOption) (*SportTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SportTypeResponse)
	err := c.cc.Invoke(ctx, SportTypeService_GetSportType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportTypeServiceClient) UpdateSportType(ctx context.Context, in *UpdateSportTypeRequest, opts ...grpc.CallOption) (*SportTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SportTypeResponse)
	err := c.cc.Invoke(ctx, SportTypeService_UpdateSportType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportTypeServiceClient) DeleteSportType(ctx context.Context, in *DeleteSportTypeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SportTypeService_DeleteSportType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportTypeServiceClient) ListSportTypes(ctx context.Context, in *ListSportTypesRequest, opts ...grpc.CallOption) (*ListSportTypesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSportTypesResponse)
	err := c.cc.Invoke(ctx, SportTypeService_ListSportTypes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportTypeServiceClient) AssignTeacherToSportType(ctx context.Context, in *AssignTeacherRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SportTypeService_AssignTeacherToSportType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportTypeServiceClient) RemoveTeacherFromSportType(ctx context.Context, in *RemoveTeacherRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SportTypeService_RemoveTeacherFromSportType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportTypeServiceClient) ListTeachersForSportType(ctx context.Context, in *ListTeachersForSportTypeRequest, opts ...grpc.CallOption) (*ListTeachersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTeachersResponse)
	err := c.cc.Invoke(ctx, SportTypeService_ListTeachersForSportType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sportTypeServiceClient) ListSportTypesForTeacher(ctx context.Context, in *ListSportTypesForTeacherRequest, opts ...grpc.CallOption) (*ListSportTypesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSportTypesResponse)
	err := c.cc.Invoke(ctx, SportTypeService_ListSportTypesForTeacher_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SportTypeServiceServer is the server API for SportTypeService service.
// All implementations must embed UnimplementedSportTypeServiceServer
// for forward compatibility.
type SportTypeServiceServer interface {
	// Create a new sport type
	CreateSportType(context.Context, *CreateSportTypeRequest) (*SportTypeResponse, error)
	// Get a sport type by ID
	GetSportType(context.Context, *GetSportTypeRequest) (*SportTypeResponse, error)
	// Update an existing sport type
	UpdateSportType(context.Context, *UpdateSportTypeRequest) (*SportTypeResponse, error)
	// Delete a sport type
	DeleteSportType(context.Context, *DeleteSportTypeRequest) (*emptypb.Empty, error)
	// List sport types with filtering
	ListSportTypes(context.Context, *ListSportTypesRequest) (*ListSportTypesResponse, error)
	// Assign a teacher to a sport type
	AssignTeacherToSportType(context.Context, *AssignTeacherRequest) (*emptypb.Empty, error)
	// Remove a teacher from a sport type
	RemoveTeacherFromSportType(context.Context, *RemoveTeacherRequest) (*emptypb.Empty, error)
	// List teachers for a sport type
	ListTeachersForSportType(context.Context, *ListTeachersForSportTypeRequest) (*ListTeachersResponse, error)
	// List sport types for a teacher
	ListSportTypesForTeacher(context.Context, *ListSportTypesForTeacherRequest) (*ListSportTypesResponse, error)
	mustEmbedUnimplementedSportTypeServiceServer()
}

// UnimplementedSportTypeServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSportTypeServiceServer struct{}

func (UnimplementedSportTypeServiceServer) CreateSportType(context.Context, *CreateSportTypeRequest) (*SportTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSportType not implemented")
}
func (UnimplementedSportTypeServiceServer) GetSportType(context.Context, *GetSportTypeRequest) (*SportTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSportType not implemented")
}
func (UnimplementedSportTypeServiceServer) UpdateSportType(context.Context, *UpdateSportTypeRequest) (*SportTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSportType not implemented")
}
func (UnimplementedSportTypeServiceServer) DeleteSportType(context.Context, *DeleteSportTypeRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSportType not implemented")
}
func (UnimplementedSportTypeServiceServer) ListSportTypes(context.Context, *ListSportTypesRequest) (*ListSportTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSportTypes not implemented")
}
func (UnimplementedSportTypeServiceServer) AssignTeacherToSportType(context.Context, *AssignTeacherRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AssignTeacherToSportType not implemented")
}
func (UnimplementedSportTypeServiceServer) RemoveTeacherFromSportType(context.Context, *RemoveTeacherRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveTeacherFromSportType not implemented")
}
func (UnimplementedSportTypeServiceServer) ListTeachersForSportType(context.Context, *ListTeachersForSportTypeRequest) (*ListTeachersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTeachersForSportType not implemented")
}
func (UnimplementedSportTypeServiceServer) ListSportTypesForTeacher(context.Context, *ListSportTypesForTeacherRequest) (*ListSportTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSportTypesForTeacher not implemented")
}
func (UnimplementedSportTypeServiceServer) mustEmbedUnimplementedSportTypeServiceServer() {}
func (UnimplementedSportTypeServiceServer) testEmbeddedByValue()                          {}

// UnsafeSportTypeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SportTypeServiceServer will
// result in compilation errors.
type UnsafeSportTypeServiceServer interface {
	mustEmbedUnimplementedSportTypeServiceServer()
}

func RegisterSportTypeServiceServer(s grpc.ServiceRegistrar, srv SportTypeServiceServer) {
	// If the following call pancis, it indicates UnimplementedSportTypeServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SportTypeService_ServiceDesc, srv)
}

func _SportTypeService_CreateSportType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSportTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportTypeServiceServer).CreateSportType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportTypeService_CreateSportType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportTypeServiceServer).CreateSportType(ctx, req.(*CreateSportTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportTypeService_GetSportType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSportTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportTypeServiceServer).GetSportType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportTypeService_GetSportType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportTypeServiceServer).GetSportType(ctx, req.(*GetSportTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportTypeService_UpdateSportType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSportTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportTypeServiceServer).UpdateSportType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportTypeService_UpdateSportType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportTypeServiceServer).UpdateSportType(ctx, req.(*UpdateSportTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportTypeService_DeleteSportType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSportTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportTypeServiceServer).DeleteSportType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportTypeService_DeleteSportType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportTypeServiceServer).DeleteSportType(ctx, req.(*DeleteSportTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportTypeService_ListSportTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSportTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportTypeServiceServer).ListSportTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportTypeService_ListSportTypes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportTypeServiceServer).ListSportTypes(ctx, req.(*ListSportTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportTypeService_AssignTeacherToSportType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignTeacherRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportTypeServiceServer).AssignTeacherToSportType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportTypeService_AssignTeacherToSportType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportTypeServiceServer).AssignTeacherToSportType(ctx, req.(*AssignTeacherRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportTypeService_RemoveTeacherFromSportType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveTeacherRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportTypeServiceServer).RemoveTeacherFromSportType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportTypeService_RemoveTeacherFromSportType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportTypeServiceServer).RemoveTeacherFromSportType(ctx, req.(*RemoveTeacherRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportTypeService_ListTeachersForSportType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTeachersForSportTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportTypeServiceServer).ListTeachersForSportType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportTypeService_ListTeachersForSportType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportTypeServiceServer).ListTeachersForSportType(ctx, req.(*ListTeachersForSportTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SportTypeService_ListSportTypesForTeacher_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSportTypesForTeacherRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SportTypeServiceServer).ListSportTypesForTeacher(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SportTypeService_ListSportTypesForTeacher_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SportTypeServiceServer).ListSportTypesForTeacher(ctx, req.(*ListSportTypesForTeacherRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SportTypeService_ServiceDesc is the grpc.ServiceDesc for SportTypeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SportTypeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sportpb.SportTypeService",
	HandlerType: (*SportTypeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSportType",
			Handler:    _SportTypeService_CreateSportType_Handler,
		},
		{
			MethodName: "GetSportType",
			Handler:    _SportTypeService_GetSportType_Handler,
		},
		{
			MethodName: "UpdateSportType",
			Handler:    _SportTypeService_UpdateSportType_Handler,
		},
		{
			MethodName: "DeleteSportType",
			Handler:    _SportTypeService_DeleteSportType_Handler,
		},
		{
			MethodName: "ListSportTypes",
			Handler:    _SportTypeService_ListSportTypes_Handler,
		},
		{
			MethodName: "AssignTeacherToSportType",
			Handler:    _SportTypeService_AssignTeacherToSportType_Handler,
		},
		{
			MethodName: "RemoveTeacherFromSportType",
			Handler:    _SportTypeService_RemoveTeacherFromSportType_Handler,
		},
		{
			MethodName: "ListTeachersForSportType",
			Handler:    _SportTypeService_ListTeachersForSportType_Handler,
		},
		{
			MethodName: "ListSportTypesForTeacher",
			Handler:    _SportTypeService_ListSportTypesForTeacher_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/sport_type.proto",
}
