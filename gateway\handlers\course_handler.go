package handlers

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
)

// CourseHandler обрабатывает HTTP-запросы, связанные с курсами
type CourseHandler struct {
	RabbitLogPublisher clients.LogPublisher
	CourseService      *clients.CourseClient
	StorageService     *clients.StorageClient
}

// Update структура, которая будет принята из тела запроса при обновлении курса
type updateCourseBody struct {
	Title          string `json:"title" binding:"required"`
	Description    string `json:"description"`
	BannerImageUrl string `json:"banner_image_url"`
}

// CourseWithImageRequest represents a request to create a course with an image
type CourseWithImageRequest struct {
	Title       string `json:"title" binding:"required"`
	Description string `json:"description"`
}

func (h *CourseHandler) Create(c *gin.Context) {
	var body struct {
		Title                 string  `json:"title" binding:"required"`
		Description           string  `json:"description"`
		PrerequisiteCourseIDs []int64 `json:"prerequisite_course_ids"`
	}

	if err := c.ShouldBindJSON(&body); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// Создаём курс
	resp, err := h.CourseService.CreateCourse(
		c.Request.Context(),
		body.Title,
		body.Description,
		body.PrerequisiteCourseIDs,
	)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error while creating course: %v", err),
			"course",
			map[string]any{"title": body.Title, "description": body.Description, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":   "Course created successfully",
		"course_id": resp.GetId(),
	})
}

// GetAll возвращает список всех курсов (GET /course)
func (h *CourseHandler) GetAll(c *gin.Context) {
	resp, err := h.CourseService.GetAllCourses(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, resp)
}

// GetOne возвращает курс по ID (GET /course/:id)
func (h *CourseHandler) GetOne(c *gin.Context) {
	idParam := c.Param("id")
	courseID, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil || courseID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid course_id"})
		return
	}

	course, err := h.CourseService.GetCourseByID(c.Request.Context(), courseID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, course)
}

// Update обновляет курс по ID (PUT /course/:id)
func (h *CourseHandler) Update(c *gin.Context) {
	idParam := c.Param("id")
	courseID, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil || courseID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid course_id"})
		return
	}

	var body updateCourseBody
	if err := c.ShouldBindJSON(&body); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	resp, err := h.CourseService.UpdateCourseByID(c.Request.Context(), courseID, body.Title, body.Description, body.BannerImageUrl)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// Delete удаляет курс по ID (DELETE /course/:id)
func (h *CourseHandler) Delete(c *gin.Context) {
	idParam := c.Param("id")
	courseID, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil || courseID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid course_id"})
		return
	}

	if err := h.CourseService.DeleteCourseByID(c.Request.Context(), courseID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// UpdateWithImage updates a course with an image (PUT /course/:id/with-image)
func (h *CourseHandler) UpdateWithImage(c *gin.Context) {
	// Parse course ID
	idParam := c.Param("id")
	courseID, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil || courseID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid course_id"})
		return
	}

	// Limit request body size
	c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, 10<<20) // 10 MB

	// Parse multipart form data
	if err := c.Request.ParseMultipartForm(10 << 20); err != nil {
		if strings.Contains(err.Error(), "request body too large") {
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{"error": "file size exceeds 10MB limit"})
			return
		}
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing form data: %v", err),
			"course_update_with_image",
			map[string]any{"course_id": courseID, "error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to parse form data"})
		return
	}

	// Extract course fields
	title := c.PostForm("title")
	description := c.PostForm("description")

	// Validate required fields
	if title == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "title is required"})
		return
	}

	// Get the current course to preserve the banner URL if no new image is uploaded
	currentCourse, err := h.CourseService.GetCourseByID(c.Request.Context(), courseID)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting current course: %v", err),
			"course_update_with_image",
			map[string]any{"course_id": courseID, "error": err.Error()},
		)
		c.JSON(http.StatusNotFound, gin.H{"error": "course not found"})
		return
	}

	// Use the current banner image URL as default
	bannerImageUrl := currentCourse.BannerImageUrl

	// Process image file if provided
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		if !errors.Is(err, http.ErrMissingFile) {
			// Error other than missing file
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error while parsing image file: %v", err),
				"course_update_with_image",
				map[string]any{"course_id": courseID, "error": err.Error()},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "failed to process image file"})
			return
		}
		// No new file uploaded, keep the existing banner URL
	} else {
		defer file.Close()

		// Determine MIME type
		contentType := header.Header.Get("Content-Type")
		if contentType == "" {
			contentType = "application/octet-stream"
		}

		// Allow only image file types
		allowedTypes := []string{"image/jpeg", "image/png", "image/gif", "image/webp"}
		isAllowed := false
		for _, t := range allowedTypes {
			if contentType == t {
				isAllowed = true
				break
			}
		}
		if !isAllowed {
			c.JSON(http.StatusUnsupportedMediaType, gin.H{"error": "only JPEG, PNG, GIF, and WEBP images are allowed"})
			return
		}

		// Check file size
		fileSize := header.Size
		if fileSize > 5<<20 { // 5 MB
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{"error": "image file size exceeds 5MB limit"})
			return
		}

		// Read file content
		fileData := make([]byte, fileSize)
		_, err := file.Read(fileData)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error reading image file: %v", err),
				"course_update_with_image",
				map[string]any{"course_id": courseID, "error": err.Error()},
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to read image file"})
			return
		}

		// Process filename
		originalFilename := header.Filename
		safeFilename := strings.ReplaceAll(originalFilename, " ", "_")
		objectName := fmt.Sprintf("%d_%s", time.Now().UnixNano(), safeFilename)

		// Upload file to storage
		_, err = h.StorageService.UploadFile(
			c.Request.Context(),
			"course-images",
			objectName,
			contentType,
			fileData,
		)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error uploading image file: %v", err),
				"course_update_with_image",
				map[string]any{"course_id": courseID, "error": err.Error()},
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to upload image file"})
			return
		}

		// Update the banner image URL
		bannerImageUrl = fmt.Sprintf("course-images/%s", objectName)

		// TODO: Consider deleting the old image file if needed
	}

	// Update the course
	resp, err := h.CourseService.UpdateCourseByID(
		c.Request.Context(),
		courseID,
		title,
		description,
		bannerImageUrl,
	)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error updating course: %v", err),
			"course_update_with_image",
			map[string]any{
				"course_id":   courseID,
				"title":       title,
				"description": description,
				"error":       err.Error(),
			},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update course"})
		return
	}

	// Log successful update
	h.RabbitLogPublisher.PublishLog(
		"info",
		fmt.Sprintf("Course with image updated successfully: course_id=%d", resp.Id),
		"course_update_with_image",
		map[string]any{
			"course_id": resp.Id,
			"image_url": bannerImageUrl,
		},
	)

	// Return success response
	c.JSON(http.StatusOK, gin.H{
		"message":   "Course with image updated successfully",
		"course_id": resp.Id,
		"image_url": bannerImageUrl,
	})
}

// CreateWithImage creates a new course with an image (POST /course/with-image)
func (h *CourseHandler) CreateWithImage(c *gin.Context) {
	// Limit request body size
	c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, 10<<20) // 10 MB

	// Parse multipart form data
	if err := c.Request.ParseMultipartForm(10 << 20); err != nil {
		if strings.Contains(err.Error(), "request body too large") {
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{"error": "file size exceeds 10MB limit"})
			return
		}
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing form data: %v", err),
			"course_with_image",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to parse form data"})
		return
	}

	// Extract course fields
	title := c.PostForm("title")
	description := c.PostForm("description")
	prerequisiteCourseIDsStr := c.PostForm("prerequisite_course_ids")

	// Validate required fields
	if title == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "title is required"})
		return
	}

	// Parse prerequisite course IDs
	var prerequisiteCourseIDs []int64
	if prerequisiteCourseIDsStr != "" {
		// Remove brackets and split by comma
		prerequisiteCourseIDsStr = strings.Trim(prerequisiteCourseIDsStr, "[]")
		if prerequisiteCourseIDsStr != "" {
			ids := strings.Split(prerequisiteCourseIDsStr, ",")
			for _, idStr := range ids {
				id, err := strconv.ParseInt(strings.TrimSpace(idStr), 10, 64)
				if err != nil {
					c.JSON(http.StatusBadRequest, gin.H{"error": "invalid prerequisite course IDs format"})
					return
				}
				prerequisiteCourseIDs = append(prerequisiteCourseIDs, id)
			}
		}
	}

	// Process image file
	var bannerImageUrl string
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		if errors.Is(err, http.ErrMissingFile) {
			// No file uploaded, continue without it
		} else {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error while parsing image file: %v", err),
				"course_with_image",
				map[string]any{"title": title, "error": err.Error()},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "failed to process image file"})
			return
		}
	} else {
		defer file.Close()

		// Determine MIME type
		contentType := header.Header.Get("Content-Type")
		if contentType == "" {
			contentType = "application/octet-stream"
		}

		// Allow only image file types
		allowedTypes := []string{"image/jpeg", "image/png", "image/gif", "image/webp"}
		isAllowed := false
		for _, t := range allowedTypes {
			if contentType == t {
				isAllowed = true
				break
			}
		}
		if !isAllowed {
			c.JSON(http.StatusUnsupportedMediaType, gin.H{"error": "only JPEG, PNG, GIF, and WEBP images are allowed"})
			return
		}

		// Check file size
		fileSize := header.Size
		if fileSize > 5<<20 { // 5 MB
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{"error": "image file size exceeds 5MB limit"})
			return
		}

		// Read file content
		fileData := make([]byte, fileSize)
		_, err := file.Read(fileData)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error reading image file: %v", err),
				"course_with_image",
				map[string]any{"title": title, "error": err.Error()},
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to read image file"})
			return
		}

		// Process filename
		originalFilename := header.Filename
		safeFilename := strings.ReplaceAll(originalFilename, " ", "_")
		objectName := fmt.Sprintf("%d_%s", time.Now().UnixNano(), safeFilename)

		// Upload file to storage
		_, err = h.StorageService.UploadFile(
			c.Request.Context(),
			"course-images",
			objectName,
			contentType,
			fileData,
		)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error uploading image file: %v", err),
				"course_with_image",
				map[string]any{"title": title, "error": err.Error()},
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to upload image file"})
			return
		}

		bannerImageUrl = fmt.Sprintf("course-images/%s", objectName)
	}

	// Create the course
	resp, err := h.CourseService.CreateCourse(
		c.Request.Context(),
		title,
		description,
		prerequisiteCourseIDs,
	)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error creating course: %v", err),
			"course_with_image",
			map[string]any{
				"title":       title,
				"description": description,
				"error":       err.Error(),
			},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create course"})
		return
	}

	// Log successful creation
	h.RabbitLogPublisher.PublishLog(
		"info",
		fmt.Sprintf("Course with image created successfully: course_id=%d", resp.Id),
		"course_with_image",
		map[string]any{
			"course_id": resp.Id,
			"image_url": bannerImageUrl,
		},
	)

	// Return success response
	c.JSON(http.StatusCreated, gin.H{
		"message":   "Course with image created successfully",
		"course_id": resp.Id,
		"image_url": bannerImageUrl,
	})
}
