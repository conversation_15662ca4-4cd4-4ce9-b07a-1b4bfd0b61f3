# AWS S3 Storage Setup

This document explains how to set up and use AWS S3 storage for the Edunite project.

## Configuration

The project has been updated to use AWS S3 instead of MinIO for file storage. The AWS credentials are stored in the `.env` file in the root directory of the project.

### Environment Variables

The following environment variables are used for AWS S3 configuration:

- `AWS_ACCESS_KEY`: Your AWS access key ID
- `AWS_SECRET_KEY`: Your AWS secret access key
- `AWS_REGION`: The AWS region where your S3 buckets are located (default: `eu-north-1`)
- `STORAGE_TYPE`: Set to `s3` to use AWS S3 storage (or `minio` to use MinIO)
- `ENVIRONMENT`: Set to `production` for production environment

## Running the Storage Service

### Using Docker Compose

The docker-compose.yaml file has been updated to include the AWS S3 configuration. To run the storage service with AWS S3, use the following command:

```bash
docker-compose up -d storage_service
```

### Running Locally

To run the storage service locally, you can use the provided scripts:

#### Linux/macOS

```bash
chmod +x run_storage_service.sh
./run_storage_service.sh
```

#### Windows

```bash
run_storage_service.bat
```

## Default Buckets

The following default buckets are created automatically when the storage service starts:

- `assignments`: For assignment files
- `profiles`: For user profile pictures
- `courses`: For course materials
- `submissions`: For assignment submissions
- `attachments`: For general attachments

## Accessing Files

Files stored in AWS S3 can be accessed using the following URL format:

```
https://{bucket-name}.s3.{region}.amazonaws.com/{object-name}
```

For example:
```
https://profiles.s3.eu-north-1.amazonaws.com/user123.jpg
```

## Troubleshooting

If you encounter any issues with AWS S3 storage, check the following:

1. Verify that the AWS credentials in the `.env` file are correct
2. Ensure that the AWS region is set correctly
3. Check that the storage service is running and connected to AWS S3
4. Verify that the buckets exist in your AWS S3 account
5. Check the storage service logs for any error messages

## Switching Back to MinIO

If you need to switch back to using MinIO for local development, set the `STORAGE_TYPE` environment variable to `minio` in the `.env` file:

```
STORAGE_TYPE=minio
ENVIRONMENT=development
```

Then restart the storage service.
