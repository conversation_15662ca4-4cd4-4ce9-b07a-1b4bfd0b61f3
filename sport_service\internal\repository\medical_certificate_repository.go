package repository

import (
	"context"
	"time"
	
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

// MedicalCertificateRepository defines the interface for medical certificate data access
type MedicalCertificateRepository interface {
	// Create creates a new medical certificate
	Create(ctx context.Context, certificate *domain.MedicalCertificate) error
	
	// GetByID retrieves a medical certificate by ID
	GetByID(ctx context.Context, id int64) (*domain.MedicalCertificate, error)
	
	// Update updates an existing medical certificate
	Update(ctx context.Context, certificate *domain.MedicalCertificate) error
	
	// Delete deletes a medical certificate by ID
	Delete(ctx context.Context, id int64) error
	
	// List retrieves medical certificates based on filters
	List(ctx context.Context, filter domain.MedicalCertificateFilter) ([]*domain.MedicalCertificate, error)
	
	// Count counts medical certificates based on filters
	Count(ctx context.Context, filter domain.MedicalCertificateFilter) (int, error)
	
	// GetLatestByUserID retrieves the latest medical certificate for a user
	GetLatestByUserID(ctx context.Context, userID int64) (*domain.MedicalCertificate, error)
	
	// HasValidCertificate checks if a user has a valid medical certificate at a specific time
	HasValidCertificate(ctx context.Context, userID int64, at time.Time) (bool, error)
	
	// ListPendingCertificates retrieves pending medical certificates
	ListPendingCertificates(ctx context.Context, page, pageSize int) ([]*domain.MedicalCertificate, error)
	
	// CountPendingCertificates counts pending medical certificates
	CountPendingCertificates(ctx context.Context) (int, error)
	
	// ApproveCertificate approves a medical certificate
	ApproveCertificate(ctx context.Context, id, reviewerID int64, validUntil time.Time) error
	
	// RejectCertificate rejects a medical certificate
	RejectCertificate(ctx context.Context, id, reviewerID int64, reason string) error
}
