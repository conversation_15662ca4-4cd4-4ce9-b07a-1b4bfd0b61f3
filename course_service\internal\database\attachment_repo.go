package database

import (
	"context"
	"errors"
	"time"

	"github.com/jackc/pgx/v4/pgxpool"
)

var (
	ErrAttachmentNotFound = errors.New("assignment attachment not found")
)

// AssignmentAttachment модель для таблицы assignment_attachments
type AssignmentAttachment struct {
	ID           int64     `json:"id"`
	AssignmentID int64     `json:"assignment_id"`
	FileURL      string    `json:"file_url"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// AssignmentAttachmentRepository интерфейс для CRUD операций
type AssignmentAttachmentRepository interface {
	CreateAttachment(ctx context.Context, attachment *AssignmentAttachment) error
	GetAttachmentsByAssignmentID(ctx context.Context, assignmentID int64) ([]*AssignmentAttachment, error)
	DeleteAttachment(ctx context.Context, attachmentID int64) error
}

type assignmentAttachmentRepo struct {
	db *pgxpool.Pool
}

// NewAssignmentAttachmentRepository - конструктор
func NewAssignmentAttachmentRepository(db *pgxpool.Pool) AssignmentAttachmentRepository {
	return &assignmentAttachmentRepo{db: db}
}

func (r *assignmentAttachmentRepo) CreateAttachment(ctx context.Context, attachment *AssignmentAttachment) error {
	query := `
		INSERT INTO assignment_attachments (assignment_id, file_url)
		VALUES ($1, $2)
		RETURNING id, created_at, updated_at
	`
	row := r.db.QueryRow(ctx, query, attachment.AssignmentID, attachment.FileURL)
	if err := row.Scan(&attachment.ID, &attachment.CreatedAt, &attachment.UpdatedAt); err != nil {
		return err
	}
	return nil
}

func (r *assignmentAttachmentRepo) GetAttachmentsByAssignmentID(ctx context.Context, assignmentID int64) ([]*AssignmentAttachment, error) {
	const q = `
	SELECT id, assignment_id, file_url, created_at, updated_at
	  FROM assignment_attachments
	 WHERE assignment_id=$1
	 ORDER BY id`
	rows, err := r.db.Query(ctx, q, assignmentID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var list []*AssignmentAttachment
	for rows.Next() {
		var att AssignmentAttachment
		if err := rows.Scan(&att.ID, &att.AssignmentID, &att.FileURL, &att.CreatedAt, &att.UpdatedAt); err != nil {
			return nil, err
		}
		list = append(list, &att)
	}
	return list, rows.Err()
}

func (r *assignmentAttachmentRepo) DeleteAttachment(ctx context.Context, attachmentID int64) error {
	cmd, err := r.db.Exec(ctx, `DELETE FROM assignment_attachments WHERE id=$1`, attachmentID)
	if err != nil {
		return err
	}
	if cmd.RowsAffected() == 0 {
		return ErrAttachmentNotFound
	}
	return nil
}
