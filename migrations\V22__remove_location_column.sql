-- Remove location column from thread_schedules table
ALTER TABLE thread_schedules DROP COLUMN IF EXISTS location;

-- Drop the trigger that syncs location with location_id
DROP TRIGGER IF EXISTS trg_sync_location_name ON thread_schedules;

-- Drop the function used by the trigger
DROP FUNCTION IF EXISTS sync_location_name();

-- Update NULL location_id values to 0 (default)
UPDATE thread_schedules SET location_id = 0 WHERE location_id IS NULL;

-- Add NOT NULL constraint to location_id with default value 0
ALTER TABLE thread_schedules ALTER COLUMN location_id SET DEFAULT 0;
ALTER TABLE thread_schedules ALTER COLUMN location_id SET NOT NULL;
