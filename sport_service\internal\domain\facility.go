package domain

import (
	"time"
)

// Facility represents a sports facility or class type
type Facility struct {
	ID                int64     `json:"id"`
	Title             string    `json:"title"`
	Description       string    `json:"description"`
	MaxCapacity       int       `json:"max_capacity"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	Version           int32     `json:"version"`
}

// FacilityFilter represents filters for querying facilities
type FacilityFilter struct {
	Title           string
	Page            int
	PageSize        int
}

// Error definitions for facility operations
var (
	ErrFacilityNotFound  = Error{"facility not found"}
	ErrDuplicateFacility = Error{"facility with this title already exists"}
)
