package database

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/user_service/internal/config"
	"io/ioutil"
	"log"
	"time"
)

var db *pgxpool.Pool

func ConnectDBLegacy(cfg *config.Config) *pgxpool.Pool {
	dbURL := cfg.DatabaseURL

	var pool *pgxpool.Pool
	var err error

	for i := 0; i < 5; i++ {
		pool, err = pgxpool.Connect(context.Background(), dbURL)
		if err == nil {
			log.Println("Connected to database successfully.")
			break
		}

		log.Printf("Failed to connect to database: %v. Retrying in 2 seconds...\n", err)
		time.Sleep(2 * time.Second)
	}

	if err != nil {
		log.Fatalf("Unable to connect to database after retries: %v\n", err)
	}

	db = pool
	return db
}

// ConnectDBCockroach connects to CockroachDB without certificates
func ConnectDBCockroach(cfg *config.Config) *pgxpool.Pool {
	// Формирование строки подключения для CockroachDB без сертификатов
	connstring := fmt.Sprintf(
		"host=%s port=%s dbname=%s user=%s password=%s sslmode=require",
		cfg.Database.Host, cfg.Database.Port, cfg.Database.Name, cfg.Database.User, cfg.Database.Password)

	// Используем pgxpool.ParseConfig для получения конфигурации пула
	poolConfig, err := pgxpool.ParseConfig(connstring)
	if err != nil {
		log.Fatalf("Unable to parse pool config: %v", err)
	}

	// Реализация повторных попыток подключения
	var pool *pgxpool.Pool
	for i := 0; i < 5; i++ {
		pool, err = pgxpool.ConnectConfig(context.Background(), poolConfig)
		if err == nil {
			log.Println("Connected to CockroachDB successfully.")
			break
		}
		log.Printf("Failed to connect to CockroachDB: %v. Retrying in 2 seconds...\n", err)
		time.Sleep(2 * time.Second)
	}

	if err != nil {
		log.Fatalf("Unable to connect to CockroachDB after retries: %v\n", err)
	}

	db = pool
	return db
}

func ConnectDB(cfg *config.Config) *pgxpool.Pool {
	// Чтение CA-файла и создание пула сертификатов
	rootCertPool := x509.NewCertPool()
	pem, err := ioutil.ReadFile(cfg.Database.CA)
	if err != nil {
		log.Fatalf("Unable to read CA file: %v", err)
	}

	if ok := rootCertPool.AppendCertsFromPEM(pem); !ok {
		log.Fatalf("Failed to append PEM.")
	}

	// Формирование строки подключения
	connstring := fmt.Sprintf(
		"host=%s port=%s dbname=%s user=%s password=%s sslmode=verify-full target_session_attrs=read-write",
		cfg.Database.Host, cfg.Database.Port, cfg.Database.Name, cfg.Database.User, cfg.Database.Password)

	// Используем pgxpool.ParseConfig для получения конфигурации пула
	poolConfig, err := pgxpool.ParseConfig(connstring)
	if err != nil {
		log.Fatalf("Unable to parse pool config: %v", err)
	}

	// Устанавливаем TLS-конфигурацию
	poolConfig.ConnConfig.TLSConfig = &tls.Config{
		RootCAs:            rootCertPool,
		InsecureSkipVerify: true,
	}

	// Реализация повторных попыток подключения
	var pool *pgxpool.Pool
	for i := 0; i < 5; i++ {
		pool, err = pgxpool.ConnectConfig(context.Background(), poolConfig)
		if err == nil {
			log.Println("Connected to database successfully.")
			break
		}
		log.Printf("Failed to connect to database: %v. Retrying in 2 seconds...\n", err)
		time.Sleep(2 * time.Second)
	}

	if err != nil {
		log.Fatalf("Unable to connect to database after retries: %v\n", err)
	}

	db = pool
	return db
}

func GetDB() *pgxpool.Pool {
	return db
}
