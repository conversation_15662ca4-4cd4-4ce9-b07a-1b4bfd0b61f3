syntax = "proto3";

package sportpb;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

option go_package = "github.com/olzzhas/edunite-server/sport_service/pb/sportpb";

service SportTypeService {
  // Create a new sport type
  rpc CreateSportType(CreateSportTypeRequest) returns (SportTypeResponse) {}
  
  // Get a sport type by ID
  rpc GetSportType(GetSportTypeRequest) returns (SportTypeResponse) {}
  
  // Update an existing sport type
  rpc UpdateSportType(UpdateSportTypeRequest) returns (SportTypeResponse) {}
  
  // Delete a sport type
  rpc DeleteSportType(DeleteSportTypeRequest) returns (google.protobuf.Empty) {}
  
  // List sport types with filtering
  rpc ListSportTypes(ListSportTypesRequest) returns (ListSportTypesResponse) {}
  
  // Assign a teacher to a sport type
  rpc AssignTeacherToSportType(AssignTeacherRequest) returns (google.protobuf.Empty) {}
  
  // Remove a teacher from a sport type
  rpc RemoveTeacherFromSportType(RemoveTeacherRequest) returns (google.protobuf.Empty) {}
  
  // List teachers for a sport type
  rpc ListTeachersForSportType(ListTeachersForSportTypeRequest) returns (ListTeachersResponse) {}
  
  // List sport types for a teacher
  rpc ListSportTypesForTeacher(ListSportTypesForTeacherRequest) returns (ListSportTypesResponse) {}
}

// Sport type category enum
enum SportTypeCategory {
  NORMAL = 0;
  LFK = 1;
}

// Create sport type request
message CreateSportTypeRequest {
  string title = 1;
  string description = 2;
  SportTypeCategory category = 3;
  bool requires_certificate = 4;
}

// Get sport type request
message GetSportTypeRequest {
  int64 id = 1;
}

// Update sport type request
message UpdateSportTypeRequest {
  int64 id = 1;
  string title = 2;
  string description = 3;
  SportTypeCategory category = 4;
  bool requires_certificate = 5;
  int32 version = 6;
}

// Delete sport type request
message DeleteSportTypeRequest {
  int64 id = 1;
}

// List sport types request
message ListSportTypesRequest {
  string title = 1;
  SportTypeCategory category = 2;
  bool requires_certificate = 3;
  int32 page = 4;
  int32 page_size = 5;
}

// Sport type response
message SportTypeResponse {
  int64 id = 1;
  string title = 2;
  string description = 3;
  SportTypeCategory category = 4;
  bool requires_certificate = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  int32 version = 8;
}

// List sport types response
message ListSportTypesResponse {
  repeated SportTypeResponse sport_types = 1;
  int32 total = 2;
  int32 page = 3;
  int32 page_size = 4;
}

// Assign teacher request
message AssignTeacherRequest {
  int64 teacher_id = 1;
  int64 sport_type_id = 2;
  bool can_review_certificates = 3;
}

// Remove teacher request
message RemoveTeacherRequest {
  int64 teacher_id = 1;
  int64 sport_type_id = 2;
}

// List teachers for sport type request
message ListTeachersForSportTypeRequest {
  int64 sport_type_id = 1;
}

// List sport types for teacher request
message ListSportTypesForTeacherRequest {
  int64 teacher_id = 1;
}

// Teacher response
message TeacherResponse {
  int64 id = 1;
  string name = 2;
  bool can_review_certificates = 3;
}

// List teachers response
message ListTeachersResponse {
  repeated TeacherResponse teachers = 1;
}
