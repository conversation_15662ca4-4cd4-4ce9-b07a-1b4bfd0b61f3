package config

import (
	"os"
)

// Config holds all configuration for the service
type Config struct {
	DatabaseURL string
	Database    Database
	Port        string
}
type Database struct {
	CA       string
	Host     string
	Port     string
	User     string
	Password string
	Name     string
}

func LoadConfig() *Config {

	database := Database{
		CA:       os.Getenv("DATABASE_CA"),
		Host:     os.<PERSON>env("DATABASE_HOST"),
		Port:     os.Getenv("DATABASE_PORT"),
		User:     os.<PERSON>v("DATABASE_USER"),
		Password: os.Getenv("DATABASE_PASSWORD"),
		Name:     os.Getenv("DATABASE_NAME"),
	}

	return &Config{
		DatabaseURL: os.Getenv("DATABASE_URL"),
		Database:    database,
		Port:        getEnv("PORT", "50054"),
	}
}

// getEnv gets an environment variable or returns a default value
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}
