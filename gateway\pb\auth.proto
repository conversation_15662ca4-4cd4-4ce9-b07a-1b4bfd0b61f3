syntax = "proto3";

package authpb;

option go_package = "/pb";

service AuthService {
  // Register creates a new user account
  rpc Register(RegisterRequest) returns (AuthResponse) {}
  
  // Login authenticates a user and returns tokens
  rpc Login(LoginRequest) returns (AuthResponse) {}
  
  // ValidateToken checks if a token is valid
  rpc ValidateToken(ValidateTokenRequest) returns (ValidateTokenResponse) {}
  
  // RefreshToken generates a new access token using a refresh token
  rpc RefreshToken(RefreshTokenRequest) returns (AuthResponse) {}
}

// RegisterRequest contains the information needed to create a new user
message RegisterRequest {
  string username = 1;
  string password = 2;
  string email = 3;
  string name = 4;
  string surname = 5;
}

// LoginRequest contains the credentials for authentication
message LoginRequest {
  string username = 1; // Can be username or email
  string password = 2;
}

// ValidateTokenRequest contains the token to validate
message ValidateTokenRequest {
  string token = 1;
}

// ValidateTokenResponse indicates if the token is valid
message ValidateTokenResponse {
  bool valid = 1;
  string user_id = 2; // User ID extracted from the token if valid
}

// RefreshTokenRequest contains the refresh token
message RefreshTokenRequest {
  string refresh_token = 1;
}

// AuthResponse contains the authentication tokens and user information
message AuthResponse {
  string access_token = 1;
  string refresh_token = 2;
  int64 expires_in = 3;
  int64 refresh_expires_in = 4;
  UserInfo user = 5;
}

// UserInfo contains basic user information
message UserInfo {
  int64 id = 1;
  string name = 2;
  string surname = 3;
  string email = 4;
  string role = 5;
}
