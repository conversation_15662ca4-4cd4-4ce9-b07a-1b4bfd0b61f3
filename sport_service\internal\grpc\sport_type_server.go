package grpc

import (
	"context"
	"errors"
	sportpb "github.com/olzzhas/edunite-server/sport_service/proto"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
	"github.com/olzzhas/edunite-server/sport_service/internal/service"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type sportTypeServer struct {
	sportpb.UnimplementedSportTypeServiceServer
	services *service.Services
}

// NewSportTypeServer creates a new sport type gRPC server
func NewSportTypeServer(services *service.Services) *sportTypeServer {
	return &sportTypeServer{
		services: services,
	}
}

// CreateSportType creates a new sport type
func (s *sportTypeServer) CreateSportType(ctx context.Context, req *sportpb.CreateSportTypeRequest) (*sportpb.SportTypeResponse, error) {
	sportType := &domain.SportType{
		Title:               req.Title,
		Description:         req.Description,
		Category:            domain.SportTypeCategory(req.Category.String()),
		RequiresCertificate: req.RequiresCertificate,
	}

	// Create the sport type
	err := s.services.PhysicalEducation.CreateSportType(ctx, sportType)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to create sport type: %v", err)
	}

	return convertSportTypeToProto(sportType), nil
}

// GetSportType retrieves a sport type by ID
func (s *sportTypeServer) GetSportType(ctx context.Context, req *sportpb.GetSportTypeRequest) (*sportpb.SportTypeResponse, error) {
	sportType, err := s.services.PhysicalEducation.GetSportType(ctx, req.Id)
	if err != nil {
		if err == domain.ErrSportTypeNotFound {
			return nil, status.Errorf(codes.NotFound, "sport type not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to get sport type: %v", err)
	}

	return convertSportTypeToProto(sportType), nil
}

// UpdateSportType updates an existing sport type
func (s *sportTypeServer) UpdateSportType(ctx context.Context, req *sportpb.UpdateSportTypeRequest) (*sportpb.SportTypeResponse, error) {
	sportType := &domain.SportType{
		ID:                  req.Id,
		Title:               req.Title,
		Description:         req.Description,
		Category:            domain.SportTypeCategory(req.Category.String()),
		RequiresCertificate: req.RequiresCertificate,
		Version:             int32(req.Version),
	}

	// Update the sport type
	err := s.services.PhysicalEducation.UpdateSportType(ctx, sportType)
	if err != nil {
		if errors.Is(err, domain.ErrSportTypeNotFound) {
			return nil, status.Errorf(codes.NotFound, "sport type not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to update sport type: %v", err)
	}

	return convertSportTypeToProto(sportType), nil
}

// DeleteSportType deletes a sport type
func (s *sportTypeServer) DeleteSportType(ctx context.Context, req *sportpb.DeleteSportTypeRequest) (*empty.Empty, error) {
	err := s.services.PhysicalEducation.DeleteSportType(ctx, req.Id)
	if err != nil {
		if err == domain.ErrSportTypeNotFound {
			return nil, status.Errorf(codes.NotFound, "sport type not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to delete sport type: %v", err)
	}

	return &emptypb.Empty{}, nil
}

// ListSportTypes lists sport types with filtering
func (s *sportTypeServer) ListSportTypes(ctx context.Context, req *sportpb.ListSportTypesRequest) (*sportpb.ListSportTypesResponse, error) {
	filter := domain.SportTypeFilter{
		Title:    req.Title,
		Category: domain.SportTypeCategory(req.Category.String()),
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
	}

	if req.RequiresCertificate {
		requiresCertificate := req.RequiresCertificate
		filter.RequiresCertificate = &requiresCertificate
	}

	sportTypes, err := s.services.PhysicalEducation.ListSportTypes(ctx, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list sport types: %v", err)
	}

	response := &sportpb.ListSportTypesResponse{
		Total:    int32(len(sportTypes)),
		Page:     int32(filter.Page),
		PageSize: int32(filter.PageSize),
	}

	for _, sportType := range sportTypes {
		response.SportTypes = append(response.SportTypes, convertSportTypeToProto(sportType))
	}

	return response, nil
}

// AssignTeacherToSportType assigns a teacher to a sport type
func (s *sportTypeServer) AssignTeacherToSportType(ctx context.Context, req *sportpb.AssignTeacherRequest) (*empty.Empty, error) {
	teacherSportType := &domain.TeacherSportType{
		TeacherID:             req.TeacherId,
		SportTypeID:           req.SportTypeId,
		CanReviewCertificates: req.CanReviewCertificates,
	}

	err := s.services.PhysicalEducation.AssignTeacherToSportType(ctx, teacherSportType)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to assign teacher to sport type: %v", err)
	}

	return &emptypb.Empty{}, nil
}

// RemoveTeacherFromSportType removes a teacher from a sport type
func (s *sportTypeServer) RemoveTeacherFromSportType(ctx context.Context, req *sportpb.RemoveTeacherRequest) (*empty.Empty, error) {
	err := s.services.PhysicalEducation.RemoveTeacherFromSportType(ctx, req.TeacherId, req.SportTypeId)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to remove teacher from sport type: %v", err)
	}

	return &emptypb.Empty{}, nil
}

// ListTeachersForSportType lists teachers for a sport type
func (s *sportTypeServer) ListTeachersForSportType(ctx context.Context, req *sportpb.ListTeachersForSportTypeRequest) (*sportpb.ListTeachersResponse, error) {
	_, err := s.services.PhysicalEducation.ListTeachersForSportType(ctx, req.SportTypeId)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list teachers for sport type: %v", err)
	}

	response := &sportpb.ListTeachersResponse{}
	//for _, teacher := range teachers {
	//	response.Teachers = append(response.Teachers, &sportpb.TeacherResponse{
	//		Id:                    teacher.TeacherID,
	//		Name:                  "", // This would need to be fetched from the user service
	//		CanReviewCertificates: teacher.CanReviewCertificates,
	//	})
	//}

	return response, nil
}

// ListSportTypesForTeacher lists sport types for a teacher
func (s *sportTypeServer) ListSportTypesForTeacher(ctx context.Context, req *sportpb.ListSportTypesForTeacherRequest) (*sportpb.ListSportTypesResponse, error) {
	sportTypes, err := s.services.PhysicalEducation.ListSportTypesForTeacher(ctx, req.TeacherId)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list sport types for teacher: %v", err)
	}

	response := &sportpb.ListSportTypesResponse{}
	for _, sportType := range sportTypes {
		response.SportTypes = append(response.SportTypes, convertSportTypeToProto(sportType))
	}

	return response, nil
}

// Helper function to convert domain.SportType to sportpb.SportTypeResponse
func convertSportTypeToProto(sportType *domain.SportType) *sportpb.SportTypeResponse {
	var category sportpb.SportTypeCategory
	if sportType.Category == domain.SportTypeCategoryLFK {
		category = sportpb.SportTypeCategory_LFK
	} else {
		category = sportpb.SportTypeCategory_NORMAL
	}

	return &sportpb.SportTypeResponse{
		Id:                  sportType.ID,
		Title:               sportType.Title,
		Description:         sportType.Description,
		Category:            category,
		RequiresCertificate: sportType.RequiresCertificate,
		CreatedAt:           timestamppb.New(sportType.CreatedAt),
		UpdatedAt:           timestamppb.New(sportType.UpdatedAt),
		Version:             sportType.Version,
	}
}
