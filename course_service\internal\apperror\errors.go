package apperror

import (
	"errors"
	"fmt"
)

type Kind string

const (
	KindNotFound      Kind = "not_found"
	KindAlreadyExists Kind = "already_exists"
	KindValidation    Kind = "validation"
	KindConflict      Kind = "conflict"
	KindForbidden     Kind = "forbidden"
	KindInternalError Kind = "internal_error"
)

type Error struct {
	Kind    Kind
	Message string
	Err     error
}

func (e *Error) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.Err)
	}
	return e.Message
}

func (e *Error) Unwrap() error { return e.Err }

func E(k Kind, msg string, err ...error) error {
	var inner error
	if len(err) > 0 {
		inner = err[0]
	}

	return &Error{k, msg, inner}
}

func IsKind(err error, k Kind) bool {
	var ae *Error
	if errors.As(err, &ae) {
		return ae.Kind == k
	}

	return false
}
