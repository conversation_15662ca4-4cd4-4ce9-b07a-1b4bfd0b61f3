syntax = "proto3";

package sportpb;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "proto/booking.proto";

option go_package = "github.com/olzzhas/edunite-server/sport_service/pb/sportpb";

service MedicalCertificateService {
  // Upload a new medical certificate
  rpc UploadCertificate(UploadCertificateRequest) returns (MedicalCertificateResponse) {}

  // Get a medical certificate by ID
  rpc GetCertificate(GetCertificateRequest) returns (MedicalCertificateResponse) {}

  // Approve a medical certificate
  rpc ApproveCertificate(ApproveCertificateRequest) returns (MedicalCertificateResponse) {}

  // Reject a medical certificate
  rpc RejectCertificate(RejectCertificateRequest) returns (MedicalCertificateResponse) {}

  // List certificates for a user
  rpc ListUserCertificates(ListUserCertificatesRequest) returns (ListCertificatesResponse) {}

  // List pending certificates
  rpc ListPendingCertificates(ListPendingCertificatesRequest) returns (ListCertificatesResponse) {}

  // Check if a user has a valid certificate
  rpc HasValidCertificate(HasValidCertificateRequest) returns (HasValidCertificateResponse) {}
}

// Certificate status enum
enum CertificateStatus {
  CERTIFICATE_PENDING = 0;
  CERTIFICATE_APPROVED = 1;
  CERTIFICATE_REJECTED = 2;
}

// Upload certificate request
message UploadCertificateRequest {
  int64 user_id = 1;
  string file_url = 2;
  google.protobuf.Timestamp valid_from = 3;
  google.protobuf.Timestamp valid_until = 4;
}

// Get certificate request
message GetCertificateRequest {
  int64 id = 1;
}

// Approve certificate request
message ApproveCertificateRequest {
  int64 id = 1;
  int64 reviewer_id = 2;
  google.protobuf.Timestamp valid_until = 3;
}

// Reject certificate request
message RejectCertificateRequest {
  int64 id = 1;
  int64 reviewer_id = 2;
  string reason = 3;
}

// List user certificates request
message ListUserCertificatesRequest {
  int64 user_id = 1;
  int32 page = 2;
  int32 page_size = 3;
}

// List pending certificates request
message ListPendingCertificatesRequest {
  int32 page = 1;
  int32 page_size = 2;
}

// Has valid certificate request
message HasValidCertificateRequest {
  int64 user_id = 1;
}

// Has valid certificate response
message HasValidCertificateResponse {
  bool has_valid_certificate = 1;
  MedicalCertificateResponse certificate = 2;
}

// Medical certificate response
message MedicalCertificateResponse {
  int64 id = 1;
  int64 user_id = 2;
  string file_url = 3;
  CertificateStatus status = 4;
  int64 reviewed_by = 5;
  string reject_reason = 6;
  google.protobuf.Timestamp valid_from = 7;
  google.protobuf.Timestamp valid_until = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;

  // Additional information
  UserInfo user = 11;
  UserInfo reviewer = 12;
}

// List certificates response
message ListCertificatesResponse {
  repeated MedicalCertificateResponse certificates = 1;
  int32 total = 2;
  int32 page = 3;
  int32 page_size = 4;
}
