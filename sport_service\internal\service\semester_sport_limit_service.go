package service

import (
	"context"
	"errors"

	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
	"github.com/olzzhas/edunite-server/sport_service/internal/repository"
)

// SemesterSportLimitService handles operations related to semester sport limits
type SemesterSportLimitService struct {
	semesterSportLimitRepo repository.SemesterSportLimitRepository
	sportTypeRepo          repository.SportTypeRepository
}

// NewSemesterSportLimitService creates a new semester sport limit service
func NewSemesterSportLimitService(
	semesterSportLimitRepo repository.SemesterSportLimitRepository,
	sportTypeRepo repository.SportTypeRepository,
) *SemesterSportLimitService {
	return &SemesterSportLimitService{
		semesterSportLimitRepo: semesterSportLimitRepo,
		sportTypeRepo:          sportTypeRepo,
	}
}

// CreateSemesterSportLimit creates a new semester sport limit
func (s *SemesterSportLimitService) CreateSemesterSportLimit(ctx context.Context, limit *domain.SemesterSportLimit) error {
	// Validate the limit
	if err := s.validateSemesterSportLimit(ctx, limit); err != nil {
		return err
	}

	// Create the limit
	return s.semesterSportLimitRepo.Create(ctx, limit)
}

// UpdateSemesterSportLimit updates an existing semester sport limit
func (s *SemesterSportLimitService) UpdateSemesterSportLimit(ctx context.Context, limit *domain.SemesterSportLimit) error {
	// Validate the limit
	if err := s.validateSemesterSportLimit(ctx, limit); err != nil {
		return err
	}

	// Update the limit
	return s.semesterSportLimitRepo.Update(ctx, limit)
}

// GetSemesterSportLimit retrieves a semester sport limit by ID
func (s *SemesterSportLimitService) GetSemesterSportLimit(ctx context.Context, limitID int64) (*domain.SemesterSportLimit, error) {
	return s.semesterSportLimitRepo.GetByID(ctx, limitID)
}

// GetSemesterSportLimitBySemesterID retrieves a semester sport limit by semester ID
func (s *SemesterSportLimitService) GetSemesterSportLimitBySemesterID(ctx context.Context, semesterID int64) (*domain.SemesterSportLimit, error) {
	return s.semesterSportLimitRepo.GetBySemesterID(ctx, semesterID)
}

// ListSemesterSportLimits retrieves semester sport limits based on filters
func (s *SemesterSportLimitService) ListSemesterSportLimits(ctx context.Context, filter domain.SemesterSportLimitFilter) ([]*domain.SemesterSportLimit, int, error) {
	// Implementation would depend on the repository interface
	// This is a placeholder for the actual implementation
	return nil, 0, errors.New("not implemented")
}

// Helper methods

func (s *SemesterSportLimitService) validateSemesterSportLimit(ctx context.Context, limit *domain.SemesterSportLimit) error {
	// Check if the minimum lessons is non-negative
	if limit.MinLessons < 0 {
		return errors.New("minimum lessons must be non-negative")
	}

	// Check if the maximum lessons is greater than or equal to the minimum lessons
	if limit.MaxLessons < limit.MinLessons {
		return errors.New("maximum lessons must be greater than or equal to minimum lessons")
	}

	return nil
}

func (s *SemesterSportLimitService) validateSemesterID(ctx context.Context, semesterID int64) error {
	// Check if the semester exists
	_, err := s.semesterSportLimitRepo.GetBySemesterID(ctx, semesterID)
	if err != nil {
		return err
	}

	return nil
}

func (s *SemesterSportLimitService) validateSportTypeID(ctx context.Context, sportTypeID int64) error {
	// Check if the sport type exists
	_, err := s.sportTypeRepo.GetByID(ctx, sportTypeID)
	if err != nil {
		return err
	}

	return nil
}

func (s *SemesterSportLimitService) DeleteSemesterSportLimit(ctx context.Context, limitID int64) error {
	return s.semesterSportLimitRepo.Delete(ctx, limitID)
}
