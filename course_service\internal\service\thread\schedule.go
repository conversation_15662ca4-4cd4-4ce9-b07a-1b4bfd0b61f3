package thread

import (
	"context"
	"errors"
	"time"

	"github.com/olzzhas/edunite-server/course_service/internal/database"
	threadpb "github.com/olzzhas/edunite-server/course_service/pb/thread"
	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// CreateThreadSchedule создаёт шаблон расписания для потока
func (s *Service) CreateThreadSchedule(
	ctx context.Context, req *threadpb.ThreadScheduleRequest,
) (*threadpb.ThreadScheduleResponse, error) {

	v := validator.New()
	start, end := s.validateScheduleInput(v,
		req.GetThreadId(), req.GetDayOfWeek(),
		req.GetStartTime(), req.GetEndTime())
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "%v", v.Errors)
	}

	// Check for time conflicts with other schedules for this thread
	overlap, err := s.scheduleRepo.OverlapExists(ctx,
		req.GetThreadId(), int(req.GetDayOfWeek()), start, end, 0)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "check time overlap: %v", err)
	}
	if overlap {
		return nil, status.Errorf(codes.AlreadyExists, "time interval overlaps existing schedule")
	}

	// Create the schedule
	ts := &database.ThreadSchedule{
		ThreadID:  req.GetThreadId(),
		DayOfWeek: int(req.GetDayOfWeek()),
		StartTime: start,
		EndTime:   end,
		Location:  "",
		// LocationID will be set later if a location is provided
	}

	// If a location is provided, check for location conflicts
	if req.GetLocation() != "" {
		// First, try to find the location by name
		location, err := s.locationRepo.GetByName(ctx, req.GetLocation())
		if err != nil {
			if errors.Is(err, database.ErrLocationNotFound) {
				// Location doesn't exist, create it
				location = &database.Location{
					Name:        req.GetLocation(),
					Description: "",
					Capacity:    30, // Default capacity
				}
				if err := s.locationRepo.Create(ctx, location); err != nil {
					return nil, status.Errorf(codes.Internal, "create location: %v", err)
				}
			} else {
				return nil, status.Errorf(codes.Internal, "fetch location: %v", err)
			}
		}

		// Check if the location is available during this time slot
		locationOverlap, err := s.scheduleRepo.LocationOverlapExists(ctx,
			location.ID, int(req.GetDayOfWeek()), start, end, 0)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "check location overlap: %v", err)
		}
		if locationOverlap {
			return nil, status.Errorf(codes.AlreadyExists, "location is already booked for this time interval")
		}

		// Set the location ID and name
		ts.LocationID = location.ID
		ts.Location = location.Name
	}

	// Save the schedule
	if err := s.scheduleRepo.Create(ctx, ts); err != nil {
		return nil, status.Errorf(codes.Internal, "create schedule: %v", err)
	}

	return scheduleToPB(ts), nil
}

// ListThreadSchedules возвращает все расписания для потока
func (s *Service) ListThreadSchedules(
	ctx context.Context, req *threadpb.ThreadSchedulesRequest,
) (*threadpb.ThreadSchedulesResponse, error) {

	if req.GetThreadId() <= 0 {
		return nil, status.Errorf(codes.InvalidArgument, "thread_id must be > 0")
	}
	list, err := s.scheduleRepo.ListByThread(ctx, req.GetThreadId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "list schedules: %v", err)
	}
	return schedulesToPB(list), nil
}

// GetThreadScheduleByID возвращает один шаблон расписания
func (s *Service) GetThreadScheduleByID(
	ctx context.Context, req *threadpb.ThreadScheduleByID,
) (*threadpb.ThreadScheduleResponse, error) {

	if req.GetScheduleId() <= 0 {
		return nil, status.Errorf(codes.InvalidArgument, "schedule_id must be > 0")
	}
	ts, err := s.scheduleRepo.GetByID(ctx, req.GetScheduleId())
	if err != nil {
		if errors.Is(err, database.ErrScheduleNotFound) {
			return nil, status.Errorf(codes.NotFound, "schedule not found")
		}
		return nil, status.Errorf(codes.Internal, "get schedule: %v", err)
	}
	return scheduleToPB(ts), nil
}

// UpdateThreadSchedule обновляет существующее расписание
func (s *Service) UpdateThreadSchedule(
	ctx context.Context, req *threadpb.ThreadScheduleUpdateRequest,
) (*threadpb.ThreadScheduleResponse, error) {

	v := validator.New()
	start, end := s.validateScheduleInput(v,
		0, req.GetDayOfWeek(), req.GetStartTime(), req.GetEndTime())
	v.Check(req.GetId() > 0, "id", "must be > 0")
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "%v", v.Errors)
	}

	existing, err := s.scheduleRepo.GetByID(ctx, req.GetId())
	if err != nil {
		if errors.Is(err, database.ErrScheduleNotFound) {
			return nil, status.Errorf(codes.NotFound, "schedule not found")
		}
		return nil, status.Errorf(codes.Internal, "fetch schedule: %v", err)
	}

	// Check for time conflicts with other schedules for this thread
	overlap, err := s.scheduleRepo.OverlapExists(ctx,
		existing.ThreadID, int(req.GetDayOfWeek()), start, end, existing.ID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "check time overlap: %v", err)
	}
	if overlap {
		return nil, status.Errorf(codes.AlreadyExists, "time interval overlaps existing schedule")
	}

	// Update basic schedule information
	existing.DayOfWeek = int(req.GetDayOfWeek())
	existing.StartTime = start
	existing.EndTime = end

	// If a location is provided, check for location conflicts
	if req.GetLocation() != "" {
		// First, try to find the location by name
		location, err := s.locationRepo.GetByName(ctx, req.GetLocation())
		if err != nil {
			if errors.Is(err, database.ErrLocationNotFound) {
				// Location doesn't exist, create it
				location = &database.Location{
					Name:        req.GetLocation(),
					Description: "",
					Capacity:    30, // Default capacity
				}
				if err := s.locationRepo.Create(ctx, location); err != nil {
					return nil, status.Errorf(codes.Internal, "create location: %v", err)
				}
			} else {
				return nil, status.Errorf(codes.Internal, "fetch location: %v", err)
			}
		}

		// If the location is different from the current one, check for conflicts
		if existing.LocationID != location.ID {
			// Check if the location is available during this time slot
			locationOverlap, err := s.scheduleRepo.LocationOverlapExists(ctx,
				location.ID, int(req.GetDayOfWeek()), start, end, existing.ID)
			if err != nil {
				return nil, status.Errorf(codes.Internal, "check location overlap: %v", err)
			}
			if locationOverlap {
				return nil, status.Errorf(codes.AlreadyExists, "location is already booked for this time interval")
			}
		}

		// Set the location ID and name
		existing.LocationID = location.ID
		existing.Location = location.Name
	}

	// Save the updated schedule
	if err := s.scheduleRepo.Update(ctx, existing); err != nil {
		return nil, status.Errorf(codes.Internal, "update schedule: %v", err)
	}

	return scheduleToPB(existing), nil
}

// DeleteThreadScheduleByID удаляет шаблон расписания
func (s *Service) DeleteThreadScheduleByID(
	ctx context.Context, req *threadpb.ThreadScheduleByID,
) (*threadpb.ThreadEmptyResponse, error) {

	if req.GetScheduleId() <= 0 {
		return nil, status.Errorf(codes.InvalidArgument, "schedule_id must be > 0")
	}
	if err := s.scheduleRepo.Delete(ctx, req.GetScheduleId()); err != nil {
		if errors.Is(err, database.ErrScheduleNotFound) {
			return nil, status.Errorf(codes.NotFound, "schedule not found")
		}
		return nil, status.Errorf(codes.Internal, "delete schedule: %v", err)
	}
	return &threadpb.ThreadEmptyResponse{}, nil
}

// ---------------------
// Вспомогательные функции
// ---------------------

func scheduleToPB(ts *database.ThreadSchedule) *threadpb.ThreadScheduleResponse {
	return &threadpb.ThreadScheduleResponse{
		Id:        ts.ID,
		ThreadId:  ts.ThreadID,
		DayOfWeek: uint32(ts.DayOfWeek),
		StartTime: ts.StartTime.Format("15:04:05"),
		EndTime:   ts.EndTime.Format("15:04:05"),
		Location:  ts.Location,
		CreatedAt: timestamppb.New(ts.CreatedAt),
		UpdatedAt: timestamppb.New(ts.UpdatedAt),
	}
}

func schedulesToPB(list []*database.ThreadSchedule) *threadpb.ThreadSchedulesResponse {
	resp := &threadpb.ThreadSchedulesResponse{}
	for _, ts := range list {
		resp.Schedules = append(resp.Schedules, scheduleToPB(ts))
	}
	return resp
}

func parseClock(value, field string, v *validator.Validator) time.Time {
	t, err := time.Parse("15:04:05", value)
	v.Check(err == nil, field, "must be HH:MM:SS")
	return t
}

func (s *Service) validateScheduleInput(v *validator.Validator,
	threadID int64, day uint32, startStr, endStr string) (time.Time, time.Time) {

	v.Check(threadID > 0, "thread_id", "must be > 0")
	v.Check(day >= 1 && day <= 7, "day_of_week", "must be in range 1…7")

	start := parseClock(startStr, "start_time", v)
	end := parseClock(endStr, "end_time", v)
	if !start.IsZero() && !end.IsZero() {
		v.Check(start.Before(end), "time_range", "start_time must be before end_time")
	}
	return start, end
}
