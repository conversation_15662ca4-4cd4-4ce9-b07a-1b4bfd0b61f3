package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/olzzhas/edunite-server/logger_service/internal"
	"github.com/olzzhas/edunite-server/logger_service/loggerpb"
	"github.com/streadway/amqp"

	"go.mongodb.org/mongo-driver/mongo"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type LoggerServer struct {
	loggerpb.UnimplementedLoggerServiceServer
	logger     *internal.Logger
	mongoDB    *mongo.Database
	rabbitConn *amqp.Connection
}

func NewLoggerServer(logger *internal.Logger, db *mongo.Database, rabbitConn *amqp.Connection) *LoggerServer {
	return &LoggerServer{
		logger:     logger,
		mongoDB:    db,
		rabbitConn: rabbitConn,
	}
}

// LogEvent - старый синхронный метод (gRPC), если кто-то напрямую вызывает LoggerService.
// Он сохранит лог в MongoDB и опубликует в RabbitMQ (как было раньше).
func (s *LoggerServer) LogEvent(ctx context.Context, req *loggerpb.LogEventRequest) (*loggerpb.LogEventResponse, error) {
	logData := map[string]interface{}{
		"level":    req.Level,
		"message":  req.Message,
		"service":  req.ServiceName,
		"data":     req.Data,
		"datetime": time.Now().UTC(),
	}

	fmt.Println("gRPC LogEvent received:", logData)

	if err := internal.SaveLogToMongo(s.mongoDB, logData); err != nil {
		fmt.Println("Error saving log to Mongo:", err)
		return nil, err
	}

	if err := internal.PublishToRabbitMQ(s.rabbitConn, logData); err != nil {
		fmt.Println("Error publishing log to RabbitMQ:", err)
		return nil, err
	}

	s.logger.LogEvent(req.Level, req.Message, req.ServiceName, req.Data)

	return &loggerpb.LogEventResponse{
		Success: true,
		Message: "Log saved successfully",
	}, nil
}

// GetLogs retrieves logs from MongoDB based on filter criteria
func (s *LoggerServer) GetLogs(ctx context.Context, req *loggerpb.GetLogsRequest) (*loggerpb.GetLogsResponse, error) {
	// Convert proto request to internal filter
	filter := internal.LogFilter{
		Level:   req.Level,
		Service: req.Service,
		Limit:   req.Limit,
		Skip:    req.Skip,
	}

	// Set start date if provided
	if req.StartDate != nil {
		filter.StartDate = req.StartDate.AsTime()
	}

	// Set end date if provided
	if req.EndDate != nil {
		filter.EndDate = req.EndDate.AsTime()
	}

	// Get logs from MongoDB
	logs, err := internal.GetLogs(ctx, s.mongoDB, filter)
	if err != nil {
		if err == internal.ErrNoLogsFound {
			return &loggerpb.GetLogsResponse{Logs: []*loggerpb.LogEntry{}}, nil
		}
		return nil, status.Errorf(codes.Internal, "failed to get logs: %v", err)
	}

	// Convert internal logs to proto logs
	var protoLogs []*loggerpb.LogEntry
	for _, log := range logs {
		// Convert map[string]interface{} to map[string]string for proto
		dataMap := make(map[string]string)
		for k, v := range log.Data {
			if strVal, ok := v.(string); ok {
				dataMap[k] = strVal
			} else {
				// Convert non-string values to JSON string
				jsonBytes, err := json.Marshal(v)
				if err == nil {
					dataMap[k] = string(jsonBytes)
				}
			}
		}

		protoLog := &loggerpb.LogEntry{
			Level:    log.Level,
			Message:  log.Message,
			Service:  log.Service,
			Data:     dataMap,
			Datetime: timestamppb.New(log.Datetime),
		}
		protoLogs = append(protoLogs, protoLog)
	}

	return &loggerpb.GetLogsResponse{Logs: protoLogs}, nil
}

// GetLogByID retrieves a specific log by its ID
func (s *LoggerServer) GetLogByID(ctx context.Context, req *loggerpb.GetLogByIDRequest) (*loggerpb.LogEntry, error) {
	// Get log from MongoDB
	log, err := internal.GetLogByID(ctx, s.mongoDB, req.Id)
	if err != nil {
		if err == internal.ErrNoLogsFound {
			return nil, status.Errorf(codes.NotFound, "log not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to get log: %v", err)
	}

	// Convert map[string]interface{} to map[string]string for proto
	dataMap := make(map[string]string)
	for k, v := range log.Data {
		if strVal, ok := v.(string); ok {
			dataMap[k] = strVal
		} else {
			// Convert non-string values to JSON string
			jsonBytes, err := json.Marshal(v)
			if err == nil {
				dataMap[k] = string(jsonBytes)
			}
		}
	}

	// Convert internal log to proto log
	protoLog := &loggerpb.LogEntry{
		Id:       req.Id,
		Level:    log.Level,
		Message:  log.Message,
		Service:  log.Service,
		Data:     dataMap,
		Datetime: timestamppb.New(log.Datetime),
	}

	return protoLog, nil
}

// CountLogs counts the number of logs matching the filter criteria
func (s *LoggerServer) CountLogs(ctx context.Context, req *loggerpb.GetLogsRequest) (*loggerpb.CountLogsResponse, error) {
	// Convert proto request to internal filter
	filter := internal.LogFilter{
		Level:   req.Level,
		Service: req.Service,
	}

	// Set start date if provided
	if req.StartDate != nil {
		filter.StartDate = req.StartDate.AsTime()
	}

	// Set end date if provided
	if req.EndDate != nil {
		filter.EndDate = req.EndDate.AsTime()
	}

	// Count logs from MongoDB
	count, err := internal.CountLogs(ctx, s.mongoDB, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to count logs: %v", err)
	}

	return &loggerpb.CountLogsResponse{Count: count}, nil
}

// GetLogLevels retrieves all unique log levels from the logs collection
func (s *LoggerServer) GetLogLevels(ctx context.Context, req *loggerpb.GetLogLevelsRequest) (*loggerpb.GetLogLevelsResponse, error) {
	// Get log levels from MongoDB
	levels, err := internal.GetLogLevels(ctx, s.mongoDB)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get log levels: %v", err)
	}

	return &loggerpb.GetLogLevelsResponse{Levels: levels}, nil
}

// GetLogServices retrieves all unique service names from the logs collection
func (s *LoggerServer) GetLogServices(ctx context.Context, req *loggerpb.GetLogServicesRequest) (*loggerpb.GetLogServicesResponse, error) {
	// Get service names from MongoDB
	services, err := internal.GetLogServices(ctx, s.mongoDB)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get service names: %v", err)
	}

	return &loggerpb.GetLogServicesResponse{Services: services}, nil
}

func main() {
	// Подключение к MongoDB
	db, err := internal.ConnectMongoDB("mongodb://mongodb:27017")
	if err != nil {
		log.Fatalf("Failed to connect to MongoDB: %v", err)
	}
	defer db.Client().Disconnect(context.Background())

	// Подключение к RabbitMQ
	rabbitConn, err := internal.ConnectRabbitMQ("amqp://guest:guest@rabbitmq:5672/")
	if err != nil {
		log.Fatalf("Failed to connect to RabbitMQ: %v", err)
	}
	defer rabbitConn.Close()

	// Инициализация логгера
	logger := internal.NewLogger()

	// -----------------------------
	// 1) Запускаем consumer:
	// -----------------------------
	go startLogsConsumer(rabbitConn, db)

	// -----------------------------
	// 2) Запускаем gRPC сервер
	// -----------------------------
	lis, err := net.Listen("tcp", ":50052")
	if err != nil {
		log.Fatalf("Failed to listen: %v", err)
	}
	grpcServer := grpc.NewServer()
	loggerpb.RegisterLoggerServiceServer(grpcServer, NewLoggerServer(logger, db, rabbitConn))

	log.Println("Logger Service is running on port 50052...")

	// Запуск gRPC в горутине
	go func() {
		if err := grpcServer.Serve(lis); err != nil {
			log.Fatalf("Failed to serve gRPC: %v", err)
		}
	}()

	// Ловим сигналы завершения (Ctrl+C / SIGTERM)
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	<-sigCh

	log.Println("Shutting down Logger Service...")
	grpcServer.GracefulStop()
}

// startLogsConsumer читает сообщения из очереди "logs" и сохраняет их в MongoDB асинхронно.
func startLogsConsumer(rabbitConn *amqp.Connection, db *mongo.Database) {
	ch, err := rabbitConn.Channel()
	if err != nil {
		log.Fatalf("Failed to open channel for consumer: %v", err)
	}
	defer ch.Close()

	q, err := ch.QueueDeclare("logs", true, false, false, false, nil)
	if err != nil {
		log.Fatalf("QueueDeclare error: %v", err)
	}

	msgs, err := ch.Consume(
		q.Name, // queue
		"",     // consumer tag
		true,   // autoAck
		false,  // exclusive
		false,  // noLocal
		false,  // noWait
		nil,
	)
	if err != nil {
		log.Fatalf("Consume error: %v", err)
	}

	log.Println("LoggerService Consumer started. Waiting for messages in queue:", q.Name)

	for d := range msgs {
		var logData map[string]interface{}
		if err := json.Unmarshal(d.Body, &logData); err != nil {
			log.Printf("Failed to unmarshal log message: %v", err)
			continue
		}

		// Сохраняем в MongoDB
		if err := internal.SaveLogToMongo(db, logData); err != nil {
			log.Printf("Error saving consumed log to Mongo: %v", err)
			continue
		}

		log.Printf("Consumer: Log saved from RabbitMQ: %v", logData)
	}
}
