// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: proto/medical_certificate.proto

package sportpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MedicalCertificateService_UploadCertificate_FullMethodName       = "/sportpb.MedicalCertificateService/UploadCertificate"
	MedicalCertificateService_GetCertificate_FullMethodName          = "/sportpb.MedicalCertificateService/GetCertificate"
	MedicalCertificateService_ApproveCertificate_FullMethodName      = "/sportpb.MedicalCertificateService/ApproveCertificate"
	MedicalCertificateService_RejectCertificate_FullMethodName       = "/sportpb.MedicalCertificateService/RejectCertificate"
	MedicalCertificateService_ListUserCertificates_FullMethodName    = "/sportpb.MedicalCertificateService/ListUserCertificates"
	MedicalCertificateService_ListPendingCertificates_FullMethodName = "/sportpb.MedicalCertificateService/ListPendingCertificates"
	MedicalCertificateService_HasValidCertificate_FullMethodName     = "/sportpb.MedicalCertificateService/HasValidCertificate"
)

// MedicalCertificateServiceClient is the client API for MedicalCertificateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MedicalCertificateServiceClient interface {
	// Upload a new medical certificate
	UploadCertificate(ctx context.Context, in *UploadCertificateRequest, opts ...grpc.CallOption) (*MedicalCertificateResponse, error)
	// Get a medical certificate by ID
	GetCertificate(ctx context.Context, in *GetCertificateRequest, opts ...grpc.CallOption) (*MedicalCertificateResponse, error)
	// Approve a medical certificate
	ApproveCertificate(ctx context.Context, in *ApproveCertificateRequest, opts ...grpc.CallOption) (*MedicalCertificateResponse, error)
	// Reject a medical certificate
	RejectCertificate(ctx context.Context, in *RejectCertificateRequest, opts ...grpc.CallOption) (*MedicalCertificateResponse, error)
	// List certificates for a user
	ListUserCertificates(ctx context.Context, in *ListUserCertificatesRequest, opts ...grpc.CallOption) (*ListCertificatesResponse, error)
	// List pending certificates
	ListPendingCertificates(ctx context.Context, in *ListPendingCertificatesRequest, opts ...grpc.CallOption) (*ListCertificatesResponse, error)
	// Check if a user has a valid certificate
	HasValidCertificate(ctx context.Context, in *HasValidCertificateRequest, opts ...grpc.CallOption) (*HasValidCertificateResponse, error)
}

type medicalCertificateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMedicalCertificateServiceClient(cc grpc.ClientConnInterface) MedicalCertificateServiceClient {
	return &medicalCertificateServiceClient{cc}
}

func (c *medicalCertificateServiceClient) UploadCertificate(ctx context.Context, in *UploadCertificateRequest, opts ...grpc.CallOption) (*MedicalCertificateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MedicalCertificateResponse)
	err := c.cc.Invoke(ctx, MedicalCertificateService_UploadCertificate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medicalCertificateServiceClient) GetCertificate(ctx context.Context, in *GetCertificateRequest, opts ...grpc.CallOption) (*MedicalCertificateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MedicalCertificateResponse)
	err := c.cc.Invoke(ctx, MedicalCertificateService_GetCertificate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medicalCertificateServiceClient) ApproveCertificate(ctx context.Context, in *ApproveCertificateRequest, opts ...grpc.CallOption) (*MedicalCertificateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MedicalCertificateResponse)
	err := c.cc.Invoke(ctx, MedicalCertificateService_ApproveCertificate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medicalCertificateServiceClient) RejectCertificate(ctx context.Context, in *RejectCertificateRequest, opts ...grpc.CallOption) (*MedicalCertificateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MedicalCertificateResponse)
	err := c.cc.Invoke(ctx, MedicalCertificateService_RejectCertificate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medicalCertificateServiceClient) ListUserCertificates(ctx context.Context, in *ListUserCertificatesRequest, opts ...grpc.CallOption) (*ListCertificatesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCertificatesResponse)
	err := c.cc.Invoke(ctx, MedicalCertificateService_ListUserCertificates_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medicalCertificateServiceClient) ListPendingCertificates(ctx context.Context, in *ListPendingCertificatesRequest, opts ...grpc.CallOption) (*ListCertificatesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCertificatesResponse)
	err := c.cc.Invoke(ctx, MedicalCertificateService_ListPendingCertificates_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *medicalCertificateServiceClient) HasValidCertificate(ctx context.Context, in *HasValidCertificateRequest, opts ...grpc.CallOption) (*HasValidCertificateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HasValidCertificateResponse)
	err := c.cc.Invoke(ctx, MedicalCertificateService_HasValidCertificate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MedicalCertificateServiceServer is the server API for MedicalCertificateService service.
// All implementations must embed UnimplementedMedicalCertificateServiceServer
// for forward compatibility.
type MedicalCertificateServiceServer interface {
	// Upload a new medical certificate
	UploadCertificate(context.Context, *UploadCertificateRequest) (*MedicalCertificateResponse, error)
	// Get a medical certificate by ID
	GetCertificate(context.Context, *GetCertificateRequest) (*MedicalCertificateResponse, error)
	// Approve a medical certificate
	ApproveCertificate(context.Context, *ApproveCertificateRequest) (*MedicalCertificateResponse, error)
	// Reject a medical certificate
	RejectCertificate(context.Context, *RejectCertificateRequest) (*MedicalCertificateResponse, error)
	// List certificates for a user
	ListUserCertificates(context.Context, *ListUserCertificatesRequest) (*ListCertificatesResponse, error)
	// List pending certificates
	ListPendingCertificates(context.Context, *ListPendingCertificatesRequest) (*ListCertificatesResponse, error)
	// Check if a user has a valid certificate
	HasValidCertificate(context.Context, *HasValidCertificateRequest) (*HasValidCertificateResponse, error)
	mustEmbedUnimplementedMedicalCertificateServiceServer()
}

// UnimplementedMedicalCertificateServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMedicalCertificateServiceServer struct{}

func (UnimplementedMedicalCertificateServiceServer) UploadCertificate(context.Context, *UploadCertificateRequest) (*MedicalCertificateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadCertificate not implemented")
}
func (UnimplementedMedicalCertificateServiceServer) GetCertificate(context.Context, *GetCertificateRequest) (*MedicalCertificateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCertificate not implemented")
}
func (UnimplementedMedicalCertificateServiceServer) ApproveCertificate(context.Context, *ApproveCertificateRequest) (*MedicalCertificateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApproveCertificate not implemented")
}
func (UnimplementedMedicalCertificateServiceServer) RejectCertificate(context.Context, *RejectCertificateRequest) (*MedicalCertificateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RejectCertificate not implemented")
}
func (UnimplementedMedicalCertificateServiceServer) ListUserCertificates(context.Context, *ListUserCertificatesRequest) (*ListCertificatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserCertificates not implemented")
}
func (UnimplementedMedicalCertificateServiceServer) ListPendingCertificates(context.Context, *ListPendingCertificatesRequest) (*ListCertificatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPendingCertificates not implemented")
}
func (UnimplementedMedicalCertificateServiceServer) HasValidCertificate(context.Context, *HasValidCertificateRequest) (*HasValidCertificateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HasValidCertificate not implemented")
}
func (UnimplementedMedicalCertificateServiceServer) mustEmbedUnimplementedMedicalCertificateServiceServer() {
}
func (UnimplementedMedicalCertificateServiceServer) testEmbeddedByValue() {}

// UnsafeMedicalCertificateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MedicalCertificateServiceServer will
// result in compilation errors.
type UnsafeMedicalCertificateServiceServer interface {
	mustEmbedUnimplementedMedicalCertificateServiceServer()
}

func RegisterMedicalCertificateServiceServer(s grpc.ServiceRegistrar, srv MedicalCertificateServiceServer) {
	// If the following call pancis, it indicates UnimplementedMedicalCertificateServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MedicalCertificateService_ServiceDesc, srv)
}

func _MedicalCertificateService_UploadCertificate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadCertificateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedicalCertificateServiceServer).UploadCertificate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MedicalCertificateService_UploadCertificate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedicalCertificateServiceServer).UploadCertificate(ctx, req.(*UploadCertificateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MedicalCertificateService_GetCertificate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCertificateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedicalCertificateServiceServer).GetCertificate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MedicalCertificateService_GetCertificate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedicalCertificateServiceServer).GetCertificate(ctx, req.(*GetCertificateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MedicalCertificateService_ApproveCertificate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApproveCertificateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedicalCertificateServiceServer).ApproveCertificate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MedicalCertificateService_ApproveCertificate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedicalCertificateServiceServer).ApproveCertificate(ctx, req.(*ApproveCertificateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MedicalCertificateService_RejectCertificate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RejectCertificateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedicalCertificateServiceServer).RejectCertificate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MedicalCertificateService_RejectCertificate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedicalCertificateServiceServer).RejectCertificate(ctx, req.(*RejectCertificateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MedicalCertificateService_ListUserCertificates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserCertificatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedicalCertificateServiceServer).ListUserCertificates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MedicalCertificateService_ListUserCertificates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedicalCertificateServiceServer).ListUserCertificates(ctx, req.(*ListUserCertificatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MedicalCertificateService_ListPendingCertificates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPendingCertificatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedicalCertificateServiceServer).ListPendingCertificates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MedicalCertificateService_ListPendingCertificates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedicalCertificateServiceServer).ListPendingCertificates(ctx, req.(*ListPendingCertificatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MedicalCertificateService_HasValidCertificate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HasValidCertificateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MedicalCertificateServiceServer).HasValidCertificate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MedicalCertificateService_HasValidCertificate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MedicalCertificateServiceServer).HasValidCertificate(ctx, req.(*HasValidCertificateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MedicalCertificateService_ServiceDesc is the grpc.ServiceDesc for MedicalCertificateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MedicalCertificateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sportpb.MedicalCertificateService",
	HandlerType: (*MedicalCertificateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UploadCertificate",
			Handler:    _MedicalCertificateService_UploadCertificate_Handler,
		},
		{
			MethodName: "GetCertificate",
			Handler:    _MedicalCertificateService_GetCertificate_Handler,
		},
		{
			MethodName: "ApproveCertificate",
			Handler:    _MedicalCertificateService_ApproveCertificate_Handler,
		},
		{
			MethodName: "RejectCertificate",
			Handler:    _MedicalCertificateService_RejectCertificate_Handler,
		},
		{
			MethodName: "ListUserCertificates",
			Handler:    _MedicalCertificateService_ListUserCertificates_Handler,
		},
		{
			MethodName: "ListPendingCertificates",
			Handler:    _MedicalCertificateService_ListPendingCertificates_Handler,
		},
		{
			MethodName: "HasValidCertificate",
			Handler:    _MedicalCertificateService_HasValidCertificate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/medical_certificate.proto",
}
