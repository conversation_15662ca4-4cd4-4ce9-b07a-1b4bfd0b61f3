package service

import (
	"context"
	"errors"
	"time"

	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
	"github.com/olzzhas/edunite-server/sport_service/internal/repository"
)

// ScheduleService handles operations related to sport schedules
type ScheduleService struct {
	scheduleRepo repository.ScheduleRepository
	facilityRepo repository.FacilityRepository
}

// NewScheduleService creates a new schedule service
func NewScheduleService(
	scheduleRepo repository.ScheduleRepository,
	facilityRepo repository.FacilityRepository,
) *ScheduleService {
	return &ScheduleService{
		scheduleRepo: scheduleRepo,
		facilityRepo: facilityRepo,
	}
}

// CreateSchedule creates a new schedule
func (s *ScheduleService) CreateSchedule(ctx context.Context, schedule *domain.Schedule) error {
	// Validate the schedule
	if err := s.validateSchedule(ctx, schedule); err != nil {
		return err
	}

	// Create the schedule
	return s.scheduleRepo.Create(ctx, schedule)
}

// CreateWeeklySchedules creates schedules from a weekly template
func (s *ScheduleService) CreateWeeklySchedules(ctx context.Context, template *domain.WeeklyScheduleTemplate) error {
	// Validate the template
	if err := s.validateWeeklyTemplate(ctx, template); err != nil {
		return err
	}

	// Generate schedules from the template
	schedules, err := s.generateSchedulesFromTemplate(template)
	if err != nil {
		return err
	}

	// Create the schedules
	return s.scheduleRepo.CreateBatch(ctx, schedules)
}

// UpdateSchedule updates an existing schedule
func (s *ScheduleService) UpdateSchedule(ctx context.Context, schedule *domain.Schedule) error {
	// Validate the schedule
	if err := s.validateSchedule(ctx, schedule); err != nil {
		return err
	}

	// Update the schedule
	return s.scheduleRepo.Update(ctx, schedule)
}

// DeleteSchedule deletes a schedule
func (s *ScheduleService) DeleteSchedule(ctx context.Context, scheduleID int64) error {
	return s.scheduleRepo.Delete(ctx, scheduleID)
}

// GetSchedule retrieves a schedule by ID
func (s *ScheduleService) GetSchedule(ctx context.Context, scheduleID int64) (*domain.Schedule, error) {
	return s.scheduleRepo.GetByID(ctx, scheduleID)
}

// ListSchedules retrieves schedules based on filters
func (s *ScheduleService) ListSchedules(ctx context.Context, filter domain.ScheduleFilter) ([]*domain.Schedule, int, error) {
	schedules, err := s.scheduleRepo.List(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	count, err := s.scheduleRepo.Count(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	return schedules, count, nil
}

// Helper methods

func (s *ScheduleService) validateSchedule(ctx context.Context, schedule *domain.Schedule) error {
	// Check if the facility exists
	_, err := s.facilityRepo.GetByID(ctx, schedule.FacilityID)
	if err != nil {
		return err
	}

	// Check if the start time is before the end time
	if !schedule.StartTime.Before(schedule.EndTime) {
		return errors.New("start time must be before end time")
	}

	// Check if the cancellation deadline is before the start time
	if !schedule.CancellationDeadline.Before(schedule.StartTime) {
		return errors.New("cancellation deadline must be before start time")
	}

	return nil
}

func (s *ScheduleService) validateWeeklyTemplate(ctx context.Context, template *domain.WeeklyScheduleTemplate) error {
	// Check if the facility exists
	_, err := s.facilityRepo.GetByID(ctx, template.FacilityID)
	if err != nil {
		return err
	}

	// Check if the day of week is valid
	if template.DayOfWeek < 1 || template.DayOfWeek > 7 {
		return errors.New("day of week must be between 1 and 7")
	}

	// Check if the start time is before the end time
	startTimeOfDay := time.Date(0, 1, 1, template.StartTime.Hour(), template.StartTime.Minute(), template.StartTime.Second(), 0, time.UTC)
	endTimeOfDay := time.Date(0, 1, 1, template.EndTime.Hour(), template.EndTime.Minute(), template.EndTime.Second(), 0, time.UTC)
	if !startTimeOfDay.Before(endTimeOfDay) {
		return errors.New("start time must be before end time")
	}

	// Check if the start date is before the end date
	if !template.StartDate.Before(template.EndDate) {
		return errors.New("start date must be before end date")
	}

	return nil
}

func (s *ScheduleService) generateSchedulesFromTemplate(template *domain.WeeklyScheduleTemplate) ([]*domain.Schedule, error) {
	var schedules []*domain.Schedule

	// Calculate the number of weeks between start and end dates
	startDate := template.StartDate
	endDate := template.EndDate

	// Iterate through each week
	currentDate := startDate
	for currentDate.Before(endDate) || currentDate.Equal(endDate) {
		// Calculate the date for the specified day of week in the current week
		daysUntilTargetDay := (template.DayOfWeek - int(currentDate.Weekday()) + 7) % 7
		targetDate := currentDate.AddDate(0, 0, daysUntilTargetDay)

		// If the target date is after the end date, break
		if targetDate.After(endDate) {
			break
		}

		// Create a schedule for this date
		startTime := time.Date(
			targetDate.Year(), targetDate.Month(), targetDate.Day(),
			template.StartTime.Hour(), template.StartTime.Minute(), template.StartTime.Second(),
			0, targetDate.Location(),
		)

		endTime := time.Date(
			targetDate.Year(), targetDate.Month(), targetDate.Day(),
			template.EndTime.Hour(), template.EndTime.Minute(), template.EndTime.Second(),
			0, targetDate.Location(),
		)

		// Calculate cancellation deadline (20:00 the day before)
		cancellationDeadline := time.Date(
			targetDate.Year(), targetDate.Month(), targetDate.Day()-1,
			20, 0, 0, 0, targetDate.Location(),
		)

		schedule := &domain.Schedule{
			FacilityID:           template.FacilityID,
			TeacherID:            template.TeacherID,
			SemesterID:           template.SemesterID,
			SportTypeID:          template.SportTypeID, // Added SportTypeID
			StartTime:            startTime,
			EndTime:              endTime,
			CancellationDeadline: cancellationDeadline,
			Location:             template.Location,
		}

		schedules = append(schedules, schedule)

		// Move to the next week
		currentDate = currentDate.AddDate(0, 0, 7)
	}

	return schedules, nil
}

func (s *ScheduleService) GetSchedulesForTeacher(ctx context.Context, teacherID int64, startDate, endDate time.Time) ([]*domain.Schedule, error) {
	return s.scheduleRepo.GetByTeacherIDAndTimeRange(ctx, teacherID, startDate, endDate)
}

func (s *ScheduleService) GetSchedulesForFacility(ctx context.Context, facilityID int64, startDate, endDate time.Time) ([]*domain.Schedule, error) {
	return s.scheduleRepo.GetByFacilityIDAndTimeRange(ctx, facilityID, startDate, endDate)
}

func (s *ScheduleService) GetSchedulesForSemester(ctx context.Context, semesterID int64) ([]*domain.Schedule, error) {
	return s.scheduleRepo.GetBySemesterID(ctx, semesterID)
}
