package clients

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/olzzhas/edunite-server/auth_service/pb/generated/pb"
	"google.golang.org/grpc"
)

// AuthClient is a client for the Auth Service
type AuthClient struct {
	client pb.AuthServiceClient
}

// NewAuthClient creates a new AuthClient
func NewAuthClient(conn *grpc.ClientConn) *AuthClient {
	return &AuthClient{
		client: pb.NewAuthServiceClient(conn),
	}
}

// Register registers a new user
func (ac *AuthClient) Register(username, password, email, name, surname string) (*pb.AuthResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	req := &pb.RegisterRequest{
		Username: username,
		Password: password,
		Email:    email,
		Name:     name,
		Surname:  surname,
	}

	resp, err := ac.client.Register(ctx, req)
	if err != nil {
		log.Printf("Failed to register user: %v", err)
		return nil, fmt.Errorf("failed to register user: %w", err)
	}

	return resp, nil
}

// Login authenticates a user
func (ac *AuthClient) Login(username, password string) (*pb.AuthResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	req := &pb.LoginRequest{
		Username: username,
		Password: password,
	}

	resp, err := ac.client.Login(ctx, req)
	if err != nil {
		log.Printf("Failed to login user: %v", err)
		return nil, fmt.Errorf("failed to login user: %w", err)
	}

	return resp, nil
}

// ValidateToken validates a token
func (ac *AuthClient) ValidateToken(token string) (bool, string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	req := &pb.ValidateTokenRequest{
		Token: token,
	}

	resp, err := ac.client.ValidateToken(ctx, req)
	if err != nil {
		log.Printf("Failed to validate token: %v", err)
		return false, "", fmt.Errorf("failed to validate token: %w", err)
	}

	return resp.Valid, resp.UserId, nil
}

// RefreshToken refreshes a token
func (ac *AuthClient) RefreshToken(refreshToken string) (*pb.AuthResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	req := &pb.RefreshTokenRequest{
		RefreshToken: refreshToken,
	}

	resp, err := ac.client.RefreshToken(ctx, req)
	if err != nil {
		log.Printf("Failed to refresh token: %v", err)
		return nil, fmt.Errorf("failed to refresh token: %w", err)
	}

	return resp, nil
}
