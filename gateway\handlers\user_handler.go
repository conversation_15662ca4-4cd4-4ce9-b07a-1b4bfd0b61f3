package handlers

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/internal/httputil"
	"github.com/olzzhas/edunite-server/gateway/pkg/validator"
	"strconv"

	"github.com/olzzhas/edunite-server/gateway/clients"
	"net/http"
)

type UserHandler struct {
	RabbitLogPublisher clients.LogPublisher
	UserService        *clients.UserClient
}

// GetUserByID handles the request to get a user by ID
func (h *UserHandler) GetUserByID(c *gin.Context) {
	// Parse and validate the user ID from the URL parameter
	userIDStr := c.Param("id")
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil || userID <= 0 {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Invalid user ID format: %s", userIDStr),
			"user_fetch",
			map[string]any{"error": "invalid user_id"},
		)
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "invalid user_id"})
		return
	}

	// Call the user service to get the user by ID with the request context
	user, err := h.UserService.GetUserWithContext(c.Request.Context(), userID)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error fetching user with ID %d: %v", userID, err),
			"user_fetch",
			map[string]any{"user_id": userID, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to fetch user"})
		return
	}

	// Return the user data
	c.JSON(http.StatusOK, gin.H{"user": user})
}

// UpdateUser handles the request to update a user
func (h *UserHandler) UpdateUser(c *gin.Context) {
	// Parse and validate the user ID from the URL parameter
	userIDStr := c.Param("id")
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil || userID <= 0 {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Invalid user ID format: %s", userIDStr),
			"user_update",
			map[string]any{"error": "invalid user_id"},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid user_id"})
		return
	}

	// Parse the request body
	var req struct {
		Name    string `json:"name"`
		Surname string `json:"surname"`
		Email   string `json:"email"`
		Role    string `json:"role"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Invalid request body: %v", err),
			"user_update",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// Validate role if provided
	if req.Role != "" {
		if !validator.PermittedValue(req.Role, "student", "teacher", "moderator", "admin") {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Invalid role: %s", req.Role),
				"user_update",
				map[string]any{"error": "invalid role", "role": req.Role},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid role"})
			return
		}
	}

	// Call the user service to update the user
	user, err := h.UserService.UpdateUser(
		c.Request.Context(),
		userID,
		req.Name,
		req.Surname,
		req.Email,
		req.Role,
	)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error updating user with ID %d: %v", userID, err),
			"user_update",
			map[string]any{"user_id": userID, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update user"})
		return
	}

	// Return the updated user data
	c.JSON(http.StatusOK, gin.H{"user": user})
}

func (h *UserHandler) GetAllUsers(c *gin.Context) {
	/* ---------- 1. Читаем query-параметры ---------- */

	v := validator.New()

	page := httputil.ReadInt(c, "page", 1, v)
	pageSize := httputil.ReadInt(c, "page_size", 20, v)
	sort := httputil.ReadString(c, "sort", "id", v)
	search := httputil.ReadString(c, "search", "", v)
	role := httputil.ReadString(c, "role", "", v)

	/* ---------- 2. Валидируем пагинацию/сортировку и роль ---------- */

	f := validator.Filters{
		Page:     page,
		PageSize: pageSize,
		Sort:     sort,
		SortSafelist: []string{ // разрешённые поля
			"id", "name", "surname", "email",
			"created_at", "-created_at",
		},
	}

	v.Check(f.Page > 0, "page", "must be greater than zero")
	v.Check(f.Page <= 10_000_000, "page", "must be a maximum of 10 million")

	// page_size: 1 … 100
	v.Check(f.PageSize > 0, "page_size", "must be greater than zero")
	v.Check(f.PageSize <= 100, "page_size", "must be a maximum of 100")

	// sort — одно из разрешённых значений
	v.Check(
		validator.PermittedValue(f.Sort, f.SortSafelist...),
		"sort",
		"invalid sort value",
	)

	// Validate role if provided
	if role != "" {
		v.Check(
			validator.PermittedValue(role, "student", "teacher", "moderator", "admin"),
			"role",
			"invalid role value",
		)
	}

	if !v.Valid() {
		c.JSON(http.StatusUnprocessableEntity, gin.H{"error": v.Errors})
		return
	}

	/* ---------- 3. Запрашиваем сервис пользователей ---------- */

	resp, err := h.UserService.GetAllUsers(
		c, // передаём context из Gin
		clients.ListFilters{
			Page:     int32(page),
			PageSize: int32(pageSize),
			Sort:     sort,
			Search:   search,
			Role:     role,
		},
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	/* ---------- 4. Отдаём результат ---------- */

	c.JSON(http.StatusOK, resp) // { metadata: {...}, users: [...] }
}
