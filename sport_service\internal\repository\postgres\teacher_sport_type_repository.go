package postgres

import (
	"context"
	"errors"
	"fmt"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

type teacherSportTypeRepository struct {
	db *pgxpool.Pool
}

func (r *teacherSportTypeRepository) List(ctx context.Context, filter domain.TeacherSportTypeFilter) ([]*domain.TeacherSportType, error) {
	//TODO implement me
	panic("implement me")
}

func (r *teacherSportTypeRepository) Count(ctx context.Context, filter domain.TeacherSportTypeFilter) (int, error) {
	//TODO implement me
	panic("implement me")
}

// NewTeacherSportTypeRepository creates a new teacher sport type repository
func NewTeacherSportTypeRepository(db *pgxpool.Pool) *teacherSportTypeRepository {
	return &teacherSportTypeRepository{db: db}
}

// Create creates a new teacher-sport type relationship
func (r *teacherSportTypeRepository) Create(ctx context.Context, teacherSportType *domain.TeacherSportType) error {
	query := `
		INSERT INTO teacher_sport_types (teacher_id, sport_type_id, can_review_certificates)
		VALUES ($1, $2, $3)
		RETURNING created_at, updated_at
	`

	err := r.db.QueryRow(ctx, query,
		teacherSportType.TeacherID,
		teacherSportType.SportTypeID,
		teacherSportType.CanReviewCertificates,
	).Scan(
		&teacherSportType.CreatedAt,
		&teacherSportType.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to create teacher sport type: %w", err)
	}

	return nil
}

// GetByTeacherAndSportType retrieves a teacher-sport type relationship by teacher ID and sport type ID
func (r *teacherSportTypeRepository) GetByTeacherAndSportType(ctx context.Context, teacherID, sportTypeID int64) (*domain.TeacherSportType, error) {
	query := `
		SELECT teacher_id, sport_type_id, can_review_certificates, created_at, updated_at
		FROM teacher_sport_types
		WHERE teacher_id = $1 AND sport_type_id = $2
	`

	var teacherSportType domain.TeacherSportType
	err := r.db.QueryRow(ctx, query, teacherID, sportTypeID).Scan(
		&teacherSportType.TeacherID,
		&teacherSportType.SportTypeID,
		&teacherSportType.CanReviewCertificates,
		&teacherSportType.CreatedAt,
		&teacherSportType.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("teacher sport type not found")
		}
		return nil, fmt.Errorf("failed to get teacher sport type: %w", err)
	}

	return &teacherSportType, nil
}

// Update updates an existing teacher-sport type relationship
func (r *teacherSportTypeRepository) Update(ctx context.Context, teacherSportType *domain.TeacherSportType) error {
	query := `
		UPDATE teacher_sport_types
		SET can_review_certificates = $1
		WHERE teacher_id = $2 AND sport_type_id = $3
		RETURNING updated_at
	`

	err := r.db.QueryRow(ctx, query,
		teacherSportType.CanReviewCertificates,
		teacherSportType.TeacherID,
		teacherSportType.SportTypeID,
	).Scan(
		&teacherSportType.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return fmt.Errorf("teacher sport type not found")
		}
		return fmt.Errorf("failed to update teacher sport type: %w", err)
	}

	return nil
}

// Delete deletes a teacher-sport type relationship
func (r *teacherSportTypeRepository) Delete(ctx context.Context, teacherID, sportTypeID int64) error {
	query := `
		DELETE FROM teacher_sport_types
		WHERE teacher_id = $1 AND sport_type_id = $2
	`

	result, err := r.db.Exec(ctx, query, teacherID, sportTypeID)
	if err != nil {
		return fmt.Errorf("failed to delete teacher sport type: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("teacher sport type not found")
	}

	return nil
}

// ListByTeacher retrieves all sport types for a teacher
func (r *teacherSportTypeRepository) ListByTeacher(ctx context.Context, teacherID int64) ([]*domain.TeacherSportType, error) {
	query := `
		SELECT teacher_id, sport_type_id, can_review_certificates, created_at, updated_at
		FROM teacher_sport_types
		WHERE teacher_id = $1
		ORDER BY sport_type_id
	`

	rows, err := r.db.Query(ctx, query, teacherID)
	if err != nil {
		return nil, fmt.Errorf("failed to list teacher sport types: %w", err)
	}
	defer rows.Close()

	var teacherSportTypes []*domain.TeacherSportType
	for rows.Next() {
		var teacherSportType domain.TeacherSportType
		err := rows.Scan(
			&teacherSportType.TeacherID,
			&teacherSportType.SportTypeID,
			&teacherSportType.CanReviewCertificates,
			&teacherSportType.CreatedAt,
			&teacherSportType.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan teacher sport type: %w", err)
		}
		teacherSportTypes = append(teacherSportTypes, &teacherSportType)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating teacher sport types: %w", err)
	}

	return teacherSportTypes, nil
}

// ListBySportType retrieves all teachers for a sport type
func (r *teacherSportTypeRepository) ListBySportType(ctx context.Context, sportTypeID int64) ([]*domain.TeacherSportType, error) {
	query := `
		SELECT teacher_id, sport_type_id, can_review_certificates, created_at, updated_at
		FROM teacher_sport_types
		WHERE sport_type_id = $1
		ORDER BY teacher_id
	`

	rows, err := r.db.Query(ctx, query, sportTypeID)
	if err != nil {
		return nil, fmt.Errorf("failed to list teacher sport types: %w", err)
	}
	defer rows.Close()

	var teacherSportTypes []*domain.TeacherSportType
	for rows.Next() {
		var teacherSportType domain.TeacherSportType
		err := rows.Scan(
			&teacherSportType.TeacherID,
			&teacherSportType.SportTypeID,
			&teacherSportType.CanReviewCertificates,
			&teacherSportType.CreatedAt,
			&teacherSportType.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan teacher sport type: %w", err)
		}
		teacherSportTypes = append(teacherSportTypes, &teacherSportType)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating teacher sport types: %w", err)
	}

	return teacherSportTypes, nil
}

// CanTeacherReviewCertificates checks if a teacher can review certificates for a sport type
func (r *teacherSportTypeRepository) CanTeacherReviewCertificates(ctx context.Context, teacherID, sportTypeID int64) (bool, error) {
	query := `
		SELECT can_review_certificates
		FROM teacher_sport_types
		WHERE teacher_id = $1 AND sport_type_id = $2
	`

	var canReview bool
	err := r.db.QueryRow(ctx, query, teacherID, sportTypeID).Scan(&canReview)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return false, nil
		}
		return false, fmt.Errorf("failed to check if teacher can review certificates: %w", err)
	}

	return canReview, nil
}
