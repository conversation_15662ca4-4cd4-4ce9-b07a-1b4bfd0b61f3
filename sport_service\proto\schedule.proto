syntax = "proto3";

package sportpb;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

option go_package = "github.com/olzzhas/edunite-server/sport_service/pb/sportpb";

service ScheduleService {
  // Create a new schedule
  rpc CreateSchedule(CreateScheduleRequest) returns (ScheduleResponse) {}
  
  // Get a schedule by ID
  rpc GetSchedule(GetScheduleRequest) returns (ScheduleResponse) {}
  
  // Update an existing schedule
  rpc UpdateSchedule(UpdateScheduleRequest) returns (ScheduleResponse) {}
  
  // Delete a schedule
  rpc DeleteSchedule(DeleteScheduleRequest) returns (google.protobuf.Empty) {}
  
  // List schedules with filtering
  rpc ListSchedules(ListSchedulesRequest) returns (ListSchedulesResponse) {}
  
  // Create schedules from a weekly template
  rpc CreateWeeklySchedules(CreateWeeklySchedulesRequest) returns (CreateWeeklySchedulesResponse) {}
  
  // Get schedules for a teacher
  rpc GetSchedulesForTeacher(GetSchedulesForTeacherRequest) returns (ListSchedulesResponse) {}
  
  // Get schedules for a facility
  rpc GetSchedulesForFacility(GetSchedulesForFacilityRequest) returns (ListSchedulesResponse) {}
  
  // Get schedules for a semester
  rpc GetSchedulesForSemester(GetSchedulesForSemesterRequest) returns (ListSchedulesResponse) {}
}

// Create schedule request
message CreateScheduleRequest {
  int64 facility_id = 1;
  int64 teacher_id = 2;
  int64 semester_id = 3;
  google.protobuf.Timestamp start_time = 4;
  google.protobuf.Timestamp end_time = 5;
  google.protobuf.Timestamp cancellation_deadline = 6;
  string location = 7;
}

// Get schedule request
message GetScheduleRequest {
  int64 id = 1;
}

// Update schedule request
message UpdateScheduleRequest {
  int64 id = 1;
  int64 facility_id = 2;
  int64 teacher_id = 3;
  int64 semester_id = 4;
  google.protobuf.Timestamp start_time = 5;
  google.protobuf.Timestamp end_time = 6;
  google.protobuf.Timestamp cancellation_deadline = 7;
  string location = 8;
  int32 version = 9;
}

// Delete schedule request
message DeleteScheduleRequest {
  int64 id = 1;
}

// List schedules request
message ListSchedulesRequest {
  int64 facility_id = 1;
  int64 teacher_id = 2;
  int64 semester_id = 3;
  google.protobuf.Timestamp start_date = 4;
  google.protobuf.Timestamp end_date = 5;
  string location = 6;
  int32 page = 7;
  int32 page_size = 8;
}

// Schedule response
message ScheduleResponse {
  int64 id = 1;
  int64 facility_id = 2;
  int64 teacher_id = 3;
  int64 semester_id = 4;
  google.protobuf.Timestamp start_time = 5;
  google.protobuf.Timestamp end_time = 6;
  google.protobuf.Timestamp cancellation_deadline = 7;
  string location = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  int32 version = 11;
  
  // Additional information
  FacilityInfo facility = 12;
  TeacherInfo teacher = 13;
  SemesterInfo semester = 14;
  int32 available_spots = 15;
  int32 total_spots = 16;
}

// List schedules response
message ListSchedulesResponse {
  repeated ScheduleResponse schedules = 1;
  int32 total = 2;
  int32 page = 3;
  int32 page_size = 4;
}

// Create weekly schedules request
message CreateWeeklySchedulesRequest {
  int64 facility_id = 1;
  int64 teacher_id = 2;
  int64 semester_id = 3;
  int64 sport_type_id = 4;
  int32 day_of_week = 5; // 1=Monday, 7=Sunday
  string start_time = 6; // Format: "HH:MM:SS"
  string end_time = 7; // Format: "HH:MM:SS"
  string location = 8;
  google.protobuf.Timestamp start_date = 9; // First occurrence
  google.protobuf.Timestamp end_date = 10; // Last occurrence
}

// Create weekly schedules response
message CreateWeeklySchedulesResponse {
  repeated ScheduleResponse schedules = 1;
  int32 count = 2;
}

// Get schedules for teacher request
message GetSchedulesForTeacherRequest {
  int64 teacher_id = 1;
  google.protobuf.Timestamp start_date = 2;
  google.protobuf.Timestamp end_date = 3;
}

// Get schedules for facility request
message GetSchedulesForFacilityRequest {
  int64 facility_id = 1;
  google.protobuf.Timestamp start_date = 2;
  google.protobuf.Timestamp end_date = 3;
}

// Get schedules for semester request
message GetSchedulesForSemesterRequest {
  int64 semester_id = 1;
}

// Facility info (simplified)
message FacilityInfo {
  int64 id = 1;
  string title = 2;
  int32 max_capacity = 3;
}

// Teacher info (simplified)
message TeacherInfo {
  int64 id = 1;
  string name = 2;
}

// Semester info (simplified)
message SemesterInfo {
  int64 id = 1;
  string name = 2;
  google.protobuf.Timestamp start_date = 3;
  google.protobuf.Timestamp end_date = 4;
}
