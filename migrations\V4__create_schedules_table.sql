-- 1) Д<PERSON><PERSON><PERSON><PERSON>ляем в threads поле max_students
ALTER TABLE threads
    ADD COLUMN IF NOT EXISTS max_students INT NOT NULL DEFAULT 0;

-- 2) Создадим тип для статуса посещаемости (CockroachDB compatible)
CREATE TYPE attendance_status AS ENUM (
  'unmarked',   -- не отмечали
  'present',    -- пришёл
  'absent',     -- не пришёл
  'excused'     -- с уважительной причиной
);

-- 3) Таблица расписания занятий по неделям (шаблон)
CREATE TABLE IF NOT EXISTS thread_schedules (
    id            BIGSERIAL PRIMARY KEY,
    thread_id     BIGINT      NOT NULL REFERENCES threads(id) ON DELETE CASCADE,
    day_of_week   SMALLINT    NOT NULL CHECK (day_of_week BETWEEN 1 AND 7),  -- 1=понедельник … 7=воскресенье
    start_time    TIME        NOT NULL,
    end_time      TIME        NOT NULL,
    created_at    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- триггер на updated_at для thread_schedules
CREATE TRIGGER trg_thread_schedules_updated
    BEFORE UPDATE ON thread_schedules
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

-- 4) Таблица посещаемости
CREATE TABLE IF NOT EXISTS attendance (
    id               BIGSERIAL         PRIMARY KEY,
    thread_id        BIGINT            NOT NULL REFERENCES threads(id) ON DELETE CASCADE,
    user_id          BIGINT            NOT NULL REFERENCES users(id)   ON DELETE CASCADE,
    attendance_date  DATE              NOT NULL,
    status           attendance_status NOT NULL DEFAULT 'unmarked',
    reason           TEXT,            -- причина, если status = 'excused'
    created_at       TIMESTAMP         NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at       TIMESTAMP         NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(thread_id, user_id, attendance_date)
);

-- триггер на updated_at для attendance
CREATE TRIGGER trg_attendance_updated
    BEFORE UPDATE ON attendance
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();
