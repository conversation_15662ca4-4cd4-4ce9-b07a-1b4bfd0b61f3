package database

import (
	"context"
	"fmt"

	"github.com/olzzhas/edunite-server/user_service/pkg/validator"

	"errors"
	"time"

	"github.com/jackc/pgx/v4/pgxpool"
)

var (
	ErrInvalidRole = errors.New("invalid role")
)

// UserRole represents possible user roles
type UserRole string

const (
	RoleStudent   UserRole = "student"
	RoleTeacher   UserRole = "teacher"
	RoleModerator UserRole = "moderator"
	RoleAdmin     UserRole = "admin"
)

// ValidateRole checks if the role is valid
func ValidateRole(role UserRole) bool {
	switch role {
	case RoleStudent, RoleTeacher, RoleModerator, RoleAdmin:
		return true
	default:
		return false
	}
}

func ValidateFilters(v *validator.Validator, f validator.Filters) {
	// page: 1 … 10 000 000
	v.Check(f.Page > 0, "page", "must be greater than zero")
	v.Check(f.Page <= 10_000_000, "page", "must be a maximum of 10 million")

	// page_size: 1 … 100
	v.Check(f.PageSize > 0, "page_size", "must be greater than zero")
	v.Check(f.PageSize <= 100, "page_size", "must be a maximum of 100")

	// sort — одно из разрешённых значений
	v.Check(
		validator.PermittedValue(f.Sort, f.SortSafelist...),
		"sort",
		"invalid sort value",
	)
}

// User структура пользователя
type User struct {
	ID           int64     `json:"id"`
	KeycloakID   *string   `json:"keycloak_id,omitempty"`
	Name         string    `json:"name"`
	Surname      string    `json:"surname"`
	Email        string    `json:"email"`
	Username     *string   `json:"username,omitempty"`
	PasswordHash *string   `json:"password_hash,omitempty"`
	Role         UserRole  `json:"role"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	Version      int       `json:"version"`
}

// UserRepository интерфейс для работы с таблицей users
type UserRepository interface {
	CreateUser(ctx context.Context, u *User) error
	GetUserByKeycloakID(ctx context.Context, keycloakID string) (*User, error)
	GetUserByID(ctx context.Context, id int64) (*User, error)
	GetUserByEmail(ctx context.Context, email string) (*User, error)
	GetAllUsers(ctx context.Context, search string, f validator.Filters, role string) ([]User, validator.Metadata, error)
	UpdateUser(ctx context.Context, u *User) error
	DeleteUser(ctx context.Context, id int64) error
}

type userRepository struct {
	db *pgxpool.Pool
}

// NewUserRepository создает новый экземпляр userRepository
func NewUserRepository(db *pgxpool.Pool) UserRepository {
	return &userRepository{db: db}
}

// CreateUser создает нового пользователя в базе
func (r *userRepository) CreateUser(ctx context.Context, user *User) error {
	if !ValidateRole(user.Role) {
		return ErrInvalidRole
	}

	now := time.Now()

	// If we're creating a user from auth service, keycloak_id will be nil
	if user.KeycloakID == nil {
		_, err := r.db.Exec(
			ctx,
			`INSERT INTO users (name, surname, email, username, password_hash, role, created_at, updated_at, version)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
			user.Name, user.Surname, user.Email, user.Username, user.PasswordHash,
			string(user.Role), now, now, 1,
		)
		return err
	}

	// For backward compatibility with existing code
	_, err := r.db.Exec(
		ctx,
		`INSERT INTO users (keycloak_id, name, surname, email, username, password_hash, role, created_at, updated_at, version)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
		user.KeycloakID, user.Name, user.Surname, user.Email, user.Username, user.PasswordHash,
		string(user.Role), now, now, 1,
	)
	return err
}

// GetUserByKeycloakID находит пользователя по keycloak_id
func (r *userRepository) GetUserByKeycloakID(ctx context.Context, keycloakID string) (*User, error) {
	var user User
	err := r.db.QueryRow(
		ctx,
		`SELECT id, keycloak_id, name, surname, email, username, password_hash, role, created_at, updated_at, version
		FROM users WHERE keycloak_id=$1`,
		keycloakID,
	).Scan(&user.ID, &user.KeycloakID, &user.Name, &user.Surname, &user.Email, &user.Username, &user.PasswordHash,
		&user.Role, &user.CreatedAt, &user.UpdatedAt, &user.Version)
	return &user, err
}

// GetUserByID находит пользователя по id
func (r *userRepository) GetUserByID(ctx context.Context, id int64) (*User, error) {
	var user User
	err := r.db.QueryRow(
		ctx,
		`SELECT id, keycloak_id, name, surname, email, username, password_hash, role, created_at, updated_at, version
		FROM users WHERE id=$1`,
		id,
	).Scan(&user.ID, &user.KeycloakID, &user.Name, &user.Surname, &user.Email, &user.Username, &user.PasswordHash,
		&user.Role, &user.CreatedAt, &user.UpdatedAt, &user.Version)
	return &user, err
}

// GetUserByEmail находит пользователя по email
func (r *userRepository) GetUserByEmail(ctx context.Context, email string) (*User, error) {
	var user User
	err := r.db.QueryRow(
		ctx,
		`SELECT id, keycloak_id, name, surname, email, username, password_hash, role, created_at, updated_at, version
		FROM users WHERE email=$1`,
		email,
	).Scan(&user.ID, &user.KeycloakID, &user.Name, &user.Surname, &user.Email, &user.Username, &user.PasswordHash,
		&user.Role, &user.CreatedAt, &user.UpdatedAt, &user.Version)
	return &user, err
}

// GetAllUsers возвращает список всех пользователей с фильтрацией по роли
func (r *userRepository) GetAllUsers(ctx context.Context, search string,
	f validator.Filters, role string) ([]User,
	validator.Metadata, error) {

	var query string
	var args []interface{}

	if role == "" {
		// Query without role filter
		query = fmt.Sprintf(`
			SELECT count(*) OVER(), id, keycloak_id, name, surname, email, username, password_hash, role,
				created_at, updated_at, version
			FROM   users
			WHERE  ($1 = '' OR
					name ILIKE '%%' || $1 || '%%' OR
					surname ILIKE '%%' || $1 || '%%' OR
					email ILIKE '%%' || $1 || '%%')
			ORDER  BY %s %s, id ASC
			LIMIT  $2 OFFSET $3`, f.SortColumn(), f.SortDirection())

		args = []interface{}{search, f.Limit(), f.Offset()}
	} else {
		// Query with role filter
		query = fmt.Sprintf(`
			SELECT count(*) OVER(), id, keycloak_id, name, surname, email, username, password_hash, role,
				created_at, updated_at, version
			FROM   users
			WHERE  ($1 = '' OR
					name ILIKE '%%' || $1 || '%%' OR
					surname ILIKE '%%' || $1 || '%%' OR
					email ILIKE '%%' || $1 || '%%')
				AND role = $4
			ORDER  BY %s %s, id ASC
			LIMIT  $2 OFFSET $3`, f.SortColumn(), f.SortDirection())

		args = []interface{}{search, f.Limit(), f.Offset(), role}
	}

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, validator.Metadata{}, err
	}

	total := 0
	var users []User
	for rows.Next() {
		var u User
		if err := rows.Scan(&total, &u.ID, &u.KeycloakID, &u.Name,
			&u.Surname, &u.Email, &u.Username, &u.PasswordHash, &u.Role,
			&u.CreatedAt, &u.UpdatedAt, &u.Version); err != nil {
			return nil, validator.Metadata{}, err
		}
		users = append(users, u)
	}
	meta := validator.NewMetadata(total, f.Page, f.PageSize)
	return users, meta, nil
}

// UpdateUser обновляет информацию о пользователе
func (r *userRepository) UpdateUser(ctx context.Context, user *User) error {
	if !ValidateRole(user.Role) {
		return ErrInvalidRole
	}

	_, err := r.db.Exec(
		ctx,
		`UPDATE users
         SET name=$1, surname=$2, email=$3, username=$4, password_hash=$5, role=$6, updated_at=$7, version=version+1
         WHERE id=$8`,
		user.Name, user.Surname, user.Email, user.Username, user.PasswordHash, user.Role, time.Now(), user.ID,
	)
	return err
}

// DeleteUser удаляет пользователя по id
func (r *userRepository) DeleteUser(ctx context.Context, id int64) error {
	_, err := r.db.Exec(ctx, `DELETE FROM users WHERE id=$1`, id)
	return err
}
