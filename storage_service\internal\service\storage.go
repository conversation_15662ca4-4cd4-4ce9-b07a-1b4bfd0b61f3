package service

import (
	"context"

	"github.com/olzzhas/edunite-server/storage_service/internal/database"
	"github.com/olzzhas/edunite-server/storage_service/pb"
	"google.golang.org/protobuf/types/known/emptypb"
)

// StorageRepository is a common interface for storage repositories
type StorageRepository interface {
	Upload(ctx context.Context, bucketName, objectName string, data []byte, contentType string) (string, error)
	Download(ctx context.Context, bucketName, objectName string) ([]byte, string, error)
	Delete(ctx context.Context, bucketName, objectName string) error
}

// Ensure that MinioRepository, S3Repository, AWSS3Repository, and LocalFSRepository implement StorageRepository
var _ StorageRepository = (database.MinioRepository)(nil)
var _ StorageRepository = (database.S3Repository)(nil)
var _ StorageRepository = (database.AWSS3Repository)(nil)
var _ StorageRepository = (database.LocalFSRepository)(nil)

type StorageService struct {
	pb.UnimplementedStorageServiceServer
	repo StorageRepository
}

// NewStorageService creates a new StorageService with MinIO repository
func NewStorageService(minioRepo database.MinioRepository) *StorageService {
	return &StorageService{repo: minioRepo}
}

// NewStorageServiceWithS3 creates a new StorageService with S3 repository
func NewStorageServiceWithS3(s3Repo database.S3Repository) *StorageService {
	return &StorageService{repo: s3Repo}
}

// NewStorageServiceWithAWSS3 creates a new StorageService with AWS S3 repository
func NewStorageServiceWithAWSS3(awsS3Repo database.AWSS3Repository) *StorageService {
	return &StorageService{repo: awsS3Repo}
}

// NewStorageServiceWithLocalFS creates a new StorageService with local file system repository
func NewStorageServiceWithLocalFS(localFSRepo database.LocalFSRepository) *StorageService {
	return &StorageService{repo: localFSRepo}
}

// UploadFile RPC
func (s *StorageService) UploadFile(ctx context.Context, req *pb.UploadFileRequest) (*pb.UploadFileResponse, error) {
	fileURL, err := s.repo.Upload(ctx, req.BucketName, req.ObjectName, req.FileData, req.ContentType)
	if err != nil {
		return nil, err
	}
	return &pb.UploadFileResponse{
		FileUrl: fileURL,
	}, nil
}

// DownloadFile RPC
func (s *StorageService) DownloadFile(ctx context.Context, req *pb.DownloadFileRequest) (*pb.DownloadFileResponse, error) {
	data, contentType, err := s.repo.Download(ctx, req.BucketName, req.ObjectName)
	if err != nil {
		return nil, err
	}
	return &pb.DownloadFileResponse{
		ContentType: contentType,
		FileData:    data,
	}, nil
}

// DeleteFile RPC
func (s *StorageService) DeleteFile(ctx context.Context, req *pb.DeleteFileRequest) (*emptypb.Empty, error) {
	if err := s.repo.Delete(ctx, req.BucketName, req.ObjectName); err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
