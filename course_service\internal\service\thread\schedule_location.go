package thread

import (
	"context"
	"errors"
	"github.com/olzzhas/edunite-server/course_service/internal/database"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// UpdateScheduleLocation updates the location field for a schedule
// This is a workaround since we can't include the location field in the protobuf messages
// without regenerating the code
func (s *Service) UpdateScheduleLocation(
	ctx context.Context, scheduleID int64, location string,
) error {
	// Get the existing schedule
	existing, err := s.scheduleRepo.GetByID(ctx, scheduleID)
	if err != nil {
		if errors.Is(err, database.ErrScheduleNotFound) {
			return status.Errorf(codes.NotFound, "schedule not found")
		}
		return status.Errorf(codes.Internal, "fetch schedule: %v", err)
	}

	// Update the location field
	existing.Location = location

	// Save the changes
	if err := s.scheduleRepo.Update(ctx, existing); err != nil {
		return status.Errorf(codes.Internal, "update schedule location: %v", err)
	}

	return nil
}

// GetScheduleLocation returns the location field for a schedule
func (s *Service) GetScheduleLocation(
	ctx context.Context, scheduleID int64,
) (string, error) {
	// Get the existing schedule
	existing, err := s.scheduleRepo.GetByID(ctx, scheduleID)
	if err != nil {
		if errors.Is(err, database.ErrScheduleNotFound) {
			return "", status.Errorf(codes.NotFound, "schedule not found")
		}
		return "", status.Errorf(codes.Internal, "fetch schedule: %v", err)
	}

	return existing.Location, nil
}
