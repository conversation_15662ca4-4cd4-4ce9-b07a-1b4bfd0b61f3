package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

// SetupLocationRoutes sets up routes for location-related endpoints
func SetupLocationRoutes(r *gin.Engine, locationHandler *handlers.LocationHandler) {
	locationGroup := r.Group("/locations")
	{
		locationGroup.POST("", locationHandler.CreateLocationHandler)
		locationGroup.GET("", locationHandler.ListLocationsHandler)
		locationGroup.GET("/:id", locationHandler.GetLocationByIDHandler)
		locationGroup.PUT("/:id", locationHandler.UpdateLocationHandler)
		locationGroup.DELETE("/:id", locationHandler.DeleteLocationHandler)
	}
}
