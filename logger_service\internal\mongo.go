package internal

import (
	"context"
	"errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"log"
)

var (
	ErrNoLogsFound = errors.New("no logs found")
)

func ConnectMongoDB(uri string) (*mongo.Database, error) {
	client, err := mongo.Connect(context.Background(), options.Client().ApplyURI(uri))
	if err != nil {
		return nil, err
	}
	return client.Database("logs"), nil
}

func SaveLogToMongo(db *mongo.Database, logData interface{}) error {
	collection := db.Collection("logs")
	_, err := collection.InsertOne(context.Background(), logData)
	if err != nil {
		log.Printf("Failed to save log to MongoDB: %v", err)
		return err
	}
	return nil
}

// GetLogs retrieves logs from MongoDB based on filter criteria
func GetLogs(ctx context.Context, db *mongo.Database, filter LogFilter) ([]LogEntry, error) {
	collection := db.Collection("logs")

	// Build the filter
	query := bson.M{}

	if filter.Level != "" {
		query["level"] = filter.Level
	}

	if filter.Service != "" {
		query["service"] = filter.Service
	}

	// Date range filter
	if !filter.StartDate.IsZero() || !filter.EndDate.IsZero() {
		dateQuery := bson.M{}

		if !filter.StartDate.IsZero() {
			dateQuery["$gte"] = filter.StartDate
		}

		if !filter.EndDate.IsZero() {
			dateQuery["$lte"] = filter.EndDate
		}

		query["datetime"] = dateQuery
	}

	// Set default limit if not specified
	limit := int64(100)
	if filter.Limit > 0 {
		limit = filter.Limit
	}

	// Set default skip if not specified
	skip := int64(0)
	if filter.Skip > 0 {
		skip = filter.Skip
	}

	// Create options for sorting by datetime in descending order (newest first)
	findOptions := options.Find().
		SetSort(bson.D{{Key: "datetime", Value: -1}}).
		SetLimit(limit).
		SetSkip(skip)

	// Execute the query
	cursor, err := collection.Find(ctx, query, findOptions)
	if err != nil {
		log.Printf("Failed to query logs from MongoDB: %v", err)
		return nil, err
	}
	defer cursor.Close(ctx)

	// Decode the results
	var logs []LogEntry
	if err := cursor.All(ctx, &logs); err != nil {
		log.Printf("Failed to decode logs from MongoDB: %v", err)
		return nil, err
	}

	if len(logs) == 0 {
		return nil, ErrNoLogsFound
	}

	return logs, nil
}

// GetLogByID retrieves a specific log by its ID
func GetLogByID(ctx context.Context, db *mongo.Database, id string) (*LogEntry, error) {
	collection := db.Collection("logs")

	var log LogEntry
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}

	err = collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&log)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, ErrNoLogsFound
		}
		return nil, err
	}

	return &log, nil
}

// CountLogs counts the number of logs matching the filter criteria
func CountLogs(ctx context.Context, db *mongo.Database, filter LogFilter) (int64, error) {
	collection := db.Collection("logs")

	// Build the filter
	query := bson.M{}

	if filter.Level != "" {
		query["level"] = filter.Level
	}

	if filter.Service != "" {
		query["service"] = filter.Service
	}

	// Date range filter
	if !filter.StartDate.IsZero() || !filter.EndDate.IsZero() {
		dateQuery := bson.M{}

		if !filter.StartDate.IsZero() {
			dateQuery["$gte"] = filter.StartDate
		}

		if !filter.EndDate.IsZero() {
			dateQuery["$lte"] = filter.EndDate
		}

		query["datetime"] = dateQuery
	}

	// Count documents
	count, err := collection.CountDocuments(ctx, query)
	if err != nil {
		log.Printf("Failed to count logs from MongoDB: %v", err)
		return 0, err
	}

	return count, nil
}

// GetLogLevels retrieves all unique log levels from the logs collection
func GetLogLevels(ctx context.Context, db *mongo.Database) ([]string, error) {
	collection := db.Collection("logs")

	// Use distinct command to get unique values
	levels, err := collection.Distinct(ctx, "level", bson.M{})
	if err != nil {
		log.Printf("Failed to get distinct log levels: %v", err)
		return nil, err
	}

	// Convert interface{} slice to string slice
	result := make([]string, 0, len(levels))
	for _, level := range levels {
		if strLevel, ok := level.(string); ok {
			result = append(result, strLevel)
		}
	}

	return result, nil
}

// GetLogServices retrieves all unique service names from the logs collection
func GetLogServices(ctx context.Context, db *mongo.Database) ([]string, error) {
	collection := db.Collection("logs")

	// Use distinct command to get unique values
	services, err := collection.Distinct(ctx, "service", bson.M{})
	if err != nil {
		log.Printf("Failed to get distinct service names: %v", err)
		return nil, err
	}

	// Convert interface{} slice to string slice
	result := make([]string, 0, len(services))
	for _, service := range services {
		if strService, ok := service.(string); ok {
			result = append(result, strService)
		}
	}

	return result, nil
}
