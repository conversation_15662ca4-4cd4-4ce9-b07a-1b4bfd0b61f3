package database

import (
	"context"
	"fmt"
	"mime"
	"net/url"
	"os"
	"path/filepath"

	"github.com/olzzhas/edunite-server/storage_service/internal/config"
)

// LocalFSRepository is the interface for local file system operations
type LocalFSRepository interface {
	Upload(ctx context.Context, bucketName, objectName string, data []byte, contentType string) (string, error)
	Download(ctx context.Context, bucketName, objectName string) ([]byte, string, error)
	Delete(ctx context.Context, bucketName, objectName string) error
}

// localFSRepository is the implementation of LocalFSRepository
type localFSRepository struct {
	config     *config.Config
	baseDir    string
	publicPath string
}

// NewLocalFSRepository creates a new local file system repository
func NewLocalFSRepository(cfg *config.Config) (LocalFSRepository, error) {
	// Use a base directory for storage
	baseDir := "/app/storage"

	// Create the base directory if it doesn't exist
	if err := os.MkdirAll(baseDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create base directory: %w", err)
	}

	// Public path for accessing files
	publicPath := "http://localhost:8081/storage"

	return &localFSRepository{
		config:     cfg,
		baseDir:    baseDir,
		publicPath: publicPath,
	}, nil
}

// Upload uploads a file to the specified bucket
func (r *localFSRepository) Upload(ctx context.Context, bucketName, objectName string, data []byte, contentType string) (string, error) {
	// Create the bucket directory if it doesn't exist
	bucketDir := filepath.Join(r.baseDir, bucketName)
	if err := os.MkdirAll(bucketDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create bucket directory: %w", err)
	}

	// Create the full path for the file
	fullPath := filepath.Join(bucketDir, objectName)

	// Create the directory for the file if it doesn't exist
	fileDir := filepath.Dir(fullPath)
	if err := os.MkdirAll(fileDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create file directory: %w", err)
	}

	// Write the file
	if err := os.WriteFile(fullPath, data, 0644); err != nil {
		return "", fmt.Errorf("failed to write file: %w", err)
	}

	// Generate the URL for the uploaded file
	encodedBucket := url.PathEscape(bucketName)
	encodedObject := url.PathEscape(objectName)
	fileURL := fmt.Sprintf("%s/photo/%s/%s", r.publicPath, encodedBucket, encodedObject)

	return fileURL, nil
}

// Download downloads a file from the specified bucket
func (r *localFSRepository) Download(ctx context.Context, bucketName, objectName string) ([]byte, string, error) {
	// Get the full path for the file
	fullPath := filepath.Join(r.baseDir, bucketName, objectName)

	// Read the file
	data, err := os.ReadFile(fullPath)
	if err != nil {
		return nil, "", fmt.Errorf("failed to read file: %w", err)
	}

	// Determine the content type
	contentType := mime.TypeByExtension(filepath.Ext(objectName))
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	return data, contentType, nil
}

// Delete deletes a file from the specified bucket
func (r *localFSRepository) Delete(ctx context.Context, bucketName, objectName string) error {
	// Get the full path for the file
	fullPath := filepath.Join(r.baseDir, bucketName, objectName)

	// Delete the file
	if err := os.Remove(fullPath); err != nil {
		return fmt.Errorf("failed to delete file: %w", err)
	}

	return nil
}
