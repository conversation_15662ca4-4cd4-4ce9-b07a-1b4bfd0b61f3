package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

// SetupLogRoutes sets up routes for log-related endpoints
func SetupLogRoutes(r *gin.Engine, logHandler *handlers.LogHandler) {
	logGroup := r.Group("/api/logs")
	{
		// Get logs with filtering
		logGroup.GET("", logHandler.GetLogs)
		
		// Get log by ID
		logGroup.GET("/:id", logHandler.GetLogByID)
		
		// Count logs
		logGroup.GET("/count", logHandler.CountLogs)
		
		// Get unique log levels
		logGroup.GET("/levels", logHandler.GetLogLevels)
		
		// Get unique service names
		logGroup.GET("/services", logHandler.GetLogServices)
	}
}
