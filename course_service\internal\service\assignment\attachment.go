package assignment

import (
	"context"
	"errors"
	assignmentpb "github.com/olzzhas/edunite-server/course_service/pb/assignment"
	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/olzzhas/edunite-server/course_service/internal/database"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// CreateAssignmentAttachment (RPC)
func (s *Service) CreateAssignmentAttachment(ctx context.Context, req *assignmentpb.CreateAssignmentAttachmentRequest) (*assignmentpb.AssignmentAttachmentResponse, error) {
	v := validator.New()
	s.validateAttachmentInput(v, req.GetAssignmentId(), req.GetFileUrl())
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "%v", v.Errors)
	}

	att := &database.AssignmentAttachment{
		AssignmentID: req.GetAssignmentId(),
		FileURL:      req.GetFileUrl(),
	}
	if err := s.attachmentRepo.CreateAttachment(ctx, att); err != nil {
		return nil, status.Errorf(codes.Internal, "create attachment: %v", err)
	}
	return attachmentToPB(att), nil
}

// GetAssignmentAttachmentsByAssignmentID (RPC)
func (s *Service) GetAssignmentAttachmentsByAssignmentID(ctx context.Context, req *assignmentpb.AssignmentIDRequest) (*assignmentpb.AssignmentAttachmentsResponse, error) {

	if req.GetAssignmentId() <= 0 {
		return nil, status.Errorf(codes.InvalidArgument, "assignment_id must be > 0")
	}
	list, err := s.attachmentRepo.GetAttachmentsByAssignmentID(ctx, req.GetAssignmentId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "list attachments: %v", err)
	}

	resp := &assignmentpb.AssignmentAttachmentsResponse{}
	for _, a := range list {
		resp.Attachments = append(resp.Attachments, attachmentToPB(a))
	}
	return resp, nil
}

// DeleteAssignmentAttachmentByID (RPC)
func (s *Service) DeleteAssignmentAttachmentByID(ctx context.Context, req *assignmentpb.AssignmentAttachmentByIDRequest) (*assignmentpb.AssignmentEmptyResponse, error) {
	if req.GetAttachmentId() <= 0 {
		return nil, status.Errorf(codes.InvalidArgument, "attachment_id must be > 0")
	}
	if err := s.attachmentRepo.DeleteAttachment(ctx, req.GetAttachmentId()); err != nil {
		if errors.Is(err, database.ErrAttachmentNotFound) {
			return nil, status.Errorf(codes.NotFound, "attachment not found")
		}
		return nil, status.Errorf(codes.Internal, "delete attachment: %v", err)
	}
	return &assignmentpb.AssignmentEmptyResponse{}, nil
}

// ---------------------
// Вспомогательная функция
// ---------------------
func attachmentToPB(a *database.AssignmentAttachment) *assignmentpb.AssignmentAttachmentResponse {
	return &assignmentpb.AssignmentAttachmentResponse{
		Id:           a.ID,
		AssignmentId: a.AssignmentID,
		FileUrl:      a.FileURL,
		CreatedAt:    timestamppb.New(a.CreatedAt),
		UpdatedAt:    timestamppb.New(a.UpdatedAt),
	}
}

func (s *Service) validateAttachmentInput(v *validator.Validator,
	assignmentID int64, url string) {

	v.Check(assignmentID > 0, "assignment_id", "must be > 0")
	v.Check(url != "", "file_url", "must be provided")
	v.Check(len(url) <= 500, "file_url", "max length is 500")
}
