package service

import (
	"context"
	"errors"
	"time"

	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
	"github.com/olzzhas/edunite-server/sport_service/internal/repository"
)

// MedicalCertificateService handles operations related to medical certificates
type MedicalCertificateService struct {
	medicalCertificateRepo repository.MedicalCertificateRepository
	teacherSportTypeRepo   repository.TeacherSportTypeRepository
}

// NewMedicalCertificateService creates a new medical certificate service
func NewMedicalCertificateService(
	medicalCertificateRepo repository.MedicalCertificateRepository,
	teacherSportTypeRepo repository.TeacherSportTypeRepository,
) *MedicalCertificateService {
	return &MedicalCertificateService{
		medicalCertificateRepo: medicalCertificateRepo,
		teacherSportTypeRepo:   teacherSportTypeRepo,
	}
}

// UploadCertificate uploads a new medical certificate for a user
func (s *MedicalCertificateService) UploadCertificate(ctx context.Context, userID int64, fileURL string, validFrom, validUntil time.Time) (*domain.MedicalCertificate, error) {
	// Validate dates
	now := time.Now()
	if validFrom.After(validUntil) {
		return nil, errors.New("valid from date must be before valid until date")
	}
	if validUntil.Before(now) {
		return nil, errors.New("certificate has already expired")
	}

	// Create the certificate
	certificate := &domain.MedicalCertificate{
		UserID:     userID,
		FileURL:    fileURL,
		Status:     domain.CertificateStatusPending,
		ValidFrom:  validFrom,
		ValidUntil: validUntil,
	}

	if err := s.medicalCertificateRepo.Create(ctx, certificate); err != nil {
		return nil, err
	}

	return certificate, nil
}

// ApproveCertificate approves a medical certificate
func (s *MedicalCertificateService) ApproveCertificate(ctx context.Context, certificateID, reviewerID int64, validUntil time.Time) error {
	// Check if the reviewer has permission to approve certificates
	// This would typically involve checking if the reviewer is a teacher with the LFK-approver role
	// For simplicity, we'll assume the check is done at the API level

	// Get the certificate
	certificate, err := s.medicalCertificateRepo.GetByID(ctx, certificateID)
	if err != nil {
		return err
	}

	// Check if the certificate is already approved or rejected
	if certificate.Status != domain.CertificateStatusPending {
		return errors.New("certificate is not in pending status")
	}

	// Approve the certificate
	return s.medicalCertificateRepo.ApproveCertificate(ctx, certificateID, reviewerID, validUntil)
}

// RejectCertificate rejects a medical certificate
func (s *MedicalCertificateService) RejectCertificate(ctx context.Context, certificateID, reviewerID int64, reason string) error {
	// Check if the reviewer has permission to reject certificates
	// This would typically involve checking if the reviewer is a teacher with the LFK-approver role
	// For simplicity, we'll assume the check is done at the API level

	// Get the certificate
	certificate, err := s.medicalCertificateRepo.GetByID(ctx, certificateID)
	if err != nil {
		return err
	}

	// Check if the certificate is already approved or rejected
	if certificate.Status != domain.CertificateStatusPending {
		return errors.New("certificate is not in pending status")
	}

	// Reject the certificate
	return s.medicalCertificateRepo.RejectCertificate(ctx, certificateID, reviewerID, reason)
}

// GetUserCertificates retrieves a user's medical certificates
func (s *MedicalCertificateService) GetUserCertificates(ctx context.Context, userID int64) ([]*domain.MedicalCertificate, error) {
	filter := domain.MedicalCertificateFilter{
		UserID: userID,
	}
	return s.medicalCertificateRepo.List(ctx, filter)
}

// GetPendingCertificates retrieves pending medical certificates for review
func (s *MedicalCertificateService) GetPendingCertificates(ctx context.Context, page, pageSize int) ([]*domain.MedicalCertificate, int, error) {
	certificates, err := s.medicalCertificateRepo.ListPendingCertificates(ctx, page, pageSize)
	if err != nil {
		return nil, 0, err
	}

	count, err := s.medicalCertificateRepo.CountPendingCertificates(ctx)
	if err != nil {
		return nil, 0, err
	}

	return certificates, count, nil
}

// HasValidCertificate checks if a user has a valid medical certificate
func (s *MedicalCertificateService) HasValidCertificate(ctx context.Context, userID int64) (bool, error) {
	return s.medicalCertificateRepo.HasValidCertificate(ctx, userID, time.Now())
}

func (s *MedicalCertificateService) GetLatestCertificate(ctx context.Context, userID int64) (*domain.MedicalCertificate, error) {
	return s.medicalCertificateRepo.GetLatestByUserID(ctx, userID)
}

func (s *MedicalCertificateService) GetCertificate(ctx context.Context, certificateID int64) (*domain.MedicalCertificate, error) {
	return s.medicalCertificateRepo.GetByID(ctx, certificateID)
}

func (s *MedicalCertificateService) GetPendingCertificatesCount(ctx context.Context) (int, error) {
	return s.medicalCertificateRepo.CountPendingCertificates(ctx)
}
