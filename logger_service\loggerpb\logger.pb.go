// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: loggerpb/logger.proto

package loggerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Existing messages
type LogEventRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Level         string                 `protobuf:"bytes,1,opt,name=level,proto3" json:"level,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ServiceName   string                 `protobuf:"bytes,3,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	Data          map[string]string      `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogEventRequest) Reset() {
	*x = LogEventRequest{}
	mi := &file_loggerpb_logger_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogEventRequest) ProtoMessage() {}

func (x *LogEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_loggerpb_logger_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogEventRequest.ProtoReflect.Descriptor instead.
func (*LogEventRequest) Descriptor() ([]byte, []int) {
	return file_loggerpb_logger_proto_rawDescGZIP(), []int{0}
}

func (x *LogEventRequest) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *LogEventRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *LogEventRequest) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *LogEventRequest) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

type LogEventResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogEventResponse) Reset() {
	*x = LogEventResponse{}
	mi := &file_loggerpb_logger_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogEventResponse) ProtoMessage() {}

func (x *LogEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_loggerpb_logger_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogEventResponse.ProtoReflect.Descriptor instead.
func (*LogEventResponse) Descriptor() ([]byte, []int) {
	return file_loggerpb_logger_proto_rawDescGZIP(), []int{1}
}

func (x *LogEventResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *LogEventResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// New messages for log fetching
type LogEntry struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Level         string                 `protobuf:"bytes,2,opt,name=level,proto3" json:"level,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	Service       string                 `protobuf:"bytes,4,opt,name=service,proto3" json:"service,omitempty"`
	Data          map[string]string      `protobuf:"bytes,5,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Datetime      *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=datetime,proto3" json:"datetime,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogEntry) Reset() {
	*x = LogEntry{}
	mi := &file_loggerpb_logger_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogEntry) ProtoMessage() {}

func (x *LogEntry) ProtoReflect() protoreflect.Message {
	mi := &file_loggerpb_logger_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogEntry.ProtoReflect.Descriptor instead.
func (*LogEntry) Descriptor() ([]byte, []int) {
	return file_loggerpb_logger_proto_rawDescGZIP(), []int{2}
}

func (x *LogEntry) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LogEntry) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *LogEntry) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *LogEntry) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *LogEntry) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *LogEntry) GetDatetime() *timestamppb.Timestamp {
	if x != nil {
		return x.Datetime
	}
	return nil
}

type GetLogsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Level         string                 `protobuf:"bytes,1,opt,name=level,proto3" json:"level,omitempty"`
	Service       string                 `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Limit         int64                  `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	Skip          int64                  `protobuf:"varint,6,opt,name=skip,proto3" json:"skip,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLogsRequest) Reset() {
	*x = GetLogsRequest{}
	mi := &file_loggerpb_logger_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLogsRequest) ProtoMessage() {}

func (x *GetLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_loggerpb_logger_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLogsRequest.ProtoReflect.Descriptor instead.
func (*GetLogsRequest) Descriptor() ([]byte, []int) {
	return file_loggerpb_logger_proto_rawDescGZIP(), []int{3}
}

func (x *GetLogsRequest) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *GetLogsRequest) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *GetLogsRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetLogsRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *GetLogsRequest) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetLogsRequest) GetSkip() int64 {
	if x != nil {
		return x.Skip
	}
	return 0
}

type GetLogsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Logs          []*LogEntry            `protobuf:"bytes,1,rep,name=logs,proto3" json:"logs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLogsResponse) Reset() {
	*x = GetLogsResponse{}
	mi := &file_loggerpb_logger_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLogsResponse) ProtoMessage() {}

func (x *GetLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_loggerpb_logger_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLogsResponse.ProtoReflect.Descriptor instead.
func (*GetLogsResponse) Descriptor() ([]byte, []int) {
	return file_loggerpb_logger_proto_rawDescGZIP(), []int{4}
}

func (x *GetLogsResponse) GetLogs() []*LogEntry {
	if x != nil {
		return x.Logs
	}
	return nil
}

type GetLogByIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLogByIDRequest) Reset() {
	*x = GetLogByIDRequest{}
	mi := &file_loggerpb_logger_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLogByIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLogByIDRequest) ProtoMessage() {}

func (x *GetLogByIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_loggerpb_logger_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLogByIDRequest.ProtoReflect.Descriptor instead.
func (*GetLogByIDRequest) Descriptor() ([]byte, []int) {
	return file_loggerpb_logger_proto_rawDescGZIP(), []int{5}
}

func (x *GetLogByIDRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type CountLogsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Count         int64                  `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountLogsResponse) Reset() {
	*x = CountLogsResponse{}
	mi := &file_loggerpb_logger_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountLogsResponse) ProtoMessage() {}

func (x *CountLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_loggerpb_logger_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountLogsResponse.ProtoReflect.Descriptor instead.
func (*CountLogsResponse) Descriptor() ([]byte, []int) {
	return file_loggerpb_logger_proto_rawDescGZIP(), []int{6}
}

func (x *CountLogsResponse) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetLogLevelsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLogLevelsRequest) Reset() {
	*x = GetLogLevelsRequest{}
	mi := &file_loggerpb_logger_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLogLevelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLogLevelsRequest) ProtoMessage() {}

func (x *GetLogLevelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_loggerpb_logger_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLogLevelsRequest.ProtoReflect.Descriptor instead.
func (*GetLogLevelsRequest) Descriptor() ([]byte, []int) {
	return file_loggerpb_logger_proto_rawDescGZIP(), []int{7}
}

type GetLogLevelsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Levels        []string               `protobuf:"bytes,1,rep,name=levels,proto3" json:"levels,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLogLevelsResponse) Reset() {
	*x = GetLogLevelsResponse{}
	mi := &file_loggerpb_logger_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLogLevelsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLogLevelsResponse) ProtoMessage() {}

func (x *GetLogLevelsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_loggerpb_logger_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLogLevelsResponse.ProtoReflect.Descriptor instead.
func (*GetLogLevelsResponse) Descriptor() ([]byte, []int) {
	return file_loggerpb_logger_proto_rawDescGZIP(), []int{8}
}

func (x *GetLogLevelsResponse) GetLevels() []string {
	if x != nil {
		return x.Levels
	}
	return nil
}

type GetLogServicesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLogServicesRequest) Reset() {
	*x = GetLogServicesRequest{}
	mi := &file_loggerpb_logger_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLogServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLogServicesRequest) ProtoMessage() {}

func (x *GetLogServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_loggerpb_logger_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLogServicesRequest.ProtoReflect.Descriptor instead.
func (*GetLogServicesRequest) Descriptor() ([]byte, []int) {
	return file_loggerpb_logger_proto_rawDescGZIP(), []int{9}
}

type GetLogServicesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Services      []string               `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLogServicesResponse) Reset() {
	*x = GetLogServicesResponse{}
	mi := &file_loggerpb_logger_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLogServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLogServicesResponse) ProtoMessage() {}

func (x *GetLogServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_loggerpb_logger_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLogServicesResponse.ProtoReflect.Descriptor instead.
func (*GetLogServicesResponse) Descriptor() ([]byte, []int) {
	return file_loggerpb_logger_proto_rawDescGZIP(), []int{10}
}

func (x *GetLogServicesResponse) GetServices() []string {
	if x != nil {
		return x.Services
	}
	return nil
}

var File_loggerpb_logger_proto protoreflect.FileDescriptor

const file_loggerpb_logger_proto_rawDesc = "" +
	"\n" +
	"\x15loggerpb/logger.proto\x12\bloggerpb\x1a\x1fgoogle/protobuf/timestamp.proto\"\xd6\x01\n" +
	"\x0fLogEventRequest\x12\x14\n" +
	"\x05level\x18\x01 \x01(\tR\x05level\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12!\n" +
	"\fservice_name\x18\x03 \x01(\tR\vserviceName\x127\n" +
	"\x04data\x18\x04 \x03(\v2#.loggerpb.LogEventRequest.DataEntryR\x04data\x1a7\n" +
	"\tDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"F\n" +
	"\x10LogEventResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x87\x02\n" +
	"\bLogEntry\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05level\x18\x02 \x01(\tR\x05level\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\x12\x18\n" +
	"\aservice\x18\x04 \x01(\tR\aservice\x120\n" +
	"\x04data\x18\x05 \x03(\v2\x1c.loggerpb.LogEntry.DataEntryR\x04data\x126\n" +
	"\bdatetime\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\bdatetime\x1a7\n" +
	"\tDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xdc\x01\n" +
	"\x0eGetLogsRequest\x12\x14\n" +
	"\x05level\x18\x01 \x01(\tR\x05level\x12\x18\n" +
	"\aservice\x18\x02 \x01(\tR\aservice\x129\n" +
	"\n" +
	"start_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\x12\x14\n" +
	"\x05limit\x18\x05 \x01(\x03R\x05limit\x12\x12\n" +
	"\x04skip\x18\x06 \x01(\x03R\x04skip\"9\n" +
	"\x0fGetLogsResponse\x12&\n" +
	"\x04logs\x18\x01 \x03(\v2\x12.loggerpb.LogEntryR\x04logs\"#\n" +
	"\x11GetLogByIDRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\")\n" +
	"\x11CountLogsResponse\x12\x14\n" +
	"\x05count\x18\x01 \x01(\x03R\x05count\"\x15\n" +
	"\x13GetLogLevelsRequest\".\n" +
	"\x14GetLogLevelsResponse\x12\x16\n" +
	"\x06levels\x18\x01 \x03(\tR\x06levels\"\x17\n" +
	"\x15GetLogServicesRequest\"4\n" +
	"\x16GetLogServicesResponse\x12\x1a\n" +
	"\bservices\x18\x01 \x03(\tR\bservices2\xc5\x03\n" +
	"\rLoggerService\x12C\n" +
	"\bLogEvent\x12\x19.loggerpb.LogEventRequest\x1a\x1a.loggerpb.LogEventResponse\"\x00\x12@\n" +
	"\aGetLogs\x12\x18.loggerpb.GetLogsRequest\x1a\x19.loggerpb.GetLogsResponse\"\x00\x12?\n" +
	"\n" +
	"GetLogByID\x12\x1b.loggerpb.GetLogByIDRequest\x1a\x12.loggerpb.LogEntry\"\x00\x12D\n" +
	"\tCountLogs\x12\x18.loggerpb.GetLogsRequest\x1a\x1b.loggerpb.CountLogsResponse\"\x00\x12O\n" +
	"\fGetLogLevels\x12\x1d.loggerpb.GetLogLevelsRequest\x1a\x1e.loggerpb.GetLogLevelsResponse\"\x00\x12U\n" +
	"\x0eGetLogServices\x12\x1f.loggerpb.GetLogServicesRequest\x1a .loggerpb.GetLogServicesResponse\"\x00B\vZ\t/loggerpbb\x06proto3"

var (
	file_loggerpb_logger_proto_rawDescOnce sync.Once
	file_loggerpb_logger_proto_rawDescData []byte
)

func file_loggerpb_logger_proto_rawDescGZIP() []byte {
	file_loggerpb_logger_proto_rawDescOnce.Do(func() {
		file_loggerpb_logger_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_loggerpb_logger_proto_rawDesc), len(file_loggerpb_logger_proto_rawDesc)))
	})
	return file_loggerpb_logger_proto_rawDescData
}

var file_loggerpb_logger_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_loggerpb_logger_proto_goTypes = []any{
	(*LogEventRequest)(nil),        // 0: loggerpb.LogEventRequest
	(*LogEventResponse)(nil),       // 1: loggerpb.LogEventResponse
	(*LogEntry)(nil),               // 2: loggerpb.LogEntry
	(*GetLogsRequest)(nil),         // 3: loggerpb.GetLogsRequest
	(*GetLogsResponse)(nil),        // 4: loggerpb.GetLogsResponse
	(*GetLogByIDRequest)(nil),      // 5: loggerpb.GetLogByIDRequest
	(*CountLogsResponse)(nil),      // 6: loggerpb.CountLogsResponse
	(*GetLogLevelsRequest)(nil),    // 7: loggerpb.GetLogLevelsRequest
	(*GetLogLevelsResponse)(nil),   // 8: loggerpb.GetLogLevelsResponse
	(*GetLogServicesRequest)(nil),  // 9: loggerpb.GetLogServicesRequest
	(*GetLogServicesResponse)(nil), // 10: loggerpb.GetLogServicesResponse
	nil,                            // 11: loggerpb.LogEventRequest.DataEntry
	nil,                            // 12: loggerpb.LogEntry.DataEntry
	(*timestamppb.Timestamp)(nil),  // 13: google.protobuf.Timestamp
}
var file_loggerpb_logger_proto_depIdxs = []int32{
	11, // 0: loggerpb.LogEventRequest.data:type_name -> loggerpb.LogEventRequest.DataEntry
	12, // 1: loggerpb.LogEntry.data:type_name -> loggerpb.LogEntry.DataEntry
	13, // 2: loggerpb.LogEntry.datetime:type_name -> google.protobuf.Timestamp
	13, // 3: loggerpb.GetLogsRequest.start_date:type_name -> google.protobuf.Timestamp
	13, // 4: loggerpb.GetLogsRequest.end_date:type_name -> google.protobuf.Timestamp
	2,  // 5: loggerpb.GetLogsResponse.logs:type_name -> loggerpb.LogEntry
	0,  // 6: loggerpb.LoggerService.LogEvent:input_type -> loggerpb.LogEventRequest
	3,  // 7: loggerpb.LoggerService.GetLogs:input_type -> loggerpb.GetLogsRequest
	5,  // 8: loggerpb.LoggerService.GetLogByID:input_type -> loggerpb.GetLogByIDRequest
	3,  // 9: loggerpb.LoggerService.CountLogs:input_type -> loggerpb.GetLogsRequest
	7,  // 10: loggerpb.LoggerService.GetLogLevels:input_type -> loggerpb.GetLogLevelsRequest
	9,  // 11: loggerpb.LoggerService.GetLogServices:input_type -> loggerpb.GetLogServicesRequest
	1,  // 12: loggerpb.LoggerService.LogEvent:output_type -> loggerpb.LogEventResponse
	4,  // 13: loggerpb.LoggerService.GetLogs:output_type -> loggerpb.GetLogsResponse
	2,  // 14: loggerpb.LoggerService.GetLogByID:output_type -> loggerpb.LogEntry
	6,  // 15: loggerpb.LoggerService.CountLogs:output_type -> loggerpb.CountLogsResponse
	8,  // 16: loggerpb.LoggerService.GetLogLevels:output_type -> loggerpb.GetLogLevelsResponse
	10, // 17: loggerpb.LoggerService.GetLogServices:output_type -> loggerpb.GetLogServicesResponse
	12, // [12:18] is the sub-list for method output_type
	6,  // [6:12] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_loggerpb_logger_proto_init() }
func file_loggerpb_logger_proto_init() {
	if File_loggerpb_logger_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_loggerpb_logger_proto_rawDesc), len(file_loggerpb_logger_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_loggerpb_logger_proto_goTypes,
		DependencyIndexes: file_loggerpb_logger_proto_depIdxs,
		MessageInfos:      file_loggerpb_logger_proto_msgTypes,
	}.Build()
	File_loggerpb_logger_proto = out.File
	file_loggerpb_logger_proto_goTypes = nil
	file_loggerpb_logger_proto_depIdxs = nil
}
