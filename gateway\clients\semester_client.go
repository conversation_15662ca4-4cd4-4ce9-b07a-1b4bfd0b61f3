package clients

import (
	"context"
	semesterpb "github.com/olzzhas/edunite-server/course_service/pb/semester"

	"google.golang.org/grpc"
)

type SemesterClient struct {
	client semesterpb.SemesterServiceClient
}

func NewSemesterClient(conn *grpc.ClientConn) *SemesterClient {
	return &SemesterClient{
		client: semesterpb.NewSemesterServiceClient(conn),
	}
}

// CreateSemester отправляет запрос на создание нового семестра
func (sc *SemesterClient) CreateSemester(ctx context.Context, req *semesterpb.SemesterRequest) (*semesterpb.SemesterResponse, error) {
	return sc.client.CreateSemester(ctx, req)
}

// ListSemesters отправляет запрос на получение списка всех семестров
func (sc *SemesterClient) ListSemesters(ctx context.Context, req *semesterpb.SemesterEmptyRequest) (*semesterpb.SemestersResponse, error) {
	return sc.client.GetAllSemesters(ctx, req)
}

// GetSemesterByID отправляет запрос на получение семестра по ID
func (sc *SemesterClient) GetSemesterByID(ctx context.Context, req *semesterpb.SemesterByID) (*semesterpb.SemesterResponse, error) {
	return sc.client.GetSemesterByID(ctx, req)
}

// UpdateSemester отправляет запрос на обновление семестра по ID
func (sc *SemesterClient) UpdateSemester(ctx context.Context, req *semesterpb.SemesterUpdateRequest) (*semesterpb.SemesterResponse, error) {
	return sc.client.UpdateSemester(ctx, req)
}

// DeleteSemester отправляет запрос на удаление семестра по ID
func (sc *SemesterClient) DeleteSemester(ctx context.Context, req *semesterpb.SemesterByID) (*semesterpb.SemesterEmptyResponse, error) {
	return sc.client.Delete(ctx, req)
}

// ListSemesterBreaks отправляет запрос на получение списка всех выходных дней семестра
func (sc *SemesterClient) ListSemesterBreaks(ctx context.Context, req *semesterpb.SemesterByID) (*semesterpb.SemesterBreaksResponse, error) {
	return sc.client.ListSemesterBreaks(ctx, req)
}

func (sc *SemesterClient) AddSemesterBreak(ctx context.Context, req *semesterpb.SemesterBreakRequest) (*semesterpb.SemesterBreakResponse, error) {
	return sc.client.AddSemesterBreak(ctx, req)
}

// GetAllSemestersWithBreaks получает список всех семестров и для каждого семестра получает его выходные дни
func (sc *SemesterClient) GetAllSemestersWithBreaks(ctx context.Context) (map[int64]*semesterpb.SemesterResponse, map[int64][]*semesterpb.SemesterBreakResponse, error) {
	// Получаем список всех семестров
	semestersResp, err := sc.ListSemesters(ctx, &semesterpb.SemesterEmptyRequest{})
	if err != nil {
		return nil, nil, err
	}

	// Создаем мапы для хранения семестров и их выходных дней
	semestersMap := make(map[int64]*semesterpb.SemesterResponse)
	breaksMap := make(map[int64][]*semesterpb.SemesterBreakResponse)

	// Заполняем мапу семестров
	for _, semester := range semestersResp.Semesters {
		semestersMap[semester.Id] = semester
	}

	// Для каждого семестра получаем его выходные дни
	for semesterID := range semestersMap {
		breaksResp, err := sc.ListSemesterBreaks(ctx, &semesterpb.SemesterByID{Id: semesterID})
		if err != nil {
			// Если произошла ошибка, просто продолжаем с пустым списком выходных для этого семестра
			breaksMap[semesterID] = []*semesterpb.SemesterBreakResponse{}
			continue
		}
		breaksMap[semesterID] = breaksResp.SemesterBreaks
	}

	return semestersMap, breaksMap, nil
}
