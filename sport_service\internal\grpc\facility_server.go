package grpc

import (
	"context"
	sportpb "github.com/olzzhas/edunite-server/sport_service/proto"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
	"github.com/olzzhas/edunite-server/sport_service/internal/service"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type facilityServer struct {
	sportpb.UnimplementedFacilityServiceServer
	services *service.Services
}

// NewFacilityServer creates a new facility gRPC server
func NewFacilityServer(services *service.Services) *facilityServer {
	return &facilityServer{
		services: services,
	}
}

// CreateFacility creates a new facility
func (s *facilityServer) CreateFacility(ctx context.Context, req *sportpb.CreateFacilityRequest) (*sportpb.FacilityResponse, error) {
	facility := &domain.Facility{
		Title:       req.Title,
		Description: req.Description,
		MaxCapacity: int(req.MaxCapacity),
	}

	// Create the facility
	err := s.services.PhysicalEducation.CreateFacility(ctx, facility)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to create facility: %v", err)
	}

	return convertFacilityToProto(facility), nil
}

// GetFacility retrieves a facility by ID
func (s *facilityServer) GetFacility(ctx context.Context, req *sportpb.GetFacilityRequest) (*sportpb.FacilityResponse, error) {
	facility, err := s.services.PhysicalEducation.GetFacility(ctx, req.Id)
	if err != nil {
		if err == domain.ErrFacilityNotFound {
			return nil, status.Errorf(codes.NotFound, "facility not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to get facility: %v", err)
	}

	return convertFacilityToProto(facility), nil
}

// UpdateFacility updates an existing facility
func (s *facilityServer) UpdateFacility(ctx context.Context, req *sportpb.UpdateFacilityRequest) (*sportpb.FacilityResponse, error) {
	facility := &domain.Facility{
		ID:          req.Id,
		Title:       req.Title,
		Description: req.Description,
		MaxCapacity: int(req.MaxCapacity),
		Version:     int32(req.Version),
	}

	// Update the facility
	err := s.services.PhysicalEducation.UpdateFacility(ctx, facility)
	if err != nil {
		if err == domain.ErrFacilityNotFound {
			return nil, status.Errorf(codes.NotFound, "facility not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to update facility: %v", err)
	}

	return convertFacilityToProto(facility), nil
}

// DeleteFacility deletes a facility
func (s *facilityServer) DeleteFacility(ctx context.Context, req *sportpb.DeleteFacilityRequest) (*empty.Empty, error) {
	err := s.services.PhysicalEducation.DeleteFacility(ctx, req.Id)
	if err != nil {
		if err == domain.ErrFacilityNotFound {
			return nil, status.Errorf(codes.NotFound, "facility not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to delete facility: %v", err)
	}

	return &emptypb.Empty{}, nil
}

// ListFacilities lists facilities with filtering
func (s *facilityServer) ListFacilities(ctx context.Context, req *sportpb.ListFacilitiesRequest) (*sportpb.ListFacilitiesResponse, error) {
	filter := domain.FacilityFilter{
		Title:    req.Title,
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
	}

	facilities, err := s.services.PhysicalEducation.ListFacilities(ctx, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list facilities: %v", err)
	}

	response := &sportpb.ListFacilitiesResponse{
		Total:    int32(len(facilities)),
		Page:     int32(filter.Page),
		PageSize: int32(filter.PageSize),
	}

	for _, facility := range facilities {
		response.Facilities = append(response.Facilities, convertFacilityToProto(facility))
	}

	return response, nil
}

// Helper function to convert domain.Facility to sportpb.FacilityResponse
func convertFacilityToProto(facility *domain.Facility) *sportpb.FacilityResponse {
	return &sportpb.FacilityResponse{
		Id:          facility.ID,
		Title:       facility.Title,
		Description: facility.Description,
		MaxCapacity: int32(facility.MaxCapacity),
		CreatedAt:   timestamppb.New(facility.CreatedAt),
		UpdatedAt:   timestamppb.New(facility.UpdatedAt),
		Version:     facility.Version,
	}
}
