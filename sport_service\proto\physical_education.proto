syntax = "proto3";

package sportpb;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "proto/sport_type.proto";
import "proto/facility.proto";
import "proto/schedule.proto";
import "proto/booking.proto";
import "proto/medical_certificate.proto";
import "proto/semester_limit.proto";

option go_package = "github.com/olzzhas/edunite-server/sport_service/pb/sportpb";

service PhysicalEducationService {
  // Get available sport types
  rpc GetAvailableSportTypes(GetAvailableSportTypesRequest) returns (ListSportTypesResponse) {}

  // Get available facilities
  rpc GetAvailableFacilities(GetAvailableFacilitiesRequest) returns (ListFacilitiesResponse) {}

  // Get available schedules
  rpc GetAvailableSchedules(GetAvailableSchedulesRequest) returns (ListSchedulesResponse) {}

  // Book a session
  rpc BookSession(BookSessionRequest) returns (BookingResponse) {}

  // Cancel a booking
  rpc CancelBooking(CancelBookingRequest) returns (BookingResponse) {}

  // Upload a medical certificate
  rpc UploadMedicalCertificate(UploadCertificateRequest) returns (MedicalCertificateResponse) {}

  // Get user bookings
  rpc GetUserBookings(GetUserBookingsRequest) returns (ListBookingsResponse) {}

  // Get user semester statistics
  rpc GetUserSemesterStats(GetUserSemesterStatsRequest) returns (UserSemesterStatsResponse) {}

  // Check if a user can book LFK
  rpc CheckUserCanBookLFK(CheckUserCanBookLFKRequest) returns (CheckUserCanBookLFKResponse) {}

  // Get semester sport limit
  rpc GetSemesterSportLimit(GetSemesterLimitBySemesterRequest) returns (SemesterLimitResponse) {}

  // Get daily booking limit
  rpc GetDailyBookingLimit(GetDailyBookingLimitBySemesterRequest) returns (DailyBookingLimitResponse) {}
}

// Get available sport types request
message GetAvailableSportTypesRequest {
  int32 page = 1;
  int32 page_size = 2;
}

// Get available facilities request
message GetAvailableFacilitiesRequest {
  int32 page = 1;
  int32 page_size = 2;
}

// Get available schedules request
message GetAvailableSchedulesRequest {
  int64 facility_id = 1;
  google.protobuf.Timestamp start_date = 2;
  google.protobuf.Timestamp end_date = 3;
  int32 page = 4;
  int32 page_size = 5;
}

// Book session request
message BookSessionRequest {
  int64 user_id = 1;
  int64 schedule_id = 2;
}

// Get user bookings request
message GetUserBookingsRequest {
  int64 user_id = 1;
  google.protobuf.Timestamp start_date = 2;
  google.protobuf.Timestamp end_date = 3;
  int32 page = 4;
  int32 page_size = 5;
}

// Check user can book LFK request
message CheckUserCanBookLFKRequest {
  int64 user_id = 1;
}

// Check user can book LFK response
message CheckUserCanBookLFKResponse {
  bool can_book = 1;
  string reason = 2;
}
