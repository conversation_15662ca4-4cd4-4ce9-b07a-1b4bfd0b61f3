package postgres

import (
	"context"
	"errors"
	"fmt"

	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

type semesterSportLimitRepository struct {
	db *pgxpool.Pool
}

func (r *semesterSportLimitRepository) GetUserSemesterStats(ctx context.Context, userID, semesterID int64) ([]*domain.UserSemesterStats, error) {
	//TODO implement me
	panic("implement me")
}

// NewSemesterSportLimitRepository creates a new semester sport limit repository
func NewSemesterSportLimitRepository(db *pgxpool.Pool) *semesterSportLimitRepository {
	return &semesterSportLimitRepository{db: db}
}

// Create creates a new semester sport limit
func (r *semesterSportLimitRepository) Create(ctx context.Context, limit *domain.SemesterSportLimit) error {
	query := `
		INSERT INTO semester_sport_limits (semester_id, min_lessons, max_lessons)
		VALUES ($1, $2, $3)
		RETURNING id, created_at, updated_at
	`

	err := r.db.QueryRow(ctx, query,
		limit.SemesterID,
		limit.MinLessons,
		limit.MaxLessons,
	).Scan(
		&limit.ID,
		&limit.CreatedAt,
		&limit.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to create semester sport limit: %w", err)
	}

	return nil
}

// GetByID retrieves a semester sport limit by ID
func (r *semesterSportLimitRepository) GetByID(ctx context.Context, id int64) (*domain.SemesterSportLimit, error) {
	query := `
		SELECT id, semester_id, min_lessons, max_lessons, created_at, updated_at
		FROM semester_sport_limits
		WHERE id = $1
	`

	var limit domain.SemesterSportLimit
	err := r.db.QueryRow(ctx, query, id).Scan(
		&limit.ID,
		&limit.SemesterID,
		&limit.MinLessons,
		&limit.MaxLessons,
		&limit.CreatedAt,
		&limit.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, domain.ErrSemesterSportLimitNotFound
		}
		return nil, fmt.Errorf("failed to get semester sport limit: %w", err)
	}

	return &limit, nil
}

// GetBySemesterID retrieves a semester sport limit by semester ID
func (r *semesterSportLimitRepository) GetBySemesterID(ctx context.Context, semesterID int64) (*domain.SemesterSportLimit, error) {
	query := `
		SELECT id, semester_id, min_lessons, max_lessons, created_at, updated_at
		FROM semester_sport_limits
		WHERE semester_id = $1
	`

	var limit domain.SemesterSportLimit
	err := r.db.QueryRow(ctx, query, semesterID).Scan(
		&limit.ID,
		&limit.SemesterID,
		&limit.MinLessons,
		&limit.MaxLessons,
		&limit.CreatedAt,
		&limit.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, domain.ErrSemesterSportLimitNotFound
		}
		return nil, fmt.Errorf("failed to get semester sport limit: %w", err)
	}

	return &limit, nil
}

// Update updates an existing semester sport limit
func (r *semesterSportLimitRepository) Update(ctx context.Context, limit *domain.SemesterSportLimit) error {
	query := `
		UPDATE semester_sport_limits
		SET min_lessons = $1, max_lessons = $2
		WHERE id = $3
		RETURNING updated_at
	`

	err := r.db.QueryRow(ctx, query,
		limit.MinLessons,
		limit.MaxLessons,
		limit.ID,
	).Scan(
		&limit.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return domain.ErrSemesterSportLimitNotFound
		}
		return fmt.Errorf("failed to update semester sport limit: %w", err)
	}

	return nil
}

// Delete deletes a semester sport limit by ID
func (r *semesterSportLimitRepository) Delete(ctx context.Context, id int64) error {
	query := `
		DELETE FROM semester_sport_limits
		WHERE id = $1
	`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete semester sport limit: %w", err)
	}

	if result.RowsAffected() == 0 {
		return domain.ErrSemesterSportLimitNotFound
	}

	return nil
}

// List retrieves all semester sport limits
func (r *semesterSportLimitRepository) List(ctx context.Context, filter domain.SemesterSportLimitFilter) ([]*domain.SemesterSportLimit, error) {
	query := `
		SELECT id, semester_id, min_lessons, max_lessons, created_at, updated_at
		FROM semester_sport_limits
		WHERE 1=1
	`

	var args []interface{}
	var conditions []string

	// Add filter conditions
	if filter.SemesterID != 0 {
		args = append(args, filter.SemesterID)
		conditions = append(conditions, fmt.Sprintf("AND semester_id = $%d", len(args)))
	}

	// Add pagination
	if filter.Page <= 0 {
		filter.Page = 1
	}

	if filter.PageSize <= 0 {
		filter.PageSize = 10
	}

	offset := (filter.Page - 1) * filter.PageSize

	for _, condition := range conditions {
		query += " " + condition
	}

	query += " ORDER BY semester_id"
	query += fmt.Sprintf(" LIMIT %d OFFSET %d", filter.PageSize, offset)

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to list semester sport limits: %w", err)
	}
	defer rows.Close()

	var limits []*domain.SemesterSportLimit
	for rows.Next() {
		var limit domain.SemesterSportLimit
		err := rows.Scan(
			&limit.ID,
			&limit.SemesterID,
			&limit.MinLessons,
			&limit.MaxLessons,
			&limit.CreatedAt,
			&limit.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan semester sport limit: %w", err)
		}
		limits = append(limits, &limit)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating semester sport limits: %w", err)
	}

	return limits, nil
}

// Count counts semester sport limits based on filters
func (r *semesterSportLimitRepository) Count(ctx context.Context, filter domain.SemesterSportLimitFilter) (int, error) {
	query := `
		SELECT COUNT(*)
		FROM semester_sport_limits
		WHERE 1=1
	`

	var args []interface{}
	var conditions []string

	// Add filter conditions
	if filter.SemesterID != 0 {
		args = append(args, filter.SemesterID)
		conditions = append(conditions, fmt.Sprintf("AND semester_id = $%d", len(args)))
	}

	for _, condition := range conditions {
		query += " " + condition
	}

	var count int
	err := r.db.QueryRow(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count semester sport limits: %w", err)
	}

	return count, nil
}
