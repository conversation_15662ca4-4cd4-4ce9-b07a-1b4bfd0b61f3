package postgres

import (
	"context"
	"errors"
	"fmt"

	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

type facilityRepository struct {
	db *pgxpool.Pool
}

func (r *facilityRepository) GetBySportType(ctx context.Context, sportTypeID int64) ([]*domain.Facility, error) {
	//TODO implement me
	panic("implement me")
}

// NewFacilityRepository creates a new facility repository
func NewFacilityRepository(db *pgxpool.Pool) *facilityRepository {
	return &facilityRepository{db: db}
}

// <PERSON><PERSON> creates a new facility
func (r *facilityRepository) Create(ctx context.Context, facility *domain.Facility) error {
	query := `
		INSERT INTO facilities (title, description, max_capacity)
		VALUES ($1, $2, $3)
		RETURNING id, created_at, updated_at, version
	`

	err := r.db.QueryRow(ctx, query,
		facility.Title,
		facility.Description,
		facility.MaxCapacity,
	).Scan(
		&facility.ID,
		&facility.CreatedAt,
		&facility.UpdatedAt,
		&facility.Version,
	)

	if err != nil {
		return fmt.Errorf("failed to create facility: %w", err)
	}

	return nil
}

// GetByID retrieves a facility by ID
func (r *facilityRepository) GetByID(ctx context.Context, id int64) (*domain.Facility, error) {
	query := `
		SELECT id, title, description, max_capacity, created_at, updated_at, version
		FROM facilities
		WHERE id = $1
	`

	var facility domain.Facility
	err := r.db.QueryRow(ctx, query, id).Scan(
		&facility.ID,
		&facility.Title,
		&facility.Description,
		&facility.MaxCapacity,
		&facility.CreatedAt,
		&facility.UpdatedAt,
		&facility.Version,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, domain.ErrFacilityNotFound
		}
		return nil, fmt.Errorf("failed to get facility: %w", err)
	}

	return &facility, nil
}

// Update updates an existing facility
func (r *facilityRepository) Update(ctx context.Context, facility *domain.Facility) error {
	query := `
		UPDATE facilities
		SET title = $1, description = $2, max_capacity = $3, version = version + 1
		WHERE id = $4 AND version = $5
		RETURNING updated_at, version
	`

	err := r.db.QueryRow(ctx, query,
		facility.Title,
		facility.Description,
		facility.MaxCapacity,
		facility.ID,
		facility.Version,
	).Scan(
		&facility.UpdatedAt,
		&facility.Version,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return domain.ErrFacilityNotFound
		}
		return fmt.Errorf("failed to update facility: %w", err)
	}

	return nil
}

// Delete deletes a facility by ID
func (r *facilityRepository) Delete(ctx context.Context, id int64) error {
	query := `
		DELETE FROM facilities
		WHERE id = $1
	`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete facility: %w", err)
	}

	if result.RowsAffected() == 0 {
		return domain.ErrFacilityNotFound
	}

	return nil
}

// List retrieves facilities based on filters
func (r *facilityRepository) List(ctx context.Context, filter domain.FacilityFilter) ([]*domain.Facility, error) {
	query := `
		SELECT id, title, description, max_capacity, created_at, updated_at, version
		FROM facilities
		WHERE 1=1
	`

	var args []interface{}
	var conditions []string

	// Add filter conditions
	if filter.Title != "" {
		args = append(args, "%"+filter.Title+"%")
		conditions = append(conditions, fmt.Sprintf("AND title ILIKE $%d", len(args)))
	}

	// Add pagination
	if filter.Page <= 0 {
		filter.Page = 1
	}

	if filter.PageSize <= 0 {
		filter.PageSize = 10
	}

	offset := (filter.Page - 1) * filter.PageSize

	for _, condition := range conditions {
		query += " " + condition
	}

	query += " ORDER BY id"
	query += fmt.Sprintf(" LIMIT %d OFFSET %d", filter.PageSize, offset)

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to list facilities: %w", err)
	}
	defer rows.Close()

	var facilities []*domain.Facility
	for rows.Next() {
		var facility domain.Facility
		err := rows.Scan(
			&facility.ID,
			&facility.Title,
			&facility.Description,
			&facility.MaxCapacity,
			&facility.CreatedAt,
			&facility.UpdatedAt,
			&facility.Version,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan facility: %w", err)
		}
		facilities = append(facilities, &facility)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating facilities: %w", err)
	}

	return facilities, nil
}

// Count counts facilities based on filters
func (r *facilityRepository) Count(ctx context.Context, filter domain.FacilityFilter) (int, error) {
	query := `
		SELECT COUNT(*)
		FROM facilities
		WHERE 1=1
	`

	var args []interface{}
	var conditions []string

	// Add filter conditions
	if filter.Title != "" {
		args = append(args, "%"+filter.Title+"%")
		conditions = append(conditions, fmt.Sprintf("AND title ILIKE $%d", len(args)))
	}

	for _, condition := range conditions {
		query += " " + condition
	}

	var count int
	err := r.db.QueryRow(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count facilities: %w", err)
	}

	return count, nil
}
