package postgres

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

type medicalCertificateRepository struct {
	db *pgxpool.Pool
}

// NewMedicalCertificateRepository creates a new medical certificate repository
func NewMedicalCertificateRepository(db *pgxpool.Pool) *medicalCertificateRepository {
	return &medicalCertificateRepository{db: db}
}

// C<PERSON> creates a new medical certificate
func (r *medicalCertificateRepository) Create(ctx context.Context, certificate *domain.MedicalCertificate) error {
	query := `
		INSERT INTO medical_certificates (user_id, file_url, status, valid_from, valid_until)
		VALUES ($1, $2, $3, $4, $5)
		RETURNING id, created_at, updated_at
	`

	err := r.db.QueryRow(ctx, query,
		certificate.UserID,
		certificate.FileURL,
		certificate.Status,
		certificate.ValidFrom,
		certificate.ValidUntil,
	).Scan(
		&certificate.ID,
		&certificate.CreatedAt,
		&certificate.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to create medical certificate: %w", err)
	}

	return nil
}

// GetByID retrieves a medical certificate by ID
func (r *medicalCertificateRepository) GetByID(ctx context.Context, id int64) (*domain.MedicalCertificate, error) {
	query := `
		SELECT id, user_id, file_url, status, reviewed_by, reject_reason, valid_from, valid_until, created_at, updated_at
		FROM medical_certificates
		WHERE id = $1
	`

	var certificate domain.MedicalCertificate
	var reviewedBy sql.NullInt64
	var rejectReason sql.NullString

	err := r.db.QueryRow(ctx, query, id).Scan(
		&certificate.ID,
		&certificate.UserID,
		&certificate.FileURL,
		&certificate.Status,
		&reviewedBy,
		&rejectReason,
		&certificate.ValidFrom,
		&certificate.ValidUntil,
		&certificate.CreatedAt,
		&certificate.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("medical certificate not found")
		}
		return nil, fmt.Errorf("failed to get medical certificate: %w", err)
	}

	if reviewedBy.Valid {
		certificate.ReviewedBy = reviewedBy.Int64
	}

	if rejectReason.Valid {
		certificate.RejectReason = rejectReason.String
	}

	return &certificate, nil
}

// Update updates an existing medical certificate
func (r *medicalCertificateRepository) Update(ctx context.Context, certificate *domain.MedicalCertificate) error {
	query := `
		UPDATE medical_certificates
		SET status = $1, reviewed_by = $2, reject_reason = $3, valid_from = $4, valid_until = $5
		WHERE id = $6
		RETURNING updated_at
	`

	var reviewedBy interface{} = nil
	if certificate.ReviewedBy != 0 {
		reviewedBy = certificate.ReviewedBy
	}

	var rejectReason interface{} = nil
	if certificate.RejectReason != "" {
		rejectReason = certificate.RejectReason
	}

	err := r.db.QueryRow(ctx, query,
		certificate.Status,
		reviewedBy,
		rejectReason,
		certificate.ValidFrom,
		certificate.ValidUntil,
		certificate.ID,
	).Scan(
		&certificate.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return fmt.Errorf("medical certificate not found")
		}
		return fmt.Errorf("failed to update medical certificate: %w", err)
	}

	return nil
}

// Delete deletes a medical certificate by ID
func (r *medicalCertificateRepository) Delete(ctx context.Context, id int64) error {
	query := `
		DELETE FROM medical_certificates
		WHERE id = $1
	`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete medical certificate: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("medical certificate not found")
	}

	return nil
}

// List retrieves medical certificates based on filters
func (r *medicalCertificateRepository) List(ctx context.Context, filter domain.MedicalCertificateFilter) ([]*domain.MedicalCertificate, error) {
	query := `
		SELECT id, user_id, file_url, status, reviewed_by, reject_reason, valid_from, valid_until, created_at, updated_at
		FROM medical_certificates
		WHERE 1=1
	`

	var args []interface{}
	var conditions []string

	// Add filter conditions
	if filter.UserID != 0 {
		args = append(args, filter.UserID)
		conditions = append(conditions, fmt.Sprintf("AND user_id = $%d", len(args)))
	}

	if filter.Status != "" {
		args = append(args, filter.Status)
		conditions = append(conditions, fmt.Sprintf("AND status = $%d", len(args)))
	}

	if !filter.ValidAt.IsZero() {
		args = append(args, filter.ValidAt)
		conditions = append(conditions, fmt.Sprintf("AND valid_from <= $%d AND valid_until >= $%d", len(args), len(args)))
	}

	// Add pagination
	if filter.Page <= 0 {
		filter.Page = 1
	}

	if filter.PageSize <= 0 {
		filter.PageSize = 10
	}

	offset := (filter.Page - 1) * filter.PageSize

	for _, condition := range conditions {
		query += " " + condition
	}

	query += " ORDER BY id DESC"
	query += fmt.Sprintf(" LIMIT %d OFFSET %d", filter.PageSize, offset)

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to list medical certificates: %w", err)
	}
	defer rows.Close()

	var certificates []*domain.MedicalCertificate
	for rows.Next() {
		var certificate domain.MedicalCertificate
		var reviewedBy sql.NullInt64
		var rejectReason sql.NullString

		err := rows.Scan(
			&certificate.ID,
			&certificate.UserID,
			&certificate.FileURL,
			&certificate.Status,
			&reviewedBy,
			&rejectReason,
			&certificate.ValidFrom,
			&certificate.ValidUntil,
			&certificate.CreatedAt,
			&certificate.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan medical certificate: %w", err)
		}

		if reviewedBy.Valid {
			certificate.ReviewedBy = reviewedBy.Int64
		}

		if rejectReason.Valid {
			certificate.RejectReason = rejectReason.String
		}

		certificates = append(certificates, &certificate)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating medical certificates: %w", err)
	}

	return certificates, nil
}

// Count counts medical certificates based on filters
func (r *medicalCertificateRepository) Count(ctx context.Context, filter domain.MedicalCertificateFilter) (int, error) {
	query := `
		SELECT COUNT(*)
		FROM medical_certificates
		WHERE 1=1
	`

	var args []interface{}
	var conditions []string

	// Add filter conditions
	if filter.UserID != 0 {
		args = append(args, filter.UserID)
		conditions = append(conditions, fmt.Sprintf("AND user_id = $%d", len(args)))
	}

	if filter.Status != "" {
		args = append(args, filter.Status)
		conditions = append(conditions, fmt.Sprintf("AND status = $%d", len(args)))
	}

	if !filter.ValidAt.IsZero() {
		args = append(args, filter.ValidAt)
		conditions = append(conditions, fmt.Sprintf("AND valid_from <= $%d AND valid_until >= $%d", len(args), len(args)))
	}

	for _, condition := range conditions {
		query += " " + condition
	}

	var count int
	err := r.db.QueryRow(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count medical certificates: %w", err)
	}

	return count, nil
}

// GetLatestByUserID retrieves the latest medical certificate for a user
func (r *medicalCertificateRepository) GetLatestByUserID(ctx context.Context, userID int64) (*domain.MedicalCertificate, error) {
	query := `
		SELECT id, user_id, file_url, status, reviewed_by, reject_reason, valid_from, valid_until, created_at, updated_at
		FROM medical_certificates
		WHERE user_id = $1
		ORDER BY id DESC
		LIMIT 1
	`

	var certificate domain.MedicalCertificate
	var reviewedBy sql.NullInt64
	var rejectReason sql.NullString

	err := r.db.QueryRow(ctx, query, userID).Scan(
		&certificate.ID,
		&certificate.UserID,
		&certificate.FileURL,
		&certificate.Status,
		&reviewedBy,
		&rejectReason,
		&certificate.ValidFrom,
		&certificate.ValidUntil,
		&certificate.CreatedAt,
		&certificate.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("medical certificate not found")
		}
		return nil, fmt.Errorf("failed to get latest medical certificate: %w", err)
	}

	if reviewedBy.Valid {
		certificate.ReviewedBy = reviewedBy.Int64
	}

	if rejectReason.Valid {
		certificate.RejectReason = rejectReason.String
	}

	return &certificate, nil
}

// HasValidCertificate checks if a user has a valid medical certificate at a specific time
func (r *medicalCertificateRepository) HasValidCertificate(ctx context.Context, userID int64, at time.Time) (bool, error) {
	query := `
		SELECT EXISTS (
			SELECT 1
			FROM medical_certificates
			WHERE user_id = $1
			AND status = 'approved'
			AND valid_from <= $2
			AND valid_until >= $2
		)
	`

	var exists bool
	err := r.db.QueryRow(ctx, query, userID, at).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("failed to check if user has valid certificate: %w", err)
	}

	return exists, nil
}

// ListPendingCertificates retrieves pending medical certificates
func (r *medicalCertificateRepository) ListPendingCertificates(ctx context.Context, page, pageSize int) ([]*domain.MedicalCertificate, error) {
	filter := domain.MedicalCertificateFilter{
		Status:   domain.CertificateStatusPending,
		Page:     page,
		PageSize: pageSize,
	}

	return r.List(ctx, filter)
}

// CountPendingCertificates counts pending medical certificates
func (r *medicalCertificateRepository) CountPendingCertificates(ctx context.Context) (int, error) {
	filter := domain.MedicalCertificateFilter{
		Status: domain.CertificateStatusPending,
	}

	return r.Count(ctx, filter)
}

// ApproveCertificate approves a medical certificate
func (r *medicalCertificateRepository) ApproveCertificate(ctx context.Context, id, reviewerID int64, validUntil time.Time) error {
	query := `
		UPDATE medical_certificates
		SET status = 'approved', reviewed_by = $1, valid_until = $2
		WHERE id = $3 AND status = 'pending'
		RETURNING updated_at
	`

	var updatedAt time.Time
	err := r.db.QueryRow(ctx, query, reviewerID, validUntil, id).Scan(&updatedAt)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return fmt.Errorf("medical certificate not found or not in pending status")
		}
		return fmt.Errorf("failed to approve medical certificate: %w", err)
	}

	return nil
}

// RejectCertificate rejects a medical certificate
func (r *medicalCertificateRepository) RejectCertificate(ctx context.Context, id, reviewerID int64, reason string) error {
	query := `
		UPDATE medical_certificates
		SET status = 'rejected', reviewed_by = $1, reject_reason = $2
		WHERE id = $3 AND status = 'pending'
		RETURNING updated_at
	`

	var updatedAt time.Time
	err := r.db.QueryRow(ctx, query, reviewerID, reason, id).Scan(&updatedAt)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return fmt.Errorf("medical certificate not found or not in pending status")
		}
		return fmt.Errorf("failed to reject medical certificate: %w", err)
	}

	return nil
}
