package domain

import (
	"time"
)

// SemesterSportLimit represents the global limits for all sport types in a semester
type SemesterSportLimit struct {
	ID          int64     `json:"id"`
	Semester<PERSON>  int64     `json:"semester_id"`
	MinLessons  int       `json:"min_lessons"`
	MaxLess<PERSON>  int       `json:"max_lessons"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// SemesterSportLimitFilter represents filters for querying semester sport limits
type SemesterSportLimitFilter struct {
	SemesterID  int64
	Page        int
	PageSize    int
}

// UserSemesterStats represents a user's statistics for a semester
type UserSemesterStats struct {
	UserID           int64     `json:"user_id"`
	SemesterID       int64     `json:"semester_id"`
	TotalBookings    int       `json:"total_bookings"`
	TotalAttendance  int       `json:"total_attendance"`
	AttendanceRate   float64   `json:"attendance_rate"`
}

// Error definitions for semester sport limit operations
var (
	ErrSemesterSportLimitNotFound = Error{"semester sport limit not found"}
	ErrDuplicateSemesterSportLimit = Error{"semester sport limit already exists"}
)
