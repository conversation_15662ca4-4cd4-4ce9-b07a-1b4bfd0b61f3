syntax = "proto3";

package semesterpb;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/olzzhas/edunite-server/course_service/pb/semester;semesterpb";

service SemesterService {
  // semester entity
  rpc CreateSemester(SemesterRequest) returns(SemesterResponse){}
  rpc GetSemesterByID(SemesterByID) returns(SemesterResponse){}
  rpc GetAllSemesters(SemesterEmptyRequest) returns(SemestersResponse){}
  rpc UpdateSemester(SemesterUpdateRequest) returns(SemesterResponse){}
  rpc Delete(SemesterByID) returns(SemesterEmptyResponse){}
  rpc AddSemesterBreak(SemesterBreakRequest) returns(SemesterBreakResponse){}
  rpc RemoveSemesterBreak(SemesterBreakByID) returns(SemesterBreakResponse){}
  rpc ListSemesterBreaks(SemesterByID) returns(SemesterBreaksResponse){}
}

//-------------------------------//
//       Semester Messages       //
//-------------------------------//
message SemesterRequest {
  string name = 1;
  google.protobuf.Timestamp start_date = 2;
  google.protobuf.Timestamp end_date = 3;
}

message SemesterUpdateRequest {
  int64 id = 1;
  string name = 2;
  google.protobuf.Timestamp start_date = 3;
  google.protobuf.Timestamp end_date = 4;
}

message SemesterResponse {
  int64 id = 1;
  string name = 2;
  google.protobuf.Timestamp start_date = 3;
  google.protobuf.Timestamp end_date = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
}

message SemesterByID {
  int64 id = 1;
}

message SemestersResponse {
  repeated SemesterResponse semesters = 1;
}

//-------------------------------//
//    Semester Break Messages    //
//-------------------------------//
message SemesterBreakByID {
  int64 id = 1;
}

message SemesterBreakRequest {
  int64 semester_id = 1;
  google.protobuf.Timestamp break_date = 2;
  string description = 3;
}

message SemesterBreaksResponse {
  repeated SemesterBreakResponse semester_breaks = 1;
}

message SemesterBreakResponse {
  int64 id = 1;
  int64 semester_id = 2;
  google.protobuf.Timestamp break_date = 3;
  string description = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
}

message SemesterEmptyRequest {

}

message SemesterEmptyResponse {

}
