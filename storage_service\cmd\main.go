package main

import (
	"log"
	"net"
	"os"
	"os/signal"
	"syscall"

	"github.com/olzzhas/edunite-server/storage_service/internal/config"
	"github.com/olzzhas/edunite-server/storage_service/internal/database"
	"github.com/olzzhas/edunite-server/storage_service/internal/service"
	"github.com/olzzhas/edunite-server/storage_service/pb"

	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

func main() {
	cfg := config.LoadConfig()

	// Initialize storage repository based on configuration
	var storageSrv *service.StorageService
	var err error

	if cfg.StorageType == "minio" {
		log.Println("Using MinIO storage")
		minioRepo, err := database.NewMinioRepository(cfg)
		if err != nil {
			log.Fatalf("failed to init minio repo: %v", err)
		}
		storageSrv = service.NewStorageService(minioRepo)
	} else if cfg.StorageType == "local" {
		log.Println("Using Local File System storage")
		localFSRepo, err := database.NewLocalFSRepository(cfg)
		if err != nil {
			log.Fatalf("failed to init local FS repo: %v", err)
		}
		storageSrv = service.NewStorageServiceWithLocalFS(localFSRepo)
	} else {
		log.Println("Using AWS S3 storage")
		awsS3Repo, err := database.NewAWSS3Repository(cfg)
		if err != nil {
			log.Fatalf("failed to init AWS S3 repo: %v", err)
		}
		storageSrv = service.NewStorageServiceWithAWSS3(awsS3Repo)
	}

	grpcServer := grpc.NewServer()
	pb.RegisterStorageServiceServer(grpcServer, storageSrv)
	reflection.Register(grpcServer)

	lis, err := net.Listen("tcp", ":50059")
	if err != nil {
		log.Fatalf("failed to listen on :50059: %v", err)
	}
	log.Println("StorageService gRPC listening on :50059")

	go func() {
		if err := grpcServer.Serve(lis); err != nil {
			log.Fatalf("failed to serve: %v", err)
		}
	}()

	waitForShutdown(grpcServer)
}

func waitForShutdown(server *grpc.Server) {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM)
	<-quit

	log.Println("Received shutdown signal, gracefully stopping StorageService gRPC server...")
	server.GracefulStop()
}
