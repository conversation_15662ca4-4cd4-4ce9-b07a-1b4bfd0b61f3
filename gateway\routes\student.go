package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

// SetupStudentRoutes sets up routes for student functionality
func SetupStudentRoutes(r *gin.Engine, authClient *clients.AuthClient, studentHandler *handlers.StudentHandler) {
	// Create a group for student routes that requires authentication and student role
	studentGroup := r.Group("/students")
	//studentGroup.Use(AuthMiddleware(authClient))
	//studentGroup.Use(RoleMiddleware(authClient, "student"))
	{
		// Student schedule endpoint
		studentGroup.GET("/:student_id/schedule", studentHandler.GetStudentScheduleHandler)

		// Pending assignments endpoint - returns assignments without submissions for a specific thread
		studentGroup.GET("/:student_id/threads/:thread_id/pending-assignments", studentHandler.ListPendingAssignmentsHandler)

		// All pending assignments endpoint - returns assignments without submissions across all threads
		studentGroup.GET("/:student_id/pending-assignments", studentHandler.ListAllPendingAssignmentsHandler)
	}
}
