package assignment

import (
	"context"
	"errors"
	"sort"
	"strconv"

	"github.com/olzzhas/edunite-server/course_service/internal/database"
	"github.com/olzzhas/edunite-server/course_service/internal/query/assignmentquery"
	assignmentpb "github.com/olzzhas/edunite-server/course_service/pb/assignment"
	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type Service struct {
	assignmentRepo      database.AssignmentRepository
	assignmentGroupRepo database.AssignmentGroupRepository
	attachmentRepo      database.AssignmentAttachmentRepository
	submissionRepo      database.AssignmentSubmissionRepository
	assignmentQueryRepo assignmentquery.Repo
	assignmentpb.UnimplementedAssignmentServiceServer
}

func NewAssignmentService(
	assignmentRepository database.AssignmentRepository,
	assignmentGroupRepository database.AssignmentGroupRepository,
	attachmentRepository database.AssignmentAttachmentRepository,
	submissionRepository database.AssignmentSubmissionRepository,
	assignmentQueryRepo assignmentquery.Repo,
) *Service {
	return &Service{
		assignmentRepo:      assignmentRepository,
		assignmentGroupRepo: assignmentGroupRepository,
		attachmentRepo:      attachmentRepository,
		submissionRepo:      submissionRepository,
		assignmentQueryRepo: assignmentQueryRepo,
	}
}

// CreateAssignment обрабатывает создание задачи
func (s *Service) CreateAssignment(ctx context.Context, req *assignmentpb.AssignmentRequest) (*assignmentpb.AssignmentResponse, error) {
	a := &database.Assignment{
		WeekID:            req.GetWeekId(),
		Title:             req.GetTitle(),
		Description:       req.GetDescription(),
		DueDate:           req.GetDueDate().AsTime(),
		MaxPoints:         req.GetMaxPoints(),
		AssignmentGroupID: req.GetAssignmentGroupId(),
		Type:              req.GetType(),
	}

	v := validator.New()
	database.ValidateAssignment(v, a)
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	err := s.assignmentRepo.CreateAssignment(ctx, a)
	if err != nil {
		switch {
		case errors.Is(err, database.ErrAssignmentConflict):
			return nil, status.Errorf(codes.AlreadyExists, err.Error())
		default:
			return nil, status.Errorf(codes.Internal, "could not create assignment: %v", err)
		}
	}

	return &assignmentpb.AssignmentResponse{
		Id:                a.ID,
		WeekId:            a.WeekID,
		Title:             a.Title,
		Description:       a.Description,
		DueDate:           timestamppb.New(a.DueDate),
		MaxPoints:         a.MaxPoints,
		AssignmentGroupId: a.AssignmentGroupID,
		Type:              a.Type,
		CreatedAt:         timestamppb.New(a.CreatedAt),
		UpdatedAt:         timestamppb.New(a.UpdatedAt),
	}, nil
}

// ListAssignmentsForWeek возвращает все задачи недели
func (s *Service) ListAssignmentsForWeek(ctx context.Context, req *assignmentpb.AssignmentsForWeekRequest) (*assignmentpb.AssignmentsResponse, error) {
	list, err := s.assignmentRepo.ListAssignmentsByWeek(ctx, req.GetWeekId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not list assignments: %v", err)
	}
	resp := make([]*assignmentpb.AssignmentResponse, 0, len(list))
	for _, a := range list {
		resp = append(resp, &assignmentpb.AssignmentResponse{
			Id:                a.ID,
			WeekId:            a.WeekID,
			Title:             a.Title,
			Description:       a.Description,
			DueDate:           timestamppb.New(a.DueDate),
			MaxPoints:         a.MaxPoints,
			AssignmentGroupId: a.AssignmentGroupID,
			Type:              a.Type,
			CreatedAt:         timestamppb.New(a.CreatedAt),
			UpdatedAt:         timestamppb.New(a.UpdatedAt),
		})
	}
	return &assignmentpb.AssignmentsResponse{Assignments: resp}, nil
}

// GetAssignmentByID возвращает задачу по ID
func (s *Service) GetAssignmentByID(ctx context.Context, req *assignmentpb.AssignmentByID) (*assignmentpb.AssignmentResponse, error) {
	a, err := s.assignmentRepo.GetAssignment(ctx, req.GetId())
	if err != nil {
		if errors.Is(err, database.ErrAssignmentNotFound) {
			return nil, status.Errorf(codes.NotFound, err.Error())
		}
		return nil, status.Errorf(codes.Internal, "error fetching assignment: %v", err)
	}
	return &assignmentpb.AssignmentResponse{
		Id:                a.ID,
		WeekId:            a.WeekID,
		Title:             a.Title,
		Description:       a.Description,
		DueDate:           timestamppb.New(a.DueDate),
		MaxPoints:         a.MaxPoints,
		AssignmentGroupId: a.AssignmentGroupID,
		Type:              a.Type,
		CreatedAt:         timestamppb.New(a.CreatedAt),
		UpdatedAt:         timestamppb.New(a.UpdatedAt),
	}, nil
}

// UpdateAssignmentByID обновляет задачу
func (s *Service) UpdateAssignmentByID(ctx context.Context, req *assignmentpb.AssignmentUpdateRequest) (*assignmentpb.AssignmentResponse, error) {
	existing, err := s.assignmentRepo.GetAssignment(ctx, req.GetId())
	if err != nil {
		if errors.Is(err, database.ErrAssignmentNotFound) {
			return nil, status.Errorf(codes.NotFound, err.Error())
		}
		return nil, status.Errorf(codes.Internal, "error fetching assignment: %v", err)
	}
	existing.WeekID = req.GetWeekId()
	existing.Title = req.GetTitle()
	existing.Description = req.GetDescription()
	existing.DueDate = req.GetDueDate().AsTime()
	existing.MaxPoints = req.GetMaxPoints()
	existing.AssignmentGroupID = req.GetAssignmentGroupId()
	existing.Type = req.GetType()
	// validation
	v := validator.New()
	database.ValidateAssignment(v, existing)
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}
	if err := s.assignmentRepo.UpdateAssignment(ctx, existing); err != nil {
		return nil, status.Errorf(codes.Internal, "could not update assignment: %v", err)
	}
	return &assignmentpb.AssignmentResponse{
		Id:                existing.ID,
		WeekId:            existing.WeekID,
		Title:             existing.Title,
		Description:       existing.Description,
		DueDate:           timestamppb.New(existing.DueDate),
		MaxPoints:         existing.MaxPoints,
		AssignmentGroupId: existing.AssignmentGroupID,
		Type:              existing.Type,
		CreatedAt:         timestamppb.New(existing.CreatedAt),
		UpdatedAt:         timestamppb.New(existing.UpdatedAt),
	}, nil
}

// DeleteAssignmentByID удаляет задачу
func (s *Service) DeleteAssignmentByID(ctx context.Context, req *assignmentpb.AssignmentByID) (*assignmentpb.AssignmentEmptyResponse, error) {
	if err := s.assignmentRepo.DeleteAssignment(ctx, req.GetId()); err != nil {
		if errors.Is(err, database.ErrAssignmentNotFound) {
			return nil, status.Errorf(codes.NotFound, err.Error())
		}
		return nil, status.Errorf(codes.Internal, "could not delete assignment: %v", err)
	}
	return &assignmentpb.AssignmentEmptyResponse{}, nil
}

// ListAssignmentsWithSubmissionForThread — реализуем новый RPC
func (s *Service) ListAssignmentsWithSubmissionForThread(
	ctx context.Context,
	req *assignmentpb.AssignmentsWithSubmissionRequest,
) (*assignmentpb.AssignmentsWithSubmissionResponse, error) {
	if req.GetThreadId() == 0 || req.GetUserId() == 0 {
		return nil, status.Error(codes.InvalidArgument, "thread_id and user_id must be > 0")
	}

	rows, err := s.assignmentQueryRepo.ListAssignmentsWithSubmission(ctx, req.GetThreadId(), req.GetUserId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to query: %v", err)
	}

	var resp assignmentpb.AssignmentsWithSubmissionResponse
	for _, d := range rows {
		// собираем AssignmentResponse
		ass := &assignmentpb.AssignmentResponse{
			Id:                d.AssID,
			WeekId:            d.WeekID,
			Title:             d.Title,
			Description:       *d.Description, // Description — *string
			DueDate:           timestamppb.New(*d.DueDate),
			MaxPoints:         *d.MaxPoints,
			AssignmentGroupId: *d.AssignmentGroupID,
			Type:              d.Type,
			CreatedAt:         timestamppb.New(d.AssCreatedAt),
			UpdatedAt:         timestamppb.New(d.AssUpdatedAt),
		}

		// собираем SubmissionResponse, если она есть
		var sub *assignmentpb.AssignmentSubmissionResponse
		if d.SubID != nil {
			sub = &assignmentpb.AssignmentSubmissionResponse{
				Id:           *d.SubID,
				AssignmentId: d.AssID,
				UserId:       *d.UserID,
				SubmittedAt:  timestamppb.New(*d.SubmittedAt),
				FileUrls:     d.FileURLs,
				Comment:      *d.Comment,
				Score:        *d.Score,
				Feedback:     *d.Feedback,
				CreatedAt:    timestamppb.New(*d.SubCreatedAt),
				UpdatedAt:    timestamppb.New(*d.SubUpdatedAt),
			}
		}

		resp.Items = append(resp.Items, &assignmentpb.AssignmentWithSubmission{
			Assignment: ass,
			Submission: sub,
		})
	}

	// опционально сортируем по week_id, id
	sort.Slice(resp.Items, func(i, j int) bool {
		a, b := resp.Items[i].Assignment, resp.Items[j].Assignment
		if a.WeekId != b.WeekId {
			return a.WeekId < b.WeekId
		}
		return a.Id < b.Id
	})

	return &resp, nil
}

// ListAssignmentsWithoutSubmissionForThread - возвращает все задания потока, для которых у студента нет сабмишенов
func (s *Service) ListAssignmentsWithoutSubmissionForThread(
	ctx context.Context,
	req *assignmentpb.AssignmentsWithSubmissionRequest,
) (*assignmentpb.AssignmentsWithSubmissionResponse, error) {
	if req.GetThreadId() == 0 || req.GetUserId() == 0 {
		return nil, status.Error(codes.InvalidArgument, "thread_id and user_id must be > 0")
	}

	rows, err := s.assignmentQueryRepo.ListAssignmentsWithoutSubmission(ctx, req.GetThreadId(), req.GetUserId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to query: %v", err)
	}

	var resp assignmentpb.AssignmentsWithSubmissionResponse
	for _, d := range rows {
		// собираем AssignmentResponse
		ass := &assignmentpb.AssignmentResponse{
			Id:                d.AssID,
			WeekId:            d.WeekID,
			Title:             d.Title,
			Description:       *d.Description, // Description — *string
			DueDate:           timestamppb.New(*d.DueDate),
			MaxPoints:         *d.MaxPoints,
			AssignmentGroupId: *d.AssignmentGroupID,
			Type:              d.Type,
			CreatedAt:         timestamppb.New(d.AssCreatedAt),
			UpdatedAt:         timestamppb.New(d.AssUpdatedAt),
		}

		// Submission всегда будет nil, так как мы ищем задания без сабмишенов
		resp.Items = append(resp.Items, &assignmentpb.AssignmentWithSubmission{
			Assignment: ass,
			Submission: nil,
		})
	}

	// опционально сортируем по week_id, id
	sort.Slice(resp.Items, func(i, j int) bool {
		a, b := resp.Items[i].Assignment, resp.Items[j].Assignment
		if a.WeekId != b.WeekId {
			return a.WeekId < b.WeekId
		}
		return a.Id < b.Id
	})

	return &resp, nil
}

// GetAssignmentDetailsForStudent returns comprehensive assignment details for a student
func (s *Service) GetAssignmentDetailsForStudent(
	ctx context.Context,
	req *assignmentpb.AssignmentDetailsForStudentRequest,
) (*assignmentpb.AssignmentDetailsForStudentResponse, error) {
	// Validate request
	if req.GetAssignmentId() <= 0 || req.GetStudentId() <= 0 {
		return nil, status.Error(codes.InvalidArgument, "assignment_id and student_id must be > 0")
	}

	// 1. Get assignment details
	assignment, err := s.assignmentRepo.GetAssignment(ctx, req.GetAssignmentId())
	if err != nil {
		if errors.Is(err, database.ErrAssignmentNotFound) {
			return nil, status.Errorf(codes.NotFound, "assignment not found")
		}
		return nil, status.Errorf(codes.Internal, "error fetching assignment: %v", err)
	}

	// 2. Get assignment group details
	var assignmentGroup *database.AssignmentGroup
	var assignmentGroupPB *assignmentpb.AssignmentGroupResponse
	if assignment.AssignmentGroupID > 0 {
		assignmentGroup, err = s.assignmentGroupRepo.GetAssignmentGroup(ctx, assignment.AssignmentGroupID)
		if err == nil && assignmentGroup != nil {
			assignmentGroupPB = &assignmentpb.AssignmentGroupResponse{
				Id:        assignmentGroup.ID,
				ThreadId:  assignmentGroup.ThreadID,
				Name:      assignmentGroup.Name,
				GroupType: assignmentGroup.GroupType,
				Weight:    assignmentGroup.Weight,
				CreatedAt: timestamppb.New(assignmentGroup.CreatedAt),
				UpdatedAt: timestamppb.New(assignmentGroup.UpdatedAt),
			}
		}
	}

	// 3. Get submission details if exists
	submissions, err := s.submissionRepo.GetSubmissionsByAssignmentAndUser(
		ctx, req.GetAssignmentId(), req.GetStudentId())

	var submissionPB *assignmentpb.AssignmentSubmissionResponse
	if err == nil && len(submissions) > 0 {
		// Use the most recent submission
		submission := submissions[0]
		// Handle nullable fields
		var score int32 = 0
		var feedback string = ""

		if submission.Score != nil {
			score = *submission.Score
		}

		if submission.Feedback != nil {
			feedback = *submission.Feedback
		}

		submissionPB = &assignmentpb.AssignmentSubmissionResponse{
			Id:           submission.ID,
			AssignmentId: submission.AssignmentID,
			UserId:       submission.UserID,
			SubmittedAt:  timestamppb.New(submission.SubmittedAt),
			FileUrls:     submission.FileURLs,
			Comment:      submission.Comment,
			Score:        score,
			Feedback:     feedback,
			CreatedAt:    timestamppb.New(submission.CreatedAt),
			UpdatedAt:    timestamppb.New(submission.UpdatedAt),
		}
	}

	// 4. Get attachments
	attachments, err := s.attachmentRepo.GetAttachmentsByAssignmentID(ctx, req.GetAssignmentId())
	attachmentsPB := make([]*assignmentpb.AssignmentAttachmentResponse, 0, len(attachments))
	if err == nil {
		for _, attachment := range attachments {
			attachmentsPB = append(attachmentsPB, &assignmentpb.AssignmentAttachmentResponse{
				Id:           attachment.ID,
				AssignmentId: attachment.AssignmentID,
				FileUrl:      attachment.FileURL,
				CreatedAt:    timestamppb.New(attachment.CreatedAt),
				UpdatedAt:    timestamppb.New(attachment.UpdatedAt),
			})
		}
	}

	// 5. Calculate deadline status
	now := timestamppb.Now().AsTime()
	daysRemaining := int32(assignment.DueDate.Sub(now).Hours() / 24)
	isOverdue := now.After(assignment.DueDate)

	var statusText string
	if isOverdue {
		statusText = "Overdue"
	} else if daysRemaining == 0 {
		statusText = "Due today"
	} else if daysRemaining == 1 {
		statusText = "Due tomorrow"
	} else {
		statusText = "Due in " + strconv.Itoa(int(daysRemaining)) + " days"
	}

	deadlineStatus := &assignmentpb.DeadlineStatus{
		IsOverdue:     isOverdue,
		DaysRemaining: daysRemaining,
		StatusText:    statusText,
	}

	// 6. Build and return the response
	return &assignmentpb.AssignmentDetailsForStudentResponse{
		Assignment: &assignmentpb.AssignmentResponse{
			Id:                assignment.ID,
			WeekId:            assignment.WeekID,
			Title:             assignment.Title,
			Description:       assignment.Description,
			DueDate:           timestamppb.New(assignment.DueDate),
			MaxPoints:         assignment.MaxPoints,
			AssignmentGroupId: assignment.AssignmentGroupID,
			Type:              assignment.Type,
			CreatedAt:         timestamppb.New(assignment.CreatedAt),
			UpdatedAt:         timestamppb.New(assignment.UpdatedAt),
		},
		AssignmentGroup: assignmentGroupPB,
		Submission:      submissionPB,
		DeadlineStatus:  deadlineStatus,
		Attachments:     attachmentsPB,
	}, nil
}
