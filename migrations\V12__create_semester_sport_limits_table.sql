-- Create semester sport limits table
CREATE TABLE IF NOT EXISTS semester_sport_limits (
    id BIGSERIAL PRIMARY KEY,
    semester_id BIGINT NOT NULL REFERENCES semesters(id) ON DELETE CASCADE,
    min_lessons INT NOT NULL DEFAULT 0,
    max_lessons INT NOT NULL DEFAULT 20,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(semester_id)
);

-- Create trigger for updated_at
CREATE TRIGGER trg_semester_sport_limits_updated
    BEFORE UPDATE ON semester_sport_limits
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();
