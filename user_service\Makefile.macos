# Получаем все .proto файлы, отслеживаемые Git
PROTO_SRC := $(shell git ls-files -- '*.proto')

# Каталог, куда будет генерироваться код
PROTO_OUT := ./

# Путь до googleapis (установи отдельно если используешь google/api/*.proto)
GOOGLE_APIS := /usr/local/include/googleapis
PROTO_INCLUDE := -I . -I $(GOOGLE_APIS)

# Основные цели Makefile
.PHONY: all clean proto

all: proto

proto:
	@echo "🛠 Генерация Go, gRPC и grpc-gateway кода..."
	protoc $(PROTO_INCLUDE) \
		--go_out=$(PROTO_OUT) --go_opt=paths=source_relative \
		--go-grpc_out=$(PROTO_OUT) --go-grpc_opt=paths=source_relative \
		--grpc-gateway_out=$(PROTO_OUT) --grpc-gateway_opt=paths=source_relative \
		$(PROTO_SRC)

clean:
	@echo "🧹 Удаление сгенерированных .go файлов..."
	rm -f $(PROTO_OUT)/*.pb.go
	rm -f $(PROTO_OUT)/*.pb.gw.go
