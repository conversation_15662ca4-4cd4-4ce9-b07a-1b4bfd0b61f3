openapi: 3.0.0
info:
  title: Edunite Assignment API
  description: API documentation for the Assignment endpoints of the Edunite platform
  version: 1.0.0
  contact:
    name: Edunite Support
    email: <EMAIL>

servers:
  - url: http://localhost:8081
    description: Local development server
  - url: https://api.edunite.com
    description: Production server

security:
  - bearerAuth: []

tags:
  - name: assignment-groups
    description: Assignment group operations
  - name: assignment-attachments
    description: Assignment attachment operations
  - name: assignment-submissions
    description: Assignment submission operations
  - name: assignments
    description: Assignment operations

paths:
  # Assignment Group endpoints
  /assignments/groups:
    post:
      tags:
        - assignment-groups
      summary: Create assignment group
      description: Creates a new assignment group for a thread
      operationId: createAssignmentGroup
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - thread_id
                - name
                - group_type
                - weight
              properties:
                thread_id:
                  type: integer
                  format: int64
                  description: ID of the thread to create the assignment group for
                  example: 1
                name:
                  type: string
                  description: Name of the assignment group
                  example: "Homework"
                group_type:
                  type: string
                  description: Type of the assignment group
                  example: "homework"
                weight:
                  type: number
                  format: float
                  description: Weight of the assignment group in the final grade calculation
                  example: 0.3
      responses:
        '201':
          description: Assignment group created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignmentGroup'
        '400':
          description: Invalid request body
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /assignments/groups/thread/{threadId}:
    get:
      tags:
        - assignment-groups
      summary: List assignment groups for thread
      description: Returns a list of assignment groups for a specific thread
      operationId: listAssignmentGroupsForThread
      parameters:
        - name: threadId
          in: path
          required: true
          description: ID of the thread to list assignment groups for
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: List of assignment groups
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AssignmentGroup'
        '400':
          description: Invalid thread ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /assignments/groups/{id}:
    get:
      tags:
        - assignment-groups
      summary: Get assignment group by ID
      description: Returns a specific assignment group by its ID
      operationId: getAssignmentGroupByID
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the assignment group to retrieve
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Assignment group details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignmentGroup'
        '400':
          description: Invalid assignment group ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Assignment group not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      tags:
        - assignment-groups
      summary: Update assignment group
      description: Updates an existing assignment group
      operationId: updateAssignmentGroupByID
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the assignment group to update
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - thread_id
                - name
                - group_type
                - weight
              properties:
                thread_id:
                  type: integer
                  format: int64
                  description: ID of the thread this assignment group belongs to
                  example: 1
                name:
                  type: string
                  description: Name of the assignment group
                  example: "Homework"
                group_type:
                  type: string
                  description: Type of the assignment group
                  example: "homework"
                weight:
                  type: number
                  format: float
                  description: Weight of the assignment group in the final grade calculation
                  example: 0.3
      responses:
        '200':
          description: Assignment group updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignmentGroup'
        '400':
          description: Invalid request body or assignment group ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Assignment group not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      tags:
        - assignment-groups
      summary: Delete assignment group
      description: Deletes an assignment group by its ID
      operationId: deleteAssignmentGroupByID
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the assignment group to delete
          schema:
            type: integer
            format: int64
      responses:
        '204':
          description: Assignment group deleted successfully
          content: {}
        '400':
          description: Invalid assignment group ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Assignment group not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Assignment Attachment endpoints
  /assignments/{assignmentId}/attachments:
    post:
      tags:
        - assignment-attachments
      summary: Create assignment attachment
      description: Adds a new attachment to an assignment
      operationId: createAssignmentAttachment
      parameters:
        - name: assignmentId
          in: path
          required: true
          description: ID of the assignment to add the attachment to
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - file_url
              properties:
                file_url:
                  type: string
                  description: URL of the file to attach
                  example: "storage/assignments/file.pdf"
      responses:
        '201':
          description: Attachment created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignmentAttachment'
        '400':
          description: Invalid request body or assignment ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    get:
      tags:
        - assignment-attachments
      summary: Get assignment attachments
      description: Returns all attachments for a specific assignment
      operationId: getAssignmentAttachmentsByAssignmentID
      parameters:
        - name: assignmentId
          in: path
          required: true
          description: ID of the assignment to get attachments for
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: List of assignment attachments
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AssignmentAttachment'
        '400':
          description: Invalid assignment ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /assignments/attachments/{id}:
    delete:
      tags:
        - assignment-attachments
      summary: Delete assignment attachment
      description: Deletes an assignment attachment by its ID
      operationId: deleteAssignmentAttachmentByID
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the attachment to delete
          schema:
            type: integer
            format: int64
      responses:
        '204':
          description: Attachment deleted successfully
          content: {}
        '400':
          description: Invalid attachment ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Attachment not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Assignment Submission endpoints
  /assignments/{assignmentId}/submissions:
    post:
      tags:
        - assignment-submissions
      summary: Create assignment submission
      description: Creates a new submission for an assignment. Supports both JSON and multipart/form-data for file uploads.
      operationId: createAssignmentSubmission
      parameters:
        - name: assignmentId
          in: path
          required: true
          description: ID of the assignment to submit for
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - user_id
                - file_urls
              properties:
                user_id:
                  type: integer
                  format: int64
                  description: ID of the user submitting the assignment
                  example: 1
                file_urls:
                  type: array
                  description: URLs of the files submitted
                  items:
                    type: string
                    example: "storage/submissions/file.pdf"
                comment:
                  type: string
                  description: Optional comment for the submission
                  example: "Here is my submission for the assignment"
          multipart/form-data:
            schema:
              type: object
              required:
                - user_id
                - file
              properties:
                user_id:
                  type: string
                  description: ID of the user submitting the assignment
                  example: "1"
                file:
                  type: string
                  format: binary
                  description: File to submit for the assignment
                file2:
                  type: string
                  format: binary
                  description: Additional file to submit (optional)
                file3:
                  type: string
                  format: binary
                  description: Additional file to submit (optional)
                comment:
                  type: string
                  description: Optional comment for the submission
                  example: "Here is my submission for the assignment"
      responses:
        '201':
          description: Submission created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignmentSubmission'
        '400':
          description: Invalid request body or assignment ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '409':
          description: User already has a submission for this assignment
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "You have already submitted this assignment. Please use the update endpoint instead."
                  code:
                    type: string
                    example: "ALREADY_SUBMITTED"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    get:
      tags:
        - assignment-submissions
      summary: List assignment submissions
      description: Returns all submissions for a specific assignment
      operationId: listAssignmentSubmissionsByAssignmentID
      parameters:
        - name: assignmentId
          in: path
          required: true
          description: ID of the assignment to get submissions for
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: List of assignment submissions
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AssignmentSubmission'
        '400':
          description: Invalid assignment ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /assignments/submissions/{id}:
    get:
      tags:
        - assignment-submissions
      summary: Get assignment submission
      description: Returns a specific assignment submission by its ID
      operationId: getAssignmentSubmissionByID
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the submission to retrieve
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Assignment submission details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignmentSubmission'
        '400':
          description: Invalid submission ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Submission not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      tags:
        - assignment-submissions
      summary: Update assignment submission
      description: Updates an existing assignment submission. Supports both JSON and multipart/form-data for file uploads.
      operationId: updateAssignmentSubmission
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the submission to update
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - file_urls
              properties:
                file_urls:
                  type: array
                  description: Updated URLs of the files submitted
                  items:
                    type: string
                    example: "storage/submissions/updated_file.pdf"
                comment:
                  type: string
                  description: Updated comment for the submission
                  example: "Here is my updated submission"
          multipart/form-data:
            schema:
              type: object
              required:
                - file
              properties:
                file:
                  type: string
                  format: binary
                  description: File to submit for the assignment
                file2:
                  type: string
                  format: binary
                  description: Additional file to submit (optional)
                file3:
                  type: string
                  format: binary
                  description: Additional file to submit (optional)
                comment:
                  type: string
                  description: Updated comment for the submission
                  example: "Here is my updated submission"
      responses:
        '200':
          description: Submission updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignmentSubmission'
        '400':
          description: Invalid request body or submission ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Submission not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      tags:
        - assignment-submissions
      summary: Delete assignment submission
      description: Deletes an assignment submission by its ID
      operationId: deleteAssignmentSubmissionByID
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the submission to delete
          schema:
            type: integer
            format: int64
      responses:
        '204':
          description: Submission deleted successfully
          content: {}
        '400':
          description: Invalid submission ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Submission not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /assignments/submissions/{id}/score:
    put:
      tags:
        - assignment-submissions
      summary: Update assignment submission score
      description: Updates the score and feedback for an assignment submission
      operationId: updateAssignmentSubmissionScore
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the submission to update the score for
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - score
                - feedback
              properties:
                score:
                  type: integer
                  description: Score for the submission
                  example: 85
                feedback:
                  type: string
                  description: Feedback for the submission
                  example: "Good work, but could improve in some areas."
      responses:
        '200':
          description: Submission score updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignmentSubmission'
        '400':
          description: Invalid request body or submission ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Submission not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Assignment endpoints
  /assignments/weeks/{weekId}/assignments:
    post:
      tags:
        - assignments
      summary: Create assignment
      description: Creates a new assignment for a specific week. Supports both JSON and multipart/form-data for file uploads.
      operationId: createAssignment
      parameters:
        - name: weekId
          in: path
          required: true
          description: ID of the week to create the assignment for
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - title
                - type
              properties:
                title:
                  type: string
                  description: Title of the assignment
                  example: "Homework 1"
                description:
                  type: string
                  description: Description of the assignment
                  example: "Complete the exercises in chapter 1"
                due_date:
                  type: string
                  format: date-time
                  description: Due date for the assignment (RFC3339 format, required for task type)
                  example: "2023-12-31T23:59:59Z"
                max_points:
                  type: integer
                  description: Maximum points for the assignment (required for task type)
                  example: 100
                assignment_group_id:
                  type: integer
                  format: int64
                  description: ID of the assignment group this assignment belongs to
                  example: 1
                type:
                  type: string
                  description: Type of assignment (task or info)
                  example: "task"
                  enum: ["task", "info"]
          multipart/form-data:
            schema:
              type: object
              required:
                - title
                - type
              properties:
                title:
                  type: string
                  description: Title of the assignment
                  example: "Homework 1"
                description:
                  type: string
                  description: Description of the assignment
                  example: "Complete the exercises in chapter 1"
                due_date:
                  type: string
                  format: date-time
                  description: Due date for the assignment (RFC3339 format, required for task type)
                  example: "2023-12-31T23:59:59Z"
                max_points:
                  type: string
                  description: Maximum points for the assignment (required for task type)
                  example: "100"
                assignment_group_id:
                  type: string
                  description: ID of the assignment group this assignment belongs to
                  example: "1"
                type:
                  type: string
                  description: Type of assignment (task or info)
                  example: "task"
                  enum: ["task", "info"]
                file:
                  type: string
                  format: binary
                  description: File to attach to the assignment
      responses:
        '201':
          description: Assignment created successfully
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/Assignment'
                  - type: object
                    properties:
                      assignment:
                        $ref: '#/components/schemas/Assignment'
                      attachment:
                        $ref: '#/components/schemas/AssignmentAttachment'
        '400':
          description: Invalid request body or week ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    get:
      tags:
        - assignments
      summary: List assignments for week
      description: Returns all assignments for a specific week
      operationId: listAssignmentsForWeek
      parameters:
        - name: weekId
          in: path
          required: true
          description: ID of the week to list assignments for
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: List of assignments
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Assignment'
        '400':
          description: Invalid week ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /assignments/{id}:
    get:
      tags:
        - assignments
      summary: Get assignment by ID
      description: Returns a specific assignment by its ID
      operationId: getAssignmentByID
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the assignment to retrieve
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Assignment details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Assignment'
        '400':
          description: Invalid assignment ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Assignment not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      tags:
        - assignments
      summary: Update assignment
      description: Updates an existing assignment
      operationId: updateAssignmentByID
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the assignment to update
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - week_id
                - title
                - due_date
                - max_points
              properties:
                week_id:
                  type: integer
                  format: int64
                  description: ID of the week this assignment belongs to
                  example: 1
                title:
                  type: string
                  description: Title of the assignment
                  example: "Updated Homework 1"
                description:
                  type: string
                  description: Description of the assignment
                  example: "Updated description for the exercises in chapter 1"
                due_date:
                  type: string
                  format: date-time
                  description: Due date for the assignment (RFC3339 format)
                  example: "2024-01-15T23:59:59Z"
                max_points:
                  type: integer
                  description: Maximum points for the assignment
                  example: 120
                assignment_group_id:
                  type: integer
                  format: int64
                  description: ID of the assignment group this assignment belongs to
                  example: 2
      responses:
        '200':
          description: Assignment updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Assignment'
        '400':
          description: Invalid request body or assignment ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Assignment not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      tags:
        - assignments
      summary: Delete assignment
      description: Deletes an assignment by its ID
      operationId: deleteAssignmentByID
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the assignment to delete
          schema:
            type: integer
            format: int64
      responses:
        '204':
          description: Assignment deleted successfully
          content: {}
        '400':
          description: Invalid assignment ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Assignment not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    AssignmentGroup:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        thread_id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: "Homework"
        group_type:
          type: string
          example: "homework"
        weight:
          type: number
          format: float
          example: 0.3
        created_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"

    AssignmentAttachment:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        assignment_id:
          type: integer
          format: int64
          example: 1
        file_url:
          type: string
          example: "storage/assignments/file.pdf"
        created_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"

    AssignmentSubmission:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        assignment_id:
          type: integer
          format: int64
          example: 1
        user_id:
          type: integer
          format: int64
          example: 1
        submitted_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        file_urls:
          type: array
          items:
            type: string
            example: "storage/submissions/file.pdf"
        comment:
          type: string
          example: "Here is my submission for the assignment"
        score:
          type: integer
          example: 85
        feedback:
          type: string
          example: "Good work, but could improve in some areas."
        created_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"

    Assignment:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        week_id:
          type: integer
          format: int64
          example: 1
        title:
          type: string
          example: "Homework 1"
        description:
          type: string
          example: "Complete the exercises in chapter 1"
        due_date:
          type: string
          format: date-time
          example: "2023-12-31T23:59:59Z"
        max_points:
          type: integer
          example: 100
        assignment_group_id:
          type: integer
          format: int64
          example: 1
        type:
          type: string
          example: "task"
        created_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"

    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
          example: "Invalid request body"
