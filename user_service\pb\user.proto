syntax = "proto3";

package userpb;

option go_package = "/pb";

import "google/api/annotations.proto";

service UserService {
  rpc CreateUser(CreateUserRequest) returns (UserResponse) {
    option (google.api.http) = {
      post: "/v1/users"
      body: "*"
    };
  }

  rpc GetUser(GetUserRequest) returns (UserResponse) {
    option (google.api.http) = {
      get: "/v1/users/{id}"
    };
  }

  rpc UpdateUser(UpdateUserRequest) returns (UserResponse) {
    option (google.api.http) = {
      put: "/v1/users/{id}"
      body: "*"
    };
  }

  rpc DeleteUser(DeleteUserRequest) returns (EmptyResponse) {
    option (google.api.http) = {
      delete: "/v1/users/{id}"
    };
  }
  rpc GetUserByEmail(GetUserByEmailRequest) returns (UserResponse) {
    option (google.api.http) = {
      get: "/v1/users/email/{email}"
    };
  }


  rpc GetUserByKeycloakID(GetUserByEmailRequest) returns (UserResponse){
  }

  rpc GetAllUsers(GetAllUsersRequest) returns (UsersWithMeta){}
}

message GetUserByEmailRequest {
  string email = 1;
}

message GetUserByKeycloakID {
  string keycloakID = 1;
}

message CreateUserRequest {
  int64 id = 1;
  string keycloakID = 2;
  string name = 3;
  string surname = 4;
  string email = 5;
  string role = 6;
  string username = 7;
  string password_hash = 8;
}

message GetUserRequest {
  int64 id = 1;
}

message UpdateUserRequest {
  int64 id = 1;
  string name = 2;
  string surname = 3;
  string role = 4;
  string email = 5;
  string username = 6;
  string password_hash = 7;
}

message DeleteUserRequest {
  int64 id = 1;
}

message UserResponse {
  int64 id = 1;
  string keycloakID = 2;
  string name = 3;
  string surname = 4;
  string email = 5;
  string role = 6;
  string created_at = 7;
  string updated_at = 8;
  int32 version = 9;
  string username = 10;
  string password_hash = 11;
}

message UsersResponse {
  repeated UserResponse users = 1;
}


message EmptyRequest {}

message EmptyResponse {}

// pb/user_service.proto
message Filters {
  int32 page = 1;
  int32 page_size = 2;
  string sort = 3;
  string search = 4;
  string role = 5;
}

message GetAllUsersRequest {
  Filters filters = 1;
}

message UsersWithMeta {
  repeated UserResponse users = 1;
  int32 current_page = 2;
  int32 page_size = 3;
  int32 first_page = 4;
  int32 last_page  = 5;
  int32 total_records = 6;
}