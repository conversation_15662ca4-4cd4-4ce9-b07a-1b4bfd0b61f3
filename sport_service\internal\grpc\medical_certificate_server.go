package grpc

import (
	"context"
	sportpb "github.com/olzzhas/edunite-server/sport_service/proto"

	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
	"github.com/olzzhas/edunite-server/sport_service/internal/service"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type medicalCertificateServer struct {
	sportpb.UnimplementedMedicalCertificateServiceServer
	services *service.Services
}

// NewMedicalCertificateServer creates a new medical certificate gRPC server
func NewMedicalCertificateServer(services *service.Services) *medicalCertificateServer {
	return &medicalCertificateServer{
		services: services,
	}
}

// UploadCertificate uploads a new medical certificate
func (s *medicalCertificateServer) UploadCertificate(ctx context.Context, req *sportpb.UploadCertificateRequest) (*sportpb.MedicalCertificateResponse, error) {
	certificate, err := s.services.MedicalCertificate.UploadCertificate(
		ctx,
		req.UserId,
		req.FileUrl,
		req.ValidFrom.AsTime(),
		req.ValidUntil.AsTime(),
	)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to upload certificate: %v", err)
	}

	return s.convertCertificateToProto(certificate), nil
}

// GetCertificate retrieves a medical certificate by ID
func (s *medicalCertificateServer) GetCertificate(ctx context.Context, req *sportpb.GetCertificateRequest) (*sportpb.MedicalCertificateResponse, error) {
	certificate, err := s.services.MedicalCertificate.GetCertificate(ctx, req.Id)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get certificate: %v", err)
	}

	return s.convertCertificateToProto(certificate), nil
}

// ApproveCertificate approves a medical certificate
func (s *medicalCertificateServer) ApproveCertificate(ctx context.Context, req *sportpb.ApproveCertificateRequest) (*sportpb.MedicalCertificateResponse, error) {
	err := s.services.MedicalCertificate.ApproveCertificate(
		ctx,
		req.Id,
		req.ReviewerId,
		req.ValidUntil.AsTime(),
	)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to approve certificate: %v", err)
	}

	certificate, err := s.services.MedicalCertificate.GetCertificate(ctx, req.Id)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get certificate after approval: %v", err)
	}

	return s.convertCertificateToProto(certificate), nil
}

// RejectCertificate rejects a medical certificate
func (s *medicalCertificateServer) RejectCertificate(ctx context.Context, req *sportpb.RejectCertificateRequest) (*sportpb.MedicalCertificateResponse, error) {
	err := s.services.MedicalCertificate.RejectCertificate(
		ctx,
		req.Id,
		req.ReviewerId,
		req.Reason,
	)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to reject certificate: %v", err)
	}

	certificate, err := s.services.MedicalCertificate.GetCertificate(ctx, req.Id)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get certificate after rejection: %v", err)
	}

	return s.convertCertificateToProto(certificate), nil
}

// ListUserCertificates lists certificates for a user
func (s *medicalCertificateServer) ListUserCertificates(ctx context.Context, req *sportpb.ListUserCertificatesRequest) (*sportpb.ListCertificatesResponse, error) {
	certificates, err := s.services.MedicalCertificate.GetUserCertificates(ctx, req.UserId)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list user certificates: %v", err)
	}

	response := &sportpb.ListCertificatesResponse{
		Total:    int32(len(certificates)),
		Page:     1,
		PageSize: int32(len(certificates)),
	}

	for _, certificate := range certificates {
		response.Certificates = append(response.Certificates, s.convertCertificateToProto(certificate))
	}

	return response, nil
}

// ListPendingCertificates lists pending certificates
func (s *medicalCertificateServer) ListPendingCertificates(ctx context.Context, req *sportpb.ListPendingCertificatesRequest) (*sportpb.ListCertificatesResponse, error) {
	certificates, total, err := s.services.MedicalCertificate.GetPendingCertificates(
		ctx,
		int(req.Page),
		int(req.PageSize),
	)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list pending certificates: %v", err)
	}

	response := &sportpb.ListCertificatesResponse{
		Total:    int32(total),
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	for _, certificate := range certificates {
		response.Certificates = append(response.Certificates, s.convertCertificateToProto(certificate))
	}

	return response, nil
}

// HasValidCertificate checks if a user has a valid certificate
func (s *medicalCertificateServer) HasValidCertificate(ctx context.Context, req *sportpb.HasValidCertificateRequest) (*sportpb.HasValidCertificateResponse, error) {
	hasValid, err := s.services.MedicalCertificate.HasValidCertificate(ctx, req.UserId)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to check if user has valid certificate: %v", err)
	}

	response := &sportpb.HasValidCertificateResponse{
		HasValidCertificate: hasValid,
	}

	if hasValid {
		// Get the latest certificate
		certificate, err := s.services.MedicalCertificate.GetLatestCertificate(ctx, req.UserId)
		if err == nil {
			response.Certificate = s.convertCertificateToProto(certificate)
		}
	}

	return response, nil
}

// Helper function to convert domain.MedicalCertificate to sportpb.MedicalCertificateResponse
func (s *medicalCertificateServer) convertCertificateToProto(certificate *domain.MedicalCertificate) *sportpb.MedicalCertificateResponse {
	// Convert certificate status
	var status sportpb.CertificateStatus
	switch certificate.Status {
	case domain.CertificateStatusPending:
		status = sportpb.CertificateStatus_CERTIFICATE_PENDING
	case domain.CertificateStatusApproved:
		status = sportpb.CertificateStatus_CERTIFICATE_APPROVED
	case domain.CertificateStatusRejected:
		status = sportpb.CertificateStatus_CERTIFICATE_REJECTED
	default:
		status = sportpb.CertificateStatus_CERTIFICATE_PENDING
	}

	return &sportpb.MedicalCertificateResponse{
		Id:           certificate.ID,
		UserId:       certificate.UserID,
		FileUrl:      certificate.FileURL,
		Status:       status,
		ReviewedBy:   certificate.ReviewedBy,
		RejectReason: certificate.RejectReason,
		ValidFrom:    timestamppb.New(certificate.ValidFrom),
		ValidUntil:   timestamppb.New(certificate.ValidUntil),
		CreatedAt:    timestamppb.New(certificate.CreatedAt),
		UpdatedAt:    timestamppb.New(certificate.UpdatedAt),
		User: &sportpb.UserInfo{
			Id:   certificate.UserID,
			Name: "", // This would need to be fetched from the user service
		},
	}
}
