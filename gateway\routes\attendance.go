package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

// SetupAttendanceRoutes configures HTTP routes for attendance
func SetupAttendanceRoutes(r *gin.Engine, h *handlers.AttendanceHandler) {
	att := r.Group("/attendance")
	{
		att.POST("", h.CreateAttendanceHandler)
		att.GET("", h.ListAttendanceHandler)
		att.PUT("/:id", h.UpdateAttendanceHandler)
		att.DELETE("/:id", h.DeleteAttendanceHandler)
	}
}
