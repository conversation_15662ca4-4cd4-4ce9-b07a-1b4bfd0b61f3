// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.2
// source: pb/attendance/attendance.proto

package attendancepb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AttendanceService_CreateAttendance_FullMethodName              = "/attendancepb.AttendanceService/CreateAttendance"
	AttendanceService_ListAttendance_FullMethodName                = "/attendancepb.AttendanceService/ListAttendance"
	AttendanceService_UpdateAttendance_FullMethodName              = "/attendancepb.AttendanceService/UpdateAttendance"
	AttendanceService_DeleteAttendanceByID_FullMethodName          = "/attendancepb.AttendanceService/DeleteAttendanceByID"
	AttendanceService_DeleteAttendancesByThreadUser_FullMethodName = "/attendancepb.AttendanceService/DeleteAttendancesByThreadUser"
)

// AttendanceServiceClient is the client API for AttendanceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// AttendanceService manages attendance records for threads
type AttendanceServiceClient interface {
	CreateAttendance(ctx context.Context, in *AttendanceRequest, opts ...grpc.CallOption) (*AttendanceResponse, error)
	ListAttendance(ctx context.Context, in *ListAttendanceRequest, opts ...grpc.CallOption) (*ListAttendanceResponse, error)
	UpdateAttendance(ctx context.Context, in *UpdateAttendanceRequest, opts ...grpc.CallOption) (*AttendanceResponse, error)
	DeleteAttendanceByID(ctx context.Context, in *AttendanceIDRequest, opts ...grpc.CallOption) (*AttendanceEmptyResponse, error)
	DeleteAttendancesByThreadUser(ctx context.Context, in *AttendancesByThreadUser, opts ...grpc.CallOption) (*AttendanceEmptyResponse, error)
}

type attendanceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAttendanceServiceClient(cc grpc.ClientConnInterface) AttendanceServiceClient {
	return &attendanceServiceClient{cc}
}

func (c *attendanceServiceClient) CreateAttendance(ctx context.Context, in *AttendanceRequest, opts ...grpc.CallOption) (*AttendanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AttendanceResponse)
	err := c.cc.Invoke(ctx, AttendanceService_CreateAttendance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *attendanceServiceClient) ListAttendance(ctx context.Context, in *ListAttendanceRequest, opts ...grpc.CallOption) (*ListAttendanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAttendanceResponse)
	err := c.cc.Invoke(ctx, AttendanceService_ListAttendance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *attendanceServiceClient) UpdateAttendance(ctx context.Context, in *UpdateAttendanceRequest, opts ...grpc.CallOption) (*AttendanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AttendanceResponse)
	err := c.cc.Invoke(ctx, AttendanceService_UpdateAttendance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *attendanceServiceClient) DeleteAttendanceByID(ctx context.Context, in *AttendanceIDRequest, opts ...grpc.CallOption) (*AttendanceEmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AttendanceEmptyResponse)
	err := c.cc.Invoke(ctx, AttendanceService_DeleteAttendanceByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *attendanceServiceClient) DeleteAttendancesByThreadUser(ctx context.Context, in *AttendancesByThreadUser, opts ...grpc.CallOption) (*AttendanceEmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AttendanceEmptyResponse)
	err := c.cc.Invoke(ctx, AttendanceService_DeleteAttendancesByThreadUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AttendanceServiceServer is the server API for AttendanceService service.
// All implementations must embed UnimplementedAttendanceServiceServer
// for forward compatibility.
//
// AttendanceService manages attendance records for threads
type AttendanceServiceServer interface {
	CreateAttendance(context.Context, *AttendanceRequest) (*AttendanceResponse, error)
	ListAttendance(context.Context, *ListAttendanceRequest) (*ListAttendanceResponse, error)
	UpdateAttendance(context.Context, *UpdateAttendanceRequest) (*AttendanceResponse, error)
	DeleteAttendanceByID(context.Context, *AttendanceIDRequest) (*AttendanceEmptyResponse, error)
	DeleteAttendancesByThreadUser(context.Context, *AttendancesByThreadUser) (*AttendanceEmptyResponse, error)
	mustEmbedUnimplementedAttendanceServiceServer()
}

// UnimplementedAttendanceServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAttendanceServiceServer struct{}

func (UnimplementedAttendanceServiceServer) CreateAttendance(context.Context, *AttendanceRequest) (*AttendanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAttendance not implemented")
}
func (UnimplementedAttendanceServiceServer) ListAttendance(context.Context, *ListAttendanceRequest) (*ListAttendanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAttendance not implemented")
}
func (UnimplementedAttendanceServiceServer) UpdateAttendance(context.Context, *UpdateAttendanceRequest) (*AttendanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAttendance not implemented")
}
func (UnimplementedAttendanceServiceServer) DeleteAttendanceByID(context.Context, *AttendanceIDRequest) (*AttendanceEmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAttendanceByID not implemented")
}
func (UnimplementedAttendanceServiceServer) DeleteAttendancesByThreadUser(context.Context, *AttendancesByThreadUser) (*AttendanceEmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAttendancesByThreadUser not implemented")
}
func (UnimplementedAttendanceServiceServer) mustEmbedUnimplementedAttendanceServiceServer() {}
func (UnimplementedAttendanceServiceServer) testEmbeddedByValue()                           {}

// UnsafeAttendanceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AttendanceServiceServer will
// result in compilation errors.
type UnsafeAttendanceServiceServer interface {
	mustEmbedUnimplementedAttendanceServiceServer()
}

func RegisterAttendanceServiceServer(s grpc.ServiceRegistrar, srv AttendanceServiceServer) {
	// If the following call pancis, it indicates UnimplementedAttendanceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AttendanceService_ServiceDesc, srv)
}

func _AttendanceService_CreateAttendance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AttendanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttendanceServiceServer).CreateAttendance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AttendanceService_CreateAttendance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttendanceServiceServer).CreateAttendance(ctx, req.(*AttendanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AttendanceService_ListAttendance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAttendanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttendanceServiceServer).ListAttendance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AttendanceService_ListAttendance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttendanceServiceServer).ListAttendance(ctx, req.(*ListAttendanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AttendanceService_UpdateAttendance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAttendanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttendanceServiceServer).UpdateAttendance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AttendanceService_UpdateAttendance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttendanceServiceServer).UpdateAttendance(ctx, req.(*UpdateAttendanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AttendanceService_DeleteAttendanceByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AttendanceIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttendanceServiceServer).DeleteAttendanceByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AttendanceService_DeleteAttendanceByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttendanceServiceServer).DeleteAttendanceByID(ctx, req.(*AttendanceIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AttendanceService_DeleteAttendancesByThreadUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AttendancesByThreadUser)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AttendanceServiceServer).DeleteAttendancesByThreadUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AttendanceService_DeleteAttendancesByThreadUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AttendanceServiceServer).DeleteAttendancesByThreadUser(ctx, req.(*AttendancesByThreadUser))
	}
	return interceptor(ctx, in, info, handler)
}

// AttendanceService_ServiceDesc is the grpc.ServiceDesc for AttendanceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AttendanceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "attendancepb.AttendanceService",
	HandlerType: (*AttendanceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAttendance",
			Handler:    _AttendanceService_CreateAttendance_Handler,
		},
		{
			MethodName: "ListAttendance",
			Handler:    _AttendanceService_ListAttendance_Handler,
		},
		{
			MethodName: "UpdateAttendance",
			Handler:    _AttendanceService_UpdateAttendance_Handler,
		},
		{
			MethodName: "DeleteAttendanceByID",
			Handler:    _AttendanceService_DeleteAttendanceByID_Handler,
		},
		{
			MethodName: "DeleteAttendancesByThreadUser",
			Handler:    _AttendanceService_DeleteAttendancesByThreadUser_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/attendance/attendance.proto",
}
