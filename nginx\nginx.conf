worker_processes auto;
events {
    worker_connections 1024;
}

http {
    upstream user_service {
        server edunite-server-user_service-1:50051;
    }

    server {
        listen 80 http2;

        location /user-service/ {
            grpc_pass grpc://user_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
