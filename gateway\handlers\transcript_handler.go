package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	transcriptpb "github.com/olzzhas/edunite-server/course_service/pb/transcript"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// TranscriptHandler handles transcript-related HTTP requests
type TranscriptHandler struct {
	TranscriptClient transcriptpb.TranscriptServiceClient
}

// NewTranscriptHandler creates a new transcript handler
func NewTranscriptHandler(transcriptClient transcriptpb.TranscriptServiceClient) *TranscriptHandler {
	return &TranscriptHandler{
		TranscriptClient: transcriptClient,
	}
}

// Request/Response DTOs
type CreateDegreeRequest struct {
	Name            string  `json:"name" binding:"required"`
	Level           string  `json:"level" binding:"required"`
	Description     string  `json:"description"`
	RequiredCredits int32   `json:"required_credits" binding:"required,min=1"`
	MinGPA          float64 `json:"min_gpa" binding:"min=0,max=4"`
}

type CreateTranscriptRequest struct {
	UserID   int64 `json:"user_id" binding:"required,min=1"`
	DegreeID int64 `json:"degree_id,omitempty"`
}

type AddTranscriptEntryRequest struct {
	TranscriptID   int64   `json:"transcript_id" binding:"required,min=1"`
	CourseID       int64   `json:"course_id" binding:"required,min=1"`
	ThreadID       int64   `json:"thread_id,omitempty"`
	SemesterID     int64   `json:"semester_id" binding:"required,min=1"`
	GradeLetter    string  `json:"grade_letter,omitempty"`
	GradeNumeric   float64 `json:"grade_numeric,omitempty"`
	GradePoints    float64 `json:"grade_points,omitempty"`
	Credits        int32   `json:"credits" binding:"required,min=1"`
	IsTransfer     bool    `json:"is_transfer"`
	IsRepeated     bool    `json:"is_repeated"`
	CompletionDate string  `json:"completion_date,omitempty"` // ISO 8601 format
}

type CreateStudentDegreeRequest struct {
	UserID                 int64  `json:"user_id" binding:"required,min=1"`
	DegreeID               int64  `json:"degree_id" binding:"required,min=1"`
	StartDate              string `json:"start_date" binding:"required"`      // ISO 8601 format
	ExpectedGraduationDate string `json:"expected_graduation_date,omitempty"` // ISO 8601 format
}

type UpdateDegreeStatusRequest struct {
	Status               string  `json:"status" binding:"required"`
	ActualGraduationDate string  `json:"actual_graduation_date,omitempty"` // ISO 8601 format
	FinalGPA             float64 `json:"final_gpa,omitempty"`
}

// Helper functions
func stringToDegreeLevel(level string) transcriptpb.DegreeLevel {
	switch level {
	case "bachelor":
		return transcriptpb.DegreeLevel_BACHELOR
	case "master":
		return transcriptpb.DegreeLevel_MASTER
	case "phd":
		return transcriptpb.DegreeLevel_PHD
	case "certificate":
		return transcriptpb.DegreeLevel_CERTIFICATE
	case "diploma":
		return transcriptpb.DegreeLevel_DIPLOMA
	default:
		return transcriptpb.DegreeLevel_BACHELOR
	}
}

func stringToDegreeStatus(status string) transcriptpb.DegreeStatus {
	switch status {
	case "in_progress":
		return transcriptpb.DegreeStatus_IN_PROGRESS
	case "completed":
		return transcriptpb.DegreeStatus_COMPLETED
	case "withdrawn":
		return transcriptpb.DegreeStatus_WITHDRAWN
	case "transferred":
		return transcriptpb.DegreeStatus_TRANSFERRED
	default:
		return transcriptpb.DegreeStatus_IN_PROGRESS
	}
}

func parseTimeString(timeStr string) (*timestamppb.Timestamp, error) {
	if timeStr == "" {
		return nil, nil
	}

	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		// Try parsing as date only
		t, err = time.Parse("2006-01-02", timeStr)
		if err != nil {
			return nil, err
		}
	}

	return timestamppb.New(t), nil
}

// Degree management endpoints

// CreateDegree creates a new degree program
// @Summary Create a new degree program
// @Description Create a new degree program with specified details
// @Tags transcript
// @Accept json
// @Produce json
// @Param request body CreateDegreeRequest true "Degree creation request"
// @Success 201 {object} map[string]interface{} "Degree created successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /degrees [post]
func (h *TranscriptHandler) CreateDegree(c *gin.Context) {
	var req CreateDegreeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.TranscriptClient.CreateDegree(c.Request.Context(), &transcriptpb.CreateDegreeRequest{
		Name:            req.Name,
		Level:           stringToDegreeLevel(req.Level),
		Description:     req.Description,
		RequiredCredits: req.RequiredCredits,
		MinGpa:          req.MinGPA,
	})

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Degree created successfully",
		"degree":  resp.Degree,
	})
}

// GetDegree retrieves a degree by ID
// @Summary Get degree by ID
// @Description Retrieve a degree program by its ID
// @Tags transcript
// @Produce json
// @Param id path int true "Degree ID"
// @Success 200 {object} map[string]interface{} "Degree retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 404 {object} map[string]interface{} "Degree not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /degrees/{id} [get]
func (h *TranscriptHandler) GetDegree(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid degree ID"})
		return
	}

	resp, err := h.TranscriptClient.GetDegree(c.Request.Context(), &transcriptpb.GetDegreeRequest{
		Id: id,
	})

	if err != nil {
		if err.Error() == "rpc error: code = NotFound desc = degree not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Degree not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"degree": resp.Degree,
	})
}

// ListDegrees retrieves a list of degrees with pagination
// @Summary List degrees
// @Description Retrieve a paginated list of degree programs
// @Tags transcript
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 10, max: 100)"
// @Param level query string false "Filter by degree level (bachelor, master, phd, certificate, diploma)"
// @Success 200 {object} map[string]interface{} "Degrees retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /degrees [get]
func (h *TranscriptHandler) ListDegrees(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	level := c.Query("level")

	req := &transcriptpb.ListDegreesRequest{
		Page:     int32(page),
		PageSize: int32(pageSize),
	}

	if level != "" {
		req.Level = stringToDegreeLevel(level)
	}

	resp, err := h.TranscriptClient.ListDegrees(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"degrees":     resp.Degrees,
		"total_count": resp.TotalCount,
		"page":        page,
		"page_size":   pageSize,
	})
}

// Transcript management endpoints

// CreateTranscript creates a new academic transcript
// @Summary Create a new academic transcript
// @Description Create a new academic transcript for a student
// @Tags transcript
// @Accept json
// @Produce json
// @Param request body CreateTranscriptRequest true "Transcript creation request"
// @Success 201 {object} map[string]interface{} "Transcript created successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /transcripts [post]
func (h *TranscriptHandler) CreateTranscript(c *gin.Context) {
	var req CreateTranscriptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.TranscriptClient.CreateTranscript(c.Request.Context(), &transcriptpb.CreateTranscriptRequest{
		UserId:   req.UserID,
		DegreeId: req.DegreeID,
	})

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":    "Transcript created successfully",
		"transcript": resp.Transcript,
	})
}

// GetTranscript retrieves a student's transcript
// @Summary Get student transcript
// @Description Retrieve a student's academic transcript
// @Tags transcript
// @Produce json
// @Param user_id path int true "User ID"
// @Param degree_id query int false "Degree ID (optional)"
// @Success 200 {object} map[string]interface{} "Transcript retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 404 {object} map[string]interface{} "Transcript not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /transcripts/user/{user_id} [get]
func (h *TranscriptHandler) GetTranscript(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	degreeIDStr := c.Query("degree_id")
	var degreeID int64
	if degreeIDStr != "" {
		degreeID, err = strconv.ParseInt(degreeIDStr, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid degree ID"})
			return
		}
	}

	resp, err := h.TranscriptClient.GetTranscript(c.Request.Context(), &transcriptpb.GetTranscriptRequest{
		UserId:   userID,
		DegreeId: degreeID,
	})

	if err != nil {
		if err.Error() == "rpc error: code = NotFound desc = transcript not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Transcript not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"transcript": resp.Transcript,
	})
}

// AddTranscriptEntry adds a new entry to a transcript
// @Summary Add transcript entry
// @Description Add a new course entry to a student's transcript
// @Tags transcript
// @Accept json
// @Produce json
// @Param request body AddTranscriptEntryRequest true "Transcript entry request"
// @Success 201 {object} map[string]interface{} "Transcript entry added successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /transcripts/entries [post]
func (h *TranscriptHandler) AddTranscriptEntry(c *gin.Context) {
	var req AddTranscriptEntryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	grpcReq := &transcriptpb.AddTranscriptEntryRequest{
		TranscriptId: req.TranscriptID,
		CourseId:     req.CourseID,
		SemesterId:   req.SemesterID,
		Credits:      req.Credits,
		IsTransfer:   req.IsTransfer,
		IsRepeated:   req.IsRepeated,
	}

	if req.ThreadID > 0 {
		grpcReq.ThreadId = req.ThreadID
	}

	if req.GradeLetter != "" {
		grpcReq.GradeLetter = req.GradeLetter
	}

	if req.GradeNumeric != 0 {
		grpcReq.GradeNumeric = req.GradeNumeric
	}

	if req.GradePoints != 0 {
		grpcReq.GradePoints = req.GradePoints
	}

	if req.CompletionDate != "" {
		completionDate, err := parseTimeString(req.CompletionDate)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid completion date format"})
			return
		}
		grpcReq.CompletionDate = completionDate
	}

	resp, err := h.TranscriptClient.AddTranscriptEntry(c.Request.Context(), grpcReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Transcript entry added successfully",
		"entry":   resp.Entry,
	})
}

// GetTranscriptEntries retrieves entries for a transcript
// @Summary Get transcript entries
// @Description Retrieve all entries for a specific transcript
// @Tags transcript
// @Produce json
// @Param transcript_id path int true "Transcript ID"
// @Param semester_id query int false "Semester ID (optional filter)"
// @Success 200 {object} map[string]interface{} "Transcript entries retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /transcripts/{transcript_id}/entries [get]
func (h *TranscriptHandler) GetTranscriptEntries(c *gin.Context) {
	transcriptIDStr := c.Param("transcript_id")
	transcriptID, err := strconv.ParseInt(transcriptIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid transcript ID"})
		return
	}

	semesterIDStr := c.Query("semester_id")
	var semesterID int64
	if semesterIDStr != "" {
		semesterID, err = strconv.ParseInt(semesterIDStr, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid semester ID"})
			return
		}
	}

	resp, err := h.TranscriptClient.GetTranscriptEntries(c.Request.Context(), &transcriptpb.GetTranscriptEntriesRequest{
		TranscriptId: transcriptID,
		SemesterId:   semesterID,
	})

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"entries": resp.Entries,
	})
}

// UpdateGPA recalculates and updates the GPA for a transcript
// @Summary Update transcript GPA
// @Description Recalculate and update the GPA for a specific transcript
// @Tags transcript
// @Produce json
// @Param transcript_id path int true "Transcript ID"
// @Success 200 {object} map[string]interface{} "GPA updated successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /transcripts/{transcript_id}/gpa [put]
func (h *TranscriptHandler) UpdateGPA(c *gin.Context) {
	transcriptIDStr := c.Param("transcript_id")
	transcriptID, err := strconv.ParseInt(transcriptIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid transcript ID"})
		return
	}

	resp, err := h.TranscriptClient.UpdateGPA(c.Request.Context(), &transcriptpb.UpdateGPARequest{
		TranscriptId: transcriptID,
	})

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "GPA updated successfully",
		"transcript": resp.Transcript,
	})
}

// Student degree management endpoints

// CreateStudentDegree creates a new student degree record
// @Summary Create student degree
// @Description Create a new student degree record
// @Tags transcript
// @Accept json
// @Produce json
// @Param request body CreateStudentDegreeRequest true "Student degree creation request"
// @Success 201 {object} map[string]interface{} "Student degree created successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /student-degrees [post]
func (h *TranscriptHandler) CreateStudentDegree(c *gin.Context) {
	var req CreateStudentDegreeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	startDate, err := parseTimeString(req.StartDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start date format"})
		return
	}

	grpcReq := &transcriptpb.CreateStudentDegreeRequest{
		UserId:    req.UserID,
		DegreeId:  req.DegreeID,
		StartDate: startDate,
	}

	if req.ExpectedGraduationDate != "" {
		expectedDate, err := parseTimeString(req.ExpectedGraduationDate)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid expected graduation date format"})
			return
		}
		grpcReq.ExpectedGraduationDate = expectedDate
	}

	resp, err := h.TranscriptClient.CreateStudentDegree(c.Request.Context(), grpcReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":        "Student degree created successfully",
		"student_degree": resp.StudentDegree,
	})
}

// GetStudentDegrees retrieves all degrees for a student
// @Summary Get student degrees
// @Description Retrieve all degree records for a specific student
// @Tags transcript
// @Produce json
// @Param user_id path int true "User ID"
// @Success 200 {object} map[string]interface{} "Student degrees retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /student-degrees/user/{user_id} [get]
func (h *TranscriptHandler) GetStudentDegrees(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	resp, err := h.TranscriptClient.GetStudentDegrees(c.Request.Context(), &transcriptpb.GetStudentDegreesRequest{
		UserId: userID,
	})

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"student_degrees": resp.StudentDegrees,
	})
}

// ListStudentDegrees retrieves a paginated list of all student degrees
// @Summary List all student degrees
// @Description Retrieve a paginated list of all student degrees with optional filters
// @Tags transcript
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 10, max: 100)"
// @Param user_id query int false "Filter by user ID"
// @Param degree_id query int false "Filter by degree ID"
// @Param status query string false "Filter by status (in_progress, completed, withdrawn, transferred)"
// @Success 200 {object} map[string]interface{} "Student degrees retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /student-degrees [get]
func (h *TranscriptHandler) ListStudentDegrees(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	userIDStr := c.Query("user_id")
	var userID int64
	if userIDStr != "" {
		var err error
		userID, err = strconv.ParseInt(userIDStr, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
			return
		}
	}

	degreeIDStr := c.Query("degree_id")
	var degreeID int64
	if degreeIDStr != "" {
		var err error
		degreeID, err = strconv.ParseInt(degreeIDStr, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid degree ID"})
			return
		}
	}

	status := c.Query("status")

	resp, err := h.TranscriptClient.ListStudentDegrees(c.Request.Context(), &transcriptpb.ListStudentDegreesRequest{
		Page:     int32(page),
		PageSize: int32(pageSize),
		UserId:   userID,
		DegreeId: degreeID,
		Status:   status,
	})

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"student_degrees": resp.StudentDegrees,
		"total_count":     resp.TotalCount,
		"page":            page,
		"page_size":       pageSize,
	})
}

// UpdateDegreeStatus updates the status of a student degree
// @Summary Update degree status
// @Description Update the status of a student's degree (e.g., mark as completed)
// @Tags transcript
// @Accept json
// @Produce json
// @Param student_degree_id path int true "Student Degree ID"
// @Param request body UpdateDegreeStatusRequest true "Degree status update request"
// @Success 200 {object} map[string]interface{} "Degree status updated successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 404 {object} map[string]interface{} "Student degree not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /student-degrees/{student_degree_id}/status [put]
func (h *TranscriptHandler) UpdateDegreeStatus(c *gin.Context) {
	studentDegreeIDStr := c.Param("student_degree_id")
	studentDegreeID, err := strconv.ParseInt(studentDegreeIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid student degree ID"})
		return
	}

	var req UpdateDegreeStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	grpcReq := &transcriptpb.UpdateDegreeStatusRequest{
		StudentDegreeId: studentDegreeID,
		Status:          stringToDegreeStatus(req.Status),
	}

	if req.ActualGraduationDate != "" {
		actualDate, err := parseTimeString(req.ActualGraduationDate)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid actual graduation date format"})
			return
		}
		grpcReq.ActualGraduationDate = actualDate
	}

	if req.FinalGPA != 0 {
		grpcReq.FinalGpa = req.FinalGPA
	}

	resp, err := h.TranscriptClient.UpdateDegreeStatus(c.Request.Context(), grpcReq)
	if err != nil {
		if err.Error() == "rpc error: code = NotFound desc = student degree not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Student degree not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":        "Degree status updated successfully",
		"student_degree": resp.StudentDegree,
	})
}

// GenerateTranscriptReport generates a comprehensive transcript report
// @Summary Generate transcript report
// @Description Generate a comprehensive academic transcript report for a student
// @Tags transcript
// @Produce json
// @Param user_id path int true "User ID"
// @Param degree_id query int false "Degree ID (optional)"
// @Param include_transfer_credits query bool false "Include transfer credits (default: true)"
// @Param include_repeated_courses query bool false "Include repeated courses (default: true)"
// @Success 200 {object} map[string]interface{} "Transcript report generated successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 404 {object} map[string]interface{} "Transcript not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /transcripts/user/{user_id}/report [get]
func (h *TranscriptHandler) GenerateTranscriptReport(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	degreeIDStr := c.Query("degree_id")
	var degreeID int64
	if degreeIDStr != "" {
		degreeID, err = strconv.ParseInt(degreeIDStr, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid degree ID"})
			return
		}
	}

	includeTransferCredits := c.DefaultQuery("include_transfer_credits", "true") == "true"
	includeRepeatedCourses := c.DefaultQuery("include_repeated_courses", "true") == "true"

	resp, err := h.TranscriptClient.GenerateTranscriptReport(c.Request.Context(), &transcriptpb.GenerateTranscriptReportRequest{
		UserId:                 userID,
		DegreeId:               degreeID,
		IncludeTransferCredits: includeTransferCredits,
		IncludeRepeatedCourses: includeRepeatedCourses,
	})

	if err != nil {
		if err.Error() == "rpc error: code = NotFound desc = transcript not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Transcript not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"report": resp,
	})
}
