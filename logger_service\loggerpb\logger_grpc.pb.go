// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: loggerpb/logger.proto

package loggerpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	LoggerService_LogEvent_FullMethodName       = "/loggerpb.LoggerService/LogEvent"
	LoggerService_GetLogs_FullMethodName        = "/loggerpb.LoggerService/GetLogs"
	LoggerService_GetLogByID_FullMethodName     = "/loggerpb.LoggerService/GetLogByID"
	LoggerService_CountLogs_FullMethodName      = "/loggerpb.LoggerService/CountLogs"
	LoggerService_GetLogLevels_FullMethodName   = "/loggerpb.LoggerService/GetLogLevels"
	LoggerService_GetLogServices_FullMethodName = "/loggerpb.LoggerService/GetLogServices"
)

// LoggerServiceClient is the client API for LoggerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LoggerServiceClient interface {
	// Existing method for logging events
	LogEvent(ctx context.Context, in *LogEventRequest, opts ...grpc.CallOption) (*LogEventResponse, error)
	// New methods for fetching logs
	GetLogs(ctx context.Context, in *GetLogsRequest, opts ...grpc.CallOption) (*GetLogsResponse, error)
	GetLogByID(ctx context.Context, in *GetLogByIDRequest, opts ...grpc.CallOption) (*LogEntry, error)
	CountLogs(ctx context.Context, in *GetLogsRequest, opts ...grpc.CallOption) (*CountLogsResponse, error)
	GetLogLevels(ctx context.Context, in *GetLogLevelsRequest, opts ...grpc.CallOption) (*GetLogLevelsResponse, error)
	GetLogServices(ctx context.Context, in *GetLogServicesRequest, opts ...grpc.CallOption) (*GetLogServicesResponse, error)
}

type loggerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLoggerServiceClient(cc grpc.ClientConnInterface) LoggerServiceClient {
	return &loggerServiceClient{cc}
}

func (c *loggerServiceClient) LogEvent(ctx context.Context, in *LogEventRequest, opts ...grpc.CallOption) (*LogEventResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LogEventResponse)
	err := c.cc.Invoke(ctx, LoggerService_LogEvent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loggerServiceClient) GetLogs(ctx context.Context, in *GetLogsRequest, opts ...grpc.CallOption) (*GetLogsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLogsResponse)
	err := c.cc.Invoke(ctx, LoggerService_GetLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loggerServiceClient) GetLogByID(ctx context.Context, in *GetLogByIDRequest, opts ...grpc.CallOption) (*LogEntry, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LogEntry)
	err := c.cc.Invoke(ctx, LoggerService_GetLogByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loggerServiceClient) CountLogs(ctx context.Context, in *GetLogsRequest, opts ...grpc.CallOption) (*CountLogsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CountLogsResponse)
	err := c.cc.Invoke(ctx, LoggerService_CountLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loggerServiceClient) GetLogLevels(ctx context.Context, in *GetLogLevelsRequest, opts ...grpc.CallOption) (*GetLogLevelsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLogLevelsResponse)
	err := c.cc.Invoke(ctx, LoggerService_GetLogLevels_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loggerServiceClient) GetLogServices(ctx context.Context, in *GetLogServicesRequest, opts ...grpc.CallOption) (*GetLogServicesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLogServicesResponse)
	err := c.cc.Invoke(ctx, LoggerService_GetLogServices_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LoggerServiceServer is the server API for LoggerService service.
// All implementations must embed UnimplementedLoggerServiceServer
// for forward compatibility.
type LoggerServiceServer interface {
	// Existing method for logging events
	LogEvent(context.Context, *LogEventRequest) (*LogEventResponse, error)
	// New methods for fetching logs
	GetLogs(context.Context, *GetLogsRequest) (*GetLogsResponse, error)
	GetLogByID(context.Context, *GetLogByIDRequest) (*LogEntry, error)
	CountLogs(context.Context, *GetLogsRequest) (*CountLogsResponse, error)
	GetLogLevels(context.Context, *GetLogLevelsRequest) (*GetLogLevelsResponse, error)
	GetLogServices(context.Context, *GetLogServicesRequest) (*GetLogServicesResponse, error)
	mustEmbedUnimplementedLoggerServiceServer()
}

// UnimplementedLoggerServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedLoggerServiceServer struct{}

func (UnimplementedLoggerServiceServer) LogEvent(context.Context, *LogEventRequest) (*LogEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LogEvent not implemented")
}
func (UnimplementedLoggerServiceServer) GetLogs(context.Context, *GetLogsRequest) (*GetLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLogs not implemented")
}
func (UnimplementedLoggerServiceServer) GetLogByID(context.Context, *GetLogByIDRequest) (*LogEntry, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLogByID not implemented")
}
func (UnimplementedLoggerServiceServer) CountLogs(context.Context, *GetLogsRequest) (*CountLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountLogs not implemented")
}
func (UnimplementedLoggerServiceServer) GetLogLevels(context.Context, *GetLogLevelsRequest) (*GetLogLevelsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLogLevels not implemented")
}
func (UnimplementedLoggerServiceServer) GetLogServices(context.Context, *GetLogServicesRequest) (*GetLogServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLogServices not implemented")
}
func (UnimplementedLoggerServiceServer) mustEmbedUnimplementedLoggerServiceServer() {}
func (UnimplementedLoggerServiceServer) testEmbeddedByValue()                       {}

// UnsafeLoggerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LoggerServiceServer will
// result in compilation errors.
type UnsafeLoggerServiceServer interface {
	mustEmbedUnimplementedLoggerServiceServer()
}

func RegisterLoggerServiceServer(s grpc.ServiceRegistrar, srv LoggerServiceServer) {
	// If the following call pancis, it indicates UnimplementedLoggerServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&LoggerService_ServiceDesc, srv)
}

func _LoggerService_LogEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoggerServiceServer).LogEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LoggerService_LogEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoggerServiceServer).LogEvent(ctx, req.(*LogEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoggerService_GetLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoggerServiceServer).GetLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LoggerService_GetLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoggerServiceServer).GetLogs(ctx, req.(*GetLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoggerService_GetLogByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLogByIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoggerServiceServer).GetLogByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LoggerService_GetLogByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoggerServiceServer).GetLogByID(ctx, req.(*GetLogByIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoggerService_CountLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoggerServiceServer).CountLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LoggerService_CountLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoggerServiceServer).CountLogs(ctx, req.(*GetLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoggerService_GetLogLevels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLogLevelsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoggerServiceServer).GetLogLevels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LoggerService_GetLogLevels_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoggerServiceServer).GetLogLevels(ctx, req.(*GetLogLevelsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoggerService_GetLogServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLogServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoggerServiceServer).GetLogServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LoggerService_GetLogServices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoggerServiceServer).GetLogServices(ctx, req.(*GetLogServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// LoggerService_ServiceDesc is the grpc.ServiceDesc for LoggerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LoggerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "loggerpb.LoggerService",
	HandlerType: (*LoggerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LogEvent",
			Handler:    _LoggerService_LogEvent_Handler,
		},
		{
			MethodName: "GetLogs",
			Handler:    _LoggerService_GetLogs_Handler,
		},
		{
			MethodName: "GetLogByID",
			Handler:    _LoggerService_GetLogByID_Handler,
		},
		{
			MethodName: "CountLogs",
			Handler:    _LoggerService_CountLogs_Handler,
		},
		{
			MethodName: "GetLogLevels",
			Handler:    _LoggerService_GetLogLevels_Handler,
		},
		{
			MethodName: "GetLogServices",
			Handler:    _LoggerService_GetLogServices_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "loggerpb/logger.proto",
}
