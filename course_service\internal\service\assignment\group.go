package assignment

import (
	"context"
	"errors"
	"github.com/olzzhas/edunite-server/course_service/internal/database"
	assignmentpb "github.com/olzzhas/edunite-server/course_service/pb/assignment"
	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// CreateAssignmentGroup создает группу с валидацией и проверкой суммарного веса
func (s *Service) CreateAssignmentGroup(ctx context.Context, req *assignmentpb.AssignmentGroupRequest) (*assignmentpb.AssignmentGroupResponse, error) {
	v := validator.New()
	v.Check(req.GetThreadId() > 0, "thread_id", "must be > 0")
	v.Check(req.GetName() != "", "name", "must be provided")
	v.Check(len(req.GetName()) <= 255, "name", "max length is 255")
	v.Check(validator.PermittedValue(req.GetGroupType(), "midterm", "endterm", "final", "custom"), "group_type", "invalid type")
	v.Check(req.GetWeight() > 0 && req.GetWeight() <= 1, "weight", "must be >0 and <=1")
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	existing, err := s.assignmentGroupRepo.GetAssignmentGroupsForThread(ctx, req.GetThreadId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not fetch existing groups: %v", err)
	}
	var total float32
	for _, g := range existing {
		total += g.Weight
	}
	if total+req.GetWeight() > 1.0 {
		return nil, status.Errorf(codes.InvalidArgument, "%v", database.ErrWeightExceeded)
	}

	ag := &database.AssignmentGroup{
		ThreadID:  req.GetThreadId(),
		Name:      req.GetName(),
		GroupType: req.GetGroupType(),
		Weight:    req.GetWeight(),
	}
	if err := s.assignmentGroupRepo.CreateAssignmentGroup(ctx, ag); err != nil {
		return nil, status.Errorf(codes.Internal, "could not create group: %v", err)
	}

	return assignmentGroupToPB(ag), nil
}

// ListAssignmentGroupsForThread
func (s *Service) ListAssignmentGroupsForThread(ctx context.Context, req *assignmentpb.AssignmentGroupsForThread) (*assignmentpb.AssignmentGroupsResponse, error) {
	groups, err := s.assignmentGroupRepo.GetAssignmentGroupsForThread(ctx, req.GetThreadId())
	if err != nil {
		return nil, err
	}
	var resp assignmentpb.AssignmentGroupsResponse
	for _, g := range groups {
		resp.AssignmentGroups = append(resp.AssignmentGroups, assignmentGroupToPB(g))
	}
	return &resp, nil
}

// GetAssignmentGroupByID
func (s *Service) GetAssignmentGroupByID(ctx context.Context, req *assignmentpb.AssignmentGroupByID) (*assignmentpb.AssignmentGroupResponse, error) {
	group, err := s.assignmentGroupRepo.GetAssignmentGroup(ctx, req.GetAssignmentId())
	if err != nil {
		return nil, err
	}
	return assignmentGroupToPB(group), nil
}

// UpdateAssignmentGroupByID обновляет группу с аналогичными проверками
func (s *Service) UpdateAssignmentGroupByID(ctx context.Context, req *assignmentpb.AssignmentGroupUpdateRequest) (*assignmentpb.AssignmentGroupResponse, error) {
	ag, err := s.assignmentGroupRepo.GetAssignmentGroup(ctx, req.GetId())
	if err != nil {
		return nil, status.Errorf(codes.NotFound, "group not found")
	}

	v := validator.New()
	v.Check(req.GetName() != "", "name", "must be provided")
	v.Check(len(req.GetName()) <= 255, "name", "max length is 255")
	v.Check(validator.PermittedValue(req.GetGroupType(), "midterm", "endterm", "final", "custom"), "group_type", "invalid type")
	v.Check(req.GetWeight() > 0 && req.GetWeight() <= 1, "weight", "must be >0 and <=1")
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	existing, err := s.assignmentGroupRepo.GetAssignmentGroupsForThread(ctx, ag.ThreadID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not fetch existing groups: %v", err)
	}
	var total float32
	for _, g := range existing {
		if g.ID != ag.ID {
			total += g.Weight
		}
	}
	if total+req.GetWeight() > 1.0 {
		return nil, status.Errorf(codes.InvalidArgument, "%v", database.ErrWeightExceeded)
	}

	ag.Name = req.GetName()
	ag.GroupType = req.GetGroupType()
	ag.Weight = req.GetWeight()
	if err := s.assignmentGroupRepo.UpdateAssignmentGroup(ctx, ag); err != nil {
		return nil, status.Errorf(codes.Internal, "could not update group: %v", err)
	}

	return assignmentGroupToPB(ag), nil
}

// DeleteAssignmentGroupByID удаляет группу
func (s *Service) DeleteAssignmentGroupByID(ctx context.Context, req *assignmentpb.AssignmentGroupByID) (*assignmentpb.AssignmentEmptyResponse, error) {
	if err := s.assignmentGroupRepo.DeleteAssignmentGroup(ctx, req.GetAssignmentId()); err != nil {
		if errors.Is(err, database.ErrAssignmentGroupNotFound) {
			return nil, status.Errorf(codes.NotFound, err.Error())
		}
		return nil, status.Errorf(codes.Internal, "could not delete group: %v", err)
	}
	return &assignmentpb.AssignmentEmptyResponse{}, nil
}

// ---------------------
// Вспомогательная функция
// ---------------------

func assignmentGroupToPB(ag *database.AssignmentGroup) *assignmentpb.AssignmentGroupResponse {
	return &assignmentpb.AssignmentGroupResponse{
		Id:        ag.ID,
		ThreadId:  ag.ThreadID,
		Name:      ag.Name,
		GroupType: ag.GroupType,
		Weight:    ag.Weight,
		CreatedAt: timestamppb.New(ag.CreatedAt),
		UpdatedAt: timestamppb.New(ag.UpdatedAt),
	}
}
