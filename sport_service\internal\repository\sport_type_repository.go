package repository

import (
	"context"

	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

// SportTypeRepository defines the interface for sport type data access
type SportTypeRepository interface {
	// Create creates a new sport type
	Create(ctx context.Context, sportType *domain.SportType) error

	// GetByID retrieves a sport type by ID
	GetByID(ctx context.Context, id int64) (*domain.SportType, error)

	// Update updates an existing sport type
	Update(ctx context.Context, sportType *domain.SportType) error

	// Delete deletes a sport type by ID
	Delete(ctx context.Context, id int64) error

	// List retrieves sport types based on filters
	List(ctx context.Context, filter domain.SportTypeFilter) ([]*domain.SportType, error)

	// Count counts sport types based on filters
	Count(ctx context.Context, filter domain.SportTypeFilter) (int, error)

	// AssignTeacherToSportType assigns a teacher to a sport type
	AssignTeacherToSportType(ctx context.Context, teacherSportType *domain.TeacherSportType) error

	// RemoveTeacherFromSportType removes a teacher from a sport type
	RemoveTeacherFromSportType(ctx context.Context, id int64, id2 int64) error

	// ListTeachersForSportType lists teachers for a sport type
	ListTeachersForSportType(ctx context.Context, id int64) ([]int64, error)

	// ListSportTypesForTeacher lists sport types for a teacher
	ListSportTypesForTeacher(ctx context.Context, id int64) ([]*domain.SportType, error)
}

// TeacherSportTypeRepository defines the interface for teacher-sport type relationship data access
type TeacherSportTypeRepository interface {
	// Create creates a new teacher-sport type relationship
	Create(ctx context.Context, teacherSportType *domain.TeacherSportType) error

	// GetByTeacherAndSportType retrieves a teacher-sport type relationship by teacher ID and sport type ID
	GetByTeacherAndSportType(ctx context.Context, teacherID, sportTypeID int64) (*domain.TeacherSportType, error)

	// Update updates an existing teacher-sport type relationship
	Update(ctx context.Context, teacherSportType *domain.TeacherSportType) error

	// Delete deletes a teacher-sport type relationship by teacher ID and sport type ID
	Delete(ctx context.Context, teacherID, sportTypeID int64) error

	// ListByTeacher retrieves teacher-sport type relationships by teacher ID
	ListByTeacher(ctx context.Context, teacherID int64) ([]*domain.TeacherSportType, error)

	// ListBySportType retrieves teacher-sport type relationships by sport type ID
	ListBySportType(ctx context.Context, sportTypeID int64) ([]*domain.TeacherSportType, error)

	// List retrieves teacher-sport type relationships based on filters
	List(ctx context.Context, filter domain.TeacherSportTypeFilter) ([]*domain.TeacherSportType, error)

	// Count counts teacher-sport type relationships based on filters
	Count(ctx context.Context, filter domain.TeacherSportTypeFilter) (int, error)

	// CanTeacherReviewCertificates checks if a teacher can review certificates for a sport type
	CanTeacherReviewCertificates(ctx context.Context, teacherID, sportTypeID int64) (bool, error)
}
