package thread

import (
	"context"
	"errors"
	"time"

	"github.com/olzzhas/edunite-server/course_service/internal/database"
	threadpb "github.com/olzzhas/edunite-server/course_service/pb/thread"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// CreateLocation implements the protobuf service method for creating a location
func (s *Service) CreateLocation(
	ctx context.Context, req *threadpb.LocationRequest,
) (*threadpb.LocationResponse, error) {
	// Create a new location
	location := &database.Location{
		Name:        req.GetName(),
		Description: req.GetDescription(),
		Capacity:    int(req.GetCapacity()),
	}

	// Save to database
	if err := s.locationRepo.Create(ctx, location); err != nil {
		return nil, status.Errorf(codes.Internal, "create location: %v", err)
	}

	return locationToPB(location), nil
}

// GetLocationByID implements the protobuf service method for retrieving a location by ID
func (s *Service) GetLocationByID(
	ctx context.Context, req *threadpb.LocationByID,
) (*threadpb.LocationResponse, error) {
	// Get the location
	location, err := s.locationRepo.GetByID(ctx, req.GetLocationId())
	if err != nil {
		if errors.Is(err, database.ErrLocationNotFound) {
			return nil, status.Errorf(codes.NotFound, "location not found")
		}
		return nil, status.Errorf(codes.Internal, "fetch location: %v", err)
	}

	return locationToPB(location), nil
}

// ListLocations implements the protobuf service method for listing all locations
func (s *Service) ListLocations(
	ctx context.Context, req *threadpb.LocationEmptyRequest,
) (*threadpb.LocationsResponse, error) {
	// List locations
	locations, err := s.locationRepo.List(ctx)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "list locations: %v", err)
	}

	// Convert to protobuf response
	resp := &threadpb.LocationsResponse{}
	for _, loc := range locations {
		resp.Locations = append(resp.Locations, locationToPB(loc))
	}

	return resp, nil
}

// UpdateLocation implements the protobuf service method for updating a location
func (s *Service) UpdateLocation(
	ctx context.Context, req *threadpb.LocationUpdateRequest,
) (*threadpb.LocationResponse, error) {
	// Get the existing location
	location, err := s.locationRepo.GetByID(ctx, req.GetId())
	if err != nil {
		if errors.Is(err, database.ErrLocationNotFound) {
			return nil, status.Errorf(codes.NotFound, "location not found")
		}
		return nil, status.Errorf(codes.Internal, "fetch location: %v", err)
	}

	// Update fields
	location.Name = req.GetName()
	location.Description = req.GetDescription()
	location.Capacity = int(req.GetCapacity())

	// Save to database
	if err := s.locationRepo.Update(ctx, location); err != nil {
		return nil, status.Errorf(codes.Internal, "update location: %v", err)
	}

	return locationToPB(location), nil
}

// DeleteLocation implements the protobuf service method for deleting a location
func (s *Service) DeleteLocation(
	ctx context.Context, req *threadpb.LocationByID,
) (*threadpb.ThreadEmptyResponse, error) {
	// Delete the location
	if err := s.locationRepo.Delete(ctx, req.GetLocationId()); err != nil {
		if errors.Is(err, database.ErrLocationNotFound) {
			return nil, status.Errorf(codes.NotFound, "location not found")
		}
		return nil, status.Errorf(codes.Internal, "delete location: %v", err)
	}

	return &threadpb.ThreadEmptyResponse{}, nil
}

// CheckLocationAvailability implements the protobuf service method for checking location availability
func (s *Service) CheckLocationAvailability(
	ctx context.Context, req *threadpb.LocationAvailabilityRequest,
) (*threadpb.LocationAvailabilityResponse, error) {
	// Parse time strings
	startTime, err := time.Parse("15:04:05", req.GetStartTime())
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid start time format: %v", err)
	}

	endTime, err := time.Parse("15:04:05", req.GetEndTime())
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid end time format: %v", err)
	}

	// Check if location is available
	locationOverlap, err := s.scheduleRepo.LocationOverlapExists(ctx,
		req.GetLocationId(),
		int(req.GetDayOfWeek()),
		startTime,
		endTime,
		req.GetExcludeScheduleId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "check location availability: %v", err)
	}

	// LocationOverlapExists returns true if there IS an overlap, so we need to invert the result
	return &threadpb.LocationAvailabilityResponse{
		Available: !locationOverlap,
	}, nil
}

// Helper function to convert database.Location to threadpb.LocationResponse
func locationToPB(location *database.Location) *threadpb.LocationResponse {
	return &threadpb.LocationResponse{
		Id:          location.ID,
		Name:        location.Name,
		Description: location.Description,
		Capacity:    int32(location.Capacity),
		CreatedAt:   timestamppb.New(location.CreatedAt),
		UpdatedAt:   timestamppb.New(location.UpdatedAt),
	}
}
