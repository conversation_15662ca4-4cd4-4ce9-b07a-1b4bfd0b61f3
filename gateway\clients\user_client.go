package clients

import (
	"context"
	"fmt"
	"log"

	"github.com/olzzhas/edunite-server/user_service/pb"

	"google.golang.org/grpc"
)

// UserClient представляет клиент для взаимодействия с User Service через gRPC
type UserClient struct {
	client pb.UserServiceClient
}

// NewUserClient создает новый экземпляр UserClient с подключением к gRPC
func NewUserClient(conn *grpc.ClientConn) *UserClient {
	return &UserClient{
		client: pb.NewUserServiceClient(conn),
	}
}

// CreateUser сохраняет пользователя в базе данных через User Service
func (uc *UserClient) CreateUser(keycloakID, name, surname, email string) error {
	_, err := uc.client.CreateUser(context.Background(), &pb.CreateUserRequest{
		KeycloakID: keycloakID,
		Name:       name,
		Surname:    surname,
		Email:      email,
	})

	if err != nil {
		log.Printf("Failed to save user in User Service: %v", err)
		return fmt.Errorf("failed to create user: %w", err)
	}
	log.Printf("User saved in User Service with Keycloak ID: %s", keycloakID)
	return nil
}

type ListFilters struct {
	Page     int32
	PageSize int32
	Sort     string
	Search   string
	Role     string
}

// GetAllUsers запрашивает список с фильтрами, сортировкой и пагинацией.
func (uc *UserClient) GetAllUsers(ctx context.Context, f ListFilters) (*pb.UsersWithMeta, error) {
	req := &pb.GetAllUsersRequest{
		Filters: &pb.Filters{
			Page:     f.Page,
			PageSize: f.PageSize,
			Sort:     f.Sort,
			Search:   f.Search,
			Role:     f.Role,
		},
	}
	return uc.client.GetAllUsers(ctx, req)
}

func (uc *UserClient) GetUserByEmail(email string) (*pb.UserResponse, error) {
	user, err := uc.client.GetUserByEmail(context.Background(), &pb.GetUserByEmailRequest{Email: email})
	fmt.Println("Email: ", email)
	fmt.Println("User", user)
	if err != nil {
		fmt.Printf("Failed to get user by email in User Service: %v", err)
		return nil, err
	}

	return user, nil
}

func (uc *UserClient) GetUser(id int64) (*pb.UserResponse, error) {
	user, err := uc.client.GetUser(context.Background(), &pb.GetUserRequest{Id: id})
	if err != nil {
		return nil, err
	}
	return user, nil
}

// GetUserByID gets a user by ID string
func (uc *UserClient) GetUserByID(userID string) (*pb.UserResponse, error) {
	// Convert string ID to int64
	var id int64
	_, err := fmt.Sscanf(userID, "%d", &id)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID format: %w", err)
	}

	return uc.GetUser(id)
}

// GetUserWithContext gets a user by ID with the provided context
func (uc *UserClient) GetUserWithContext(ctx context.Context, id int64) (*pb.UserResponse, error) {
	user, err := uc.client.GetUser(ctx, &pb.GetUserRequest{Id: id})
	if err != nil {
		return nil, err
	}
	return user, nil
}

// UpdateUser updates a user with the provided information
func (uc *UserClient) UpdateUser(ctx context.Context, id int64, name, surname, email, role string) (*pb.UserResponse, error) {
	req := &pb.UpdateUserRequest{
		Id:      id,
		Name:    name,
		Surname: surname,
		Email:   email,
		Role:    role,
	}

	user, err := uc.client.UpdateUser(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}
	return user, nil
}
