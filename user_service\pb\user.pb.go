// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: pb/user.proto

package pb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetUserByEmailRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserByEmailRequest) Reset() {
	*x = GetUserByEmailRequest{}
	mi := &file_pb_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserByEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByEmailRequest) ProtoMessage() {}

func (x *GetUserByEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByEmailRequest.ProtoReflect.Descriptor instead.
func (*GetUserByEmailRequest) Descriptor() ([]byte, []int) {
	return file_pb_user_proto_rawDescGZIP(), []int{0}
}

func (x *GetUserByEmailRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type GetUserByKeycloakID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	KeycloakID    string                 `protobuf:"bytes,1,opt,name=keycloakID,proto3" json:"keycloakID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserByKeycloakID) Reset() {
	*x = GetUserByKeycloakID{}
	mi := &file_pb_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserByKeycloakID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByKeycloakID) ProtoMessage() {}

func (x *GetUserByKeycloakID) ProtoReflect() protoreflect.Message {
	mi := &file_pb_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByKeycloakID.ProtoReflect.Descriptor instead.
func (*GetUserByKeycloakID) Descriptor() ([]byte, []int) {
	return file_pb_user_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserByKeycloakID) GetKeycloakID() string {
	if x != nil {
		return x.KeycloakID
	}
	return ""
}

type CreateUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	KeycloakID    string                 `protobuf:"bytes,2,opt,name=keycloakID,proto3" json:"keycloakID,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Surname       string                 `protobuf:"bytes,4,opt,name=surname,proto3" json:"surname,omitempty"`
	Email         string                 `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	Role          string                 `protobuf:"bytes,6,opt,name=role,proto3" json:"role,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserRequest) Reset() {
	*x = CreateUserRequest{}
	mi := &file_pb_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserRequest) ProtoMessage() {}

func (x *CreateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserRequest.ProtoReflect.Descriptor instead.
func (*CreateUserRequest) Descriptor() ([]byte, []int) {
	return file_pb_user_proto_rawDescGZIP(), []int{2}
}

func (x *CreateUserRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateUserRequest) GetKeycloakID() string {
	if x != nil {
		return x.KeycloakID
	}
	return ""
}

func (x *CreateUserRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateUserRequest) GetSurname() string {
	if x != nil {
		return x.Surname
	}
	return ""
}

func (x *CreateUserRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateUserRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

type GetUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserRequest) Reset() {
	*x = GetUserRequest{}
	mi := &file_pb_user_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRequest) ProtoMessage() {}

func (x *GetUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_user_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRequest.ProtoReflect.Descriptor instead.
func (*GetUserRequest) Descriptor() ([]byte, []int) {
	return file_pb_user_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Surname       string                 `protobuf:"bytes,3,opt,name=surname,proto3" json:"surname,omitempty"`
	Role          string                 `protobuf:"bytes,4,opt,name=role,proto3" json:"role,omitempty"`
	Email         string                 `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserRequest) Reset() {
	*x = UpdateUserRequest{}
	mi := &file_pb_user_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserRequest) ProtoMessage() {}

func (x *UpdateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_user_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserRequest) Descriptor() ([]byte, []int) {
	return file_pb_user_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateUserRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateUserRequest) GetSurname() string {
	if x != nil {
		return x.Surname
	}
	return ""
}

func (x *UpdateUserRequest) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *UpdateUserRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type DeleteUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserRequest) Reset() {
	*x = DeleteUserRequest{}
	mi := &file_pb_user_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserRequest) ProtoMessage() {}

func (x *DeleteUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_user_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserRequest) Descriptor() ([]byte, []int) {
	return file_pb_user_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteUserRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	KeycloakID    string                 `protobuf:"bytes,2,opt,name=keycloakID,proto3" json:"keycloakID,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Surname       string                 `protobuf:"bytes,4,opt,name=surname,proto3" json:"surname,omitempty"`
	Email         string                 `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	Role          string                 `protobuf:"bytes,6,opt,name=role,proto3" json:"role,omitempty"`
	CreatedAt     string                 `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     string                 `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Version       int32                  `protobuf:"varint,9,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserResponse) Reset() {
	*x = UserResponse{}
	mi := &file_pb_user_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserResponse) ProtoMessage() {}

func (x *UserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_user_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserResponse.ProtoReflect.Descriptor instead.
func (*UserResponse) Descriptor() ([]byte, []int) {
	return file_pb_user_proto_rawDescGZIP(), []int{6}
}

func (x *UserResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserResponse) GetKeycloakID() string {
	if x != nil {
		return x.KeycloakID
	}
	return ""
}

func (x *UserResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserResponse) GetSurname() string {
	if x != nil {
		return x.Surname
	}
	return ""
}

func (x *UserResponse) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserResponse) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *UserResponse) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *UserResponse) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *UserResponse) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

type UsersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []*UserResponse        `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UsersResponse) Reset() {
	*x = UsersResponse{}
	mi := &file_pb_user_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersResponse) ProtoMessage() {}

func (x *UsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_user_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersResponse.ProtoReflect.Descriptor instead.
func (*UsersResponse) Descriptor() ([]byte, []int) {
	return file_pb_user_proto_rawDescGZIP(), []int{7}
}

func (x *UsersResponse) GetUsers() []*UserResponse {
	if x != nil {
		return x.Users
	}
	return nil
}

type EmptyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmptyRequest) Reset() {
	*x = EmptyRequest{}
	mi := &file_pb_user_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRequest) ProtoMessage() {}

func (x *EmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_user_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRequest.ProtoReflect.Descriptor instead.
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return file_pb_user_proto_rawDescGZIP(), []int{8}
}

type EmptyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	mi := &file_pb_user_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_user_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_pb_user_proto_rawDescGZIP(), []int{9}
}

// pb/user_service.proto
type Filters struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Sort          string                 `protobuf:"bytes,3,opt,name=sort,proto3" json:"sort,omitempty"`
	Search        string                 `protobuf:"bytes,4,opt,name=search,proto3" json:"search,omitempty"`
	Role          string                 `protobuf:"bytes,5,opt,name=role,proto3" json:"role,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Filters) Reset() {
	*x = Filters{}
	mi := &file_pb_user_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Filters) ProtoMessage() {}

func (x *Filters) ProtoReflect() protoreflect.Message {
	mi := &file_pb_user_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Filters.ProtoReflect.Descriptor instead.
func (*Filters) Descriptor() ([]byte, []int) {
	return file_pb_user_proto_rawDescGZIP(), []int{10}
}

func (x *Filters) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *Filters) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *Filters) GetSort() string {
	if x != nil {
		return x.Sort
	}
	return ""
}

func (x *Filters) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

func (x *Filters) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

type GetAllUsersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Filters       *Filters               `protobuf:"bytes,1,opt,name=filters,proto3" json:"filters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllUsersRequest) Reset() {
	*x = GetAllUsersRequest{}
	mi := &file_pb_user_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllUsersRequest) ProtoMessage() {}

func (x *GetAllUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_user_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllUsersRequest.ProtoReflect.Descriptor instead.
func (*GetAllUsersRequest) Descriptor() ([]byte, []int) {
	return file_pb_user_proto_rawDescGZIP(), []int{11}
}

func (x *GetAllUsersRequest) GetFilters() *Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

type UsersWithMeta struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []*UserResponse        `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	CurrentPage   int32                  `protobuf:"varint,2,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	FirstPage     int32                  `protobuf:"varint,4,opt,name=first_page,json=firstPage,proto3" json:"first_page,omitempty"`
	LastPage      int32                  `protobuf:"varint,5,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	TotalRecords  int32                  `protobuf:"varint,6,opt,name=total_records,json=totalRecords,proto3" json:"total_records,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UsersWithMeta) Reset() {
	*x = UsersWithMeta{}
	mi := &file_pb_user_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UsersWithMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersWithMeta) ProtoMessage() {}

func (x *UsersWithMeta) ProtoReflect() protoreflect.Message {
	mi := &file_pb_user_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersWithMeta.ProtoReflect.Descriptor instead.
func (*UsersWithMeta) Descriptor() ([]byte, []int) {
	return file_pb_user_proto_rawDescGZIP(), []int{12}
}

func (x *UsersWithMeta) GetUsers() []*UserResponse {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *UsersWithMeta) GetCurrentPage() int32 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *UsersWithMeta) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *UsersWithMeta) GetFirstPage() int32 {
	if x != nil {
		return x.FirstPage
	}
	return 0
}

func (x *UsersWithMeta) GetLastPage() int32 {
	if x != nil {
		return x.LastPage
	}
	return 0
}

func (x *UsersWithMeta) GetTotalRecords() int32 {
	if x != nil {
		return x.TotalRecords
	}
	return 0
}

var File_pb_user_proto protoreflect.FileDescriptor

const file_pb_user_proto_rawDesc = "" +
	"\n" +
	"\rpb/user.proto\x12\x06userpb\x1a\x1cgoogle/api/annotations.proto\"-\n" +
	"\x15GetUserByEmailRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\"5\n" +
	"\x13GetUserByKeycloakID\x12\x1e\n" +
	"\n" +
	"keycloakID\x18\x01 \x01(\tR\n" +
	"keycloakID\"\x9b\x01\n" +
	"\x11CreateUserRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n" +
	"\n" +
	"keycloakID\x18\x02 \x01(\tR\n" +
	"keycloakID\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x18\n" +
	"\asurname\x18\x04 \x01(\tR\asurname\x12\x14\n" +
	"\x05email\x18\x05 \x01(\tR\x05email\x12\x12\n" +
	"\x04role\x18\x06 \x01(\tR\x04role\" \n" +
	"\x0eGetUserRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"{\n" +
	"\x11UpdateUserRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x18\n" +
	"\asurname\x18\x03 \x01(\tR\asurname\x12\x12\n" +
	"\x04role\x18\x04 \x01(\tR\x04role\x12\x14\n" +
	"\x05email\x18\x05 \x01(\tR\x05email\"#\n" +
	"\x11DeleteUserRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\xee\x01\n" +
	"\fUserResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n" +
	"\n" +
	"keycloakID\x18\x02 \x01(\tR\n" +
	"keycloakID\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x18\n" +
	"\asurname\x18\x04 \x01(\tR\asurname\x12\x14\n" +
	"\x05email\x18\x05 \x01(\tR\x05email\x12\x12\n" +
	"\x04role\x18\x06 \x01(\tR\x04role\x12\x1d\n" +
	"\n" +
	"created_at\x18\a \x01(\tR\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\b \x01(\tR\tupdatedAt\x12\x18\n" +
	"\aversion\x18\t \x01(\x05R\aversion\";\n" +
	"\rUsersResponse\x12*\n" +
	"\x05users\x18\x01 \x03(\v2\x14.userpb.UserResponseR\x05users\"\x0e\n" +
	"\fEmptyRequest\"\x0f\n" +
	"\rEmptyResponse\"z\n" +
	"\aFilters\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x12\n" +
	"\x04sort\x18\x03 \x01(\tR\x04sort\x12\x16\n" +
	"\x06search\x18\x04 \x01(\tR\x06search\x12\x12\n" +
	"\x04role\x18\x05 \x01(\tR\x04role\"?\n" +
	"\x12GetAllUsersRequest\x12)\n" +
	"\afilters\x18\x01 \x01(\v2\x0f.userpb.FiltersR\afilters\"\xdc\x01\n" +
	"\rUsersWithMeta\x12*\n" +
	"\x05users\x18\x01 \x03(\v2\x14.userpb.UserResponseR\x05users\x12!\n" +
	"\fcurrent_page\x18\x02 \x01(\x05R\vcurrentPage\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\x12\x1d\n" +
	"\n" +
	"first_page\x18\x04 \x01(\x05R\tfirstPage\x12\x1b\n" +
	"\tlast_page\x18\x05 \x01(\x05R\blastPage\x12#\n" +
	"\rtotal_records\x18\x06 \x01(\x05R\ftotalRecords2\xdf\x04\n" +
	"\vUserService\x12S\n" +
	"\n" +
	"CreateUser\x12\x19.userpb.CreateUserRequest\x1a\x14.userpb.UserResponse\"\x14\x82\xd3\xe4\x93\x02\x0e:\x01*\"\t/v1/users\x12O\n" +
	"\aGetUser\x12\x16.userpb.GetUserRequest\x1a\x14.userpb.UserResponse\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/v1/users/{id}\x12X\n" +
	"\n" +
	"UpdateUser\x12\x19.userpb.UpdateUserRequest\x1a\x14.userpb.UserResponse\"\x19\x82\xd3\xe4\x93\x02\x13:\x01*\x1a\x0e/v1/users/{id}\x12V\n" +
	"\n" +
	"DeleteUser\x12\x19.userpb.DeleteUserRequest\x1a\x15.userpb.EmptyResponse\"\x16\x82\xd3\xe4\x93\x02\x10*\x0e/v1/users/{id}\x12f\n" +
	"\x0eGetUserByEmail\x12\x1d.userpb.GetUserByEmailRequest\x1a\x14.userpb.UserResponse\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/v1/users/email/{email}\x12L\n" +
	"\x13GetUserByKeycloakID\x12\x1d.userpb.GetUserByEmailRequest\x1a\x14.userpb.UserResponse\"\x00\x12B\n" +
	"\vGetAllUsers\x12\x1a.userpb.GetAllUsersRequest\x1a\x15.userpb.UsersWithMeta\"\x00B\x05Z\x03/pbb\x06proto3"

var (
	file_pb_user_proto_rawDescOnce sync.Once
	file_pb_user_proto_rawDescData []byte
)

func file_pb_user_proto_rawDescGZIP() []byte {
	file_pb_user_proto_rawDescOnce.Do(func() {
		file_pb_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pb_user_proto_rawDesc), len(file_pb_user_proto_rawDesc)))
	})
	return file_pb_user_proto_rawDescData
}

var file_pb_user_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_pb_user_proto_goTypes = []any{
	(*GetUserByEmailRequest)(nil), // 0: userpb.GetUserByEmailRequest
	(*GetUserByKeycloakID)(nil),   // 1: userpb.GetUserByKeycloakID
	(*CreateUserRequest)(nil),     // 2: userpb.CreateUserRequest
	(*GetUserRequest)(nil),        // 3: userpb.GetUserRequest
	(*UpdateUserRequest)(nil),     // 4: userpb.UpdateUserRequest
	(*DeleteUserRequest)(nil),     // 5: userpb.DeleteUserRequest
	(*UserResponse)(nil),          // 6: userpb.UserResponse
	(*UsersResponse)(nil),         // 7: userpb.UsersResponse
	(*EmptyRequest)(nil),          // 8: userpb.EmptyRequest
	(*EmptyResponse)(nil),         // 9: userpb.EmptyResponse
	(*Filters)(nil),               // 10: userpb.Filters
	(*GetAllUsersRequest)(nil),    // 11: userpb.GetAllUsersRequest
	(*UsersWithMeta)(nil),         // 12: userpb.UsersWithMeta
}
var file_pb_user_proto_depIdxs = []int32{
	6,  // 0: userpb.UsersResponse.users:type_name -> userpb.UserResponse
	10, // 1: userpb.GetAllUsersRequest.filters:type_name -> userpb.Filters
	6,  // 2: userpb.UsersWithMeta.users:type_name -> userpb.UserResponse
	2,  // 3: userpb.UserService.CreateUser:input_type -> userpb.CreateUserRequest
	3,  // 4: userpb.UserService.GetUser:input_type -> userpb.GetUserRequest
	4,  // 5: userpb.UserService.UpdateUser:input_type -> userpb.UpdateUserRequest
	5,  // 6: userpb.UserService.DeleteUser:input_type -> userpb.DeleteUserRequest
	0,  // 7: userpb.UserService.GetUserByEmail:input_type -> userpb.GetUserByEmailRequest
	0,  // 8: userpb.UserService.GetUserByKeycloakID:input_type -> userpb.GetUserByEmailRequest
	11, // 9: userpb.UserService.GetAllUsers:input_type -> userpb.GetAllUsersRequest
	6,  // 10: userpb.UserService.CreateUser:output_type -> userpb.UserResponse
	6,  // 11: userpb.UserService.GetUser:output_type -> userpb.UserResponse
	6,  // 12: userpb.UserService.UpdateUser:output_type -> userpb.UserResponse
	9,  // 13: userpb.UserService.DeleteUser:output_type -> userpb.EmptyResponse
	6,  // 14: userpb.UserService.GetUserByEmail:output_type -> userpb.UserResponse
	6,  // 15: userpb.UserService.GetUserByKeycloakID:output_type -> userpb.UserResponse
	12, // 16: userpb.UserService.GetAllUsers:output_type -> userpb.UsersWithMeta
	10, // [10:17] is the sub-list for method output_type
	3,  // [3:10] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_pb_user_proto_init() }
func file_pb_user_proto_init() {
	if File_pb_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pb_user_proto_rawDesc), len(file_pb_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_user_proto_goTypes,
		DependencyIndexes: file_pb_user_proto_depIdxs,
		MessageInfos:      file_pb_user_proto_msgTypes,
	}.Build()
	File_pb_user_proto = out.File
	file_pb_user_proto_goTypes = nil
	file_pb_user_proto_depIdxs = nil
}
