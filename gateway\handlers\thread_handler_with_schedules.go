package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	threadpb "github.com/olzzhas/edunite-server/course_service/pb/thread"
)

// ListThreadsForUserWithSchedulesHandler возвращает все потоки (детальной информацией),
// на которые записан пользователь, включая расписания для каждого потока.
func (h *ThreadHandler) ListThreadsForUserWithSchedulesHandler(c *gin.Context) {
	// ── validate ──────────────────────────────────────────
	userID, err := strconv.ParseInt(c<PERSON><PERSON>("user_id"), 10, 64)
	if err != nil || userID <= 0 {
		c.<PERSON><PERSON><PERSON>(http.StatusBadRequest, gin.H{"error": "invalid user_id"})
		return
	}

	// ── fetch threads from Thread‑service ────────────────
	trResp, err := h.ThreadService.ListThreadsForUser(
		c.Request.Context(), &threadpb.UserThreadsRequest{UserId: userID},
	)
	if err != nil {
		mapGRPCError(c, h.RabbitLogPublisher,
			"thread_user_list",
			fmt.Sprintf("ListThreadsForUser: %v", err),
			map[string]any{"user_id": userID}, err)
		return
	}
	threadsPB := trResp.GetThreads()
	if len(threadsPB) == 0 {
		c.JSON(http.StatusOK, gin.H{"threads": []any{}})
		return
	}

	// ── teacher map exactly as раньше ─────────────────────
	teacherSet := map[int64]struct{}{}
	for _, t := range threadsPB {
		if id := t.Thread.TeacherId; id > 0 {
			teacherSet[id] = struct{}{}
		}
	}
	teachers := map[int64]teacherDTO{}
	for id := range teacherSet {
		u, err := h.UserService.GetUser(id)
		if err != nil {
			h.RabbitLogPublisher.PublishLog("error",
				fmt.Sprintf("GetUser(%d): %v", id, err),
				"user_fetch", map[string]any{"teacher_id": id})
			fmt.Println(fmt.Sprintf("GetUser(%d): %v", id, err))
			continue
		}
		teachers[id] = teacherDTO{
			Id:      u.GetId(),
			Name:    u.GetName(),
			Surname: u.GetSurname(),
			Email:   u.GetEmail(),
		}
	}

	// ── get schedules for all threads ────────────────────
	threadIDs := make([]int64, 0, len(threadsPB))
	for _, t := range threadsPB {
		threadIDs = append(threadIDs, t.Thread.Id)
	}

	schedules := make(map[int64][]*threadpb.ThreadScheduleResponse)
	for _, threadID := range threadIDs {
		scheduleReq := &threadpb.ThreadSchedulesRequest{
			ThreadId: threadID,
		}
		scheduleResp, err := h.ThreadService.ListThreadSchedules(c.Request.Context(), scheduleReq)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error getting thread schedules: %v", err),
				"thread_user_list",
				map[string]any{"thread_id": threadID, "error": err.Error()},
			)
			// Continue even if we can't get schedules for this thread
			continue
		}
		schedules[threadID] = scheduleResp.Schedules
	}

	// ── формируем HTTP‑DTO с teacher и schedules ──────────
	out := make([]threadHTTP, 0, len(threadsPB))
	for _, t := range threadsPB {
		dto := threadHTTP{ThreadWithDetails: t}
		
		// Add teacher information
		if tch, ok := teachers[t.Thread.TeacherId]; ok {
			dto.Teacher = &tch
		}
		
		// Add schedules information
		if threadSchedules, ok := schedules[t.Thread.Id]; ok {
			dto.Schedules = threadSchedules
		} else {
			dto.Schedules = []*threadpb.ThreadScheduleResponse{}
		}
		
		out = append(out, dto)
	}

	c.JSON(http.StatusOK, gin.H{"threads": out})
}
