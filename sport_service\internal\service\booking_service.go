package service

import (
	"context"
	"errors"
	"time"

	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
	"github.com/olzzhas/edunite-server/sport_service/internal/repository"
)

// BookingService handles operations related to sport facility bookings
type BookingService struct {
	bookingRepo            repository.BookingRepository
	scheduleRepo           repository.ScheduleRepository
	facilityRepo           repository.FacilityRepository
	sportTypeRepo          repository.SportTypeRepository
	medicalCertificateRepo repository.MedicalCertificateRepository
	semesterSportLimitRepo repository.SemesterSportLimitRepository
	dailyBookingLimitRepo  repository.DailyBookingLimitRepository
}

// NewBookingService creates a new booking service
func NewBookingService(
	bookingRepo repository.BookingRepository,
	scheduleRepo repository.ScheduleRepository,
	facilityRepo repository.FacilityRepository,
	sportTypeRepo repository.SportTypeRepository,
	medicalCertificateRepo repository.MedicalCertificateRepository,
	semesterSportLimitRepo repository.SemesterSportLimitRepository,
	dailyBookingLimitRepo repository.DailyBookingLimitRepository,
) *BookingService {
	return &BookingService{
		bookingRepo:            bookingRepo,
		scheduleRepo:           scheduleRepo,
		facilityRepo:           facilityRepo,
		sportTypeRepo:          sportTypeRepo,
		medicalCertificateRepo: medicalCertificateRepo,
		semesterSportLimitRepo: semesterSportLimitRepo,
		dailyBookingLimitRepo:  dailyBookingLimitRepo,
	}
}

// CreateBooking creates a new booking with all necessary validations
func (s *BookingService) CreateBooking(ctx context.Context, userID, scheduleID int64) (*domain.Booking, error) {
	// Get the schedule
	schedule, err := s.scheduleRepo.GetByID(ctx, scheduleID)
	if err != nil {
		return nil, err
	}

	// Get the facility
	facility, err := s.facilityRepo.GetByID(ctx, schedule.FacilityID)
	if err != nil {
		return nil, err
	}

	// Check if schedule is full
	bookingCount, err := s.bookingRepo.CountBySchedule(ctx, scheduleID)
	if err != nil {
		return nil, err
	}

	if bookingCount >= facility.MaxCapacity {
		return nil, domain.ErrScheduleFull
	}

	// Check if user already has a booking for this schedule
	_, err = s.bookingRepo.GetByUserAndSchedule(ctx, userID, scheduleID)
	if err == nil {
		return nil, domain.ErrDuplicateBooking
	} else if !errors.Is(err, domain.ErrBookingNotFound) {
		return nil, err
	}

	// Get the sport type to check if it requires a medical certificate
	sportType, err := s.sportTypeRepo.GetByID(ctx, schedule.SportTypeID)
	if err != nil {
		return nil, err
	}

	// Check if medical certificate is required and valid
	if sportType.RequiresCertificate {
		hasValid, err := s.medicalCertificateRepo.HasValidCertificate(ctx, userID, time.Now())
		if err != nil {
			return nil, err
		}
		if !hasValid {
			return nil, domain.ErrInvalidMedicalCertificate
		}
	}

	// Check daily booking limit
	scheduleDate := schedule.StartTime.Truncate(24 * time.Hour)
	userBookingsForDate, err := s.bookingRepo.GetUserBookingsForDate(ctx, userID, scheduleDate)
	if err != nil {
		return nil, err
	}

	dailyLimit, err := s.dailyBookingLimitRepo.GetBySemesterID(ctx, schedule.SemesterID)
	if err != nil && !errors.Is(err, domain.ErrDailyBookingLimitNotFound) {
		return nil, err
	}

	maxDailyBookings := 1 // Default limit
	if dailyLimit != nil {
		maxDailyBookings = dailyLimit.MaxBookingsPerDay
	}

	if len(userBookingsForDate) >= maxDailyBookings {
		return nil, domain.ErrDailyBookingLimit
	}

	// Check semester booking limit
	semesterBookingCount, err := s.bookingRepo.CountUserBookingsInSemester(ctx, userID, schedule.SemesterID, 0) // 0 means all sport types
	if err != nil {
		return nil, err
	}

	semesterLimit, err := s.semesterSportLimitRepo.GetBySemesterID(ctx, schedule.SemesterID)
	if err != nil && !errors.Is(err, domain.ErrSemesterSportLimitNotFound) {
		return nil, err
	}

	maxSemesterBookings := 20 // Default limit
	if semesterLimit != nil {
		maxSemesterBookings = semesterLimit.MaxLessons
	}

	if semesterBookingCount >= maxSemesterBookings {
		return nil, domain.ErrSemesterBookingLimit
	}

	// Create the booking
	booking := &domain.Booking{
		ScheduleID: scheduleID,
		UserID:     userID,
		Status:     domain.BookingStatusConfirmed,
	}

	if err := s.bookingRepo.Create(ctx, booking); err != nil {
		return nil, err
	}

	return booking, nil
}

// CancelBooking cancels a booking if the cancellation deadline hasn't passed
func (s *BookingService) CancelBooking(ctx context.Context, bookingID, userID int64) error {
	// Get the booking
	booking, err := s.bookingRepo.GetByID(ctx, bookingID)
	if err != nil {
		return err
	}

	// Check if the booking belongs to the user
	if booking.UserID != userID {
		return errors.New("booking does not belong to the user")
	}

	// Check if the booking is already cancelled
	if booking.Status == domain.BookingStatusCancelled {
		return errors.New("booking is already cancelled")
	}

	// Get the schedule
	schedule, err := s.scheduleRepo.GetByID(ctx, booking.ScheduleID)
	if err != nil {
		return err
	}

	// Check if cancellation deadline has passed
	now := time.Now()
	if now.After(schedule.CancellationDeadline) {
		return domain.ErrCancellationDeadline
	}

	// Update the booking status
	booking.Status = domain.BookingStatusCancelled
	if err := s.bookingRepo.Update(ctx, booking); err != nil {
		return err
	}

	return nil
}

// GetUserBookings retrieves a user's bookings
func (s *BookingService) GetUserBookings(ctx context.Context, userID int64, filter domain.BookingFilter) ([]*domain.Booking, int, error) {
	filter.UserID = userID
	bookings, err := s.bookingRepo.List(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	count, err := s.bookingRepo.Count(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	return bookings, count, nil
}

// GetScheduleBookings retrieves bookings for a schedule
func (s *BookingService) GetScheduleBookings(ctx context.Context, scheduleID int64, filter domain.BookingFilter) ([]*domain.Booking, int, error) {
	filter.ScheduleID = scheduleID
	bookings, err := s.bookingRepo.List(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	count, err := s.bookingRepo.Count(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	return bookings, count, nil
}

// GetUserSemesterStats retrieves a user's statistics for a semester
func (s *BookingService) GetUserSemesterStats(ctx context.Context, userID, semesterID int64) (*domain.UserSemesterStats, error) {
	// Implementation would depend on how you want to calculate these statistics
	// This is a placeholder for the actual implementation
	return nil, errors.New("not implemented")
}

func (s *BookingService) GetBooking(ctx context.Context, bookingID int64) (*domain.Booking, error) {
	return s.bookingRepo.GetByID(ctx, bookingID)
}

func (s *BookingService) GetBookingStats(ctx context.Context, filter domain.BookingFilter) (*domain.BookingStats, error) {
	return s.bookingRepo.GetStats(ctx, filter)
}

func (s *BookingService) CancelBookingByID(ctx context.Context, bookingID int64) error {
	return s.bookingRepo.CancelBooking(ctx, bookingID)
}

func (s *BookingService) CountBySchedule(ctx context.Context, scheduleID int64) (int, error) {
	return s.bookingRepo.CountBySchedule(ctx, scheduleID)
}
