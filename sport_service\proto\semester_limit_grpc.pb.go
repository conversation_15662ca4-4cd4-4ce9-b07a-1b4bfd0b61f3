// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: proto/semester_limit.proto

package sportpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SemesterLimitService_CreateSemesterLimit_FullMethodName            = "/sportpb.SemesterLimitService/CreateSemesterLimit"
	SemesterLimitService_GetSemesterLimit_FullMethodName               = "/sportpb.SemesterLimitService/GetSemesterLimit"
	SemesterLimitService_GetSemesterLimitBySemester_FullMethodName     = "/sportpb.SemesterLimitService/GetSemesterLimitBySemester"
	SemesterLimitService_UpdateSemesterLimit_FullMethodName            = "/sportpb.SemesterLimitService/UpdateSemesterLimit"
	SemesterLimitService_DeleteSemesterLimit_FullMethodName            = "/sportpb.SemesterLimitService/DeleteSemesterLimit"
	SemesterLimitService_ListSemesterLimits_FullMethodName             = "/sportpb.SemesterLimitService/ListSemesterLimits"
	SemesterLimitService_CreateDailyBookingLimit_FullMethodName        = "/sportpb.SemesterLimitService/CreateDailyBookingLimit"
	SemesterLimitService_GetDailyBookingLimit_FullMethodName           = "/sportpb.SemesterLimitService/GetDailyBookingLimit"
	SemesterLimitService_GetDailyBookingLimitBySemester_FullMethodName = "/sportpb.SemesterLimitService/GetDailyBookingLimitBySemester"
	SemesterLimitService_UpdateDailyBookingLimit_FullMethodName        = "/sportpb.SemesterLimitService/UpdateDailyBookingLimit"
	SemesterLimitService_DeleteDailyBookingLimit_FullMethodName        = "/sportpb.SemesterLimitService/DeleteDailyBookingLimit"
	SemesterLimitService_ListDailyBookingLimits_FullMethodName         = "/sportpb.SemesterLimitService/ListDailyBookingLimits"
)

// SemesterLimitServiceClient is the client API for SemesterLimitService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SemesterLimitServiceClient interface {
	// Create a new semester sport limit
	CreateSemesterLimit(ctx context.Context, in *CreateSemesterLimitRequest, opts ...grpc.CallOption) (*SemesterLimitResponse, error)
	// Get a semester sport limit by ID
	GetSemesterLimit(ctx context.Context, in *GetSemesterLimitRequest, opts ...grpc.CallOption) (*SemesterLimitResponse, error)
	// Get a semester sport limit by semester ID
	GetSemesterLimitBySemester(ctx context.Context, in *GetSemesterLimitBySemesterRequest, opts ...grpc.CallOption) (*SemesterLimitResponse, error)
	// Update an existing semester sport limit
	UpdateSemesterLimit(ctx context.Context, in *UpdateSemesterLimitRequest, opts ...grpc.CallOption) (*SemesterLimitResponse, error)
	// Delete a semester sport limit
	DeleteSemesterLimit(ctx context.Context, in *DeleteSemesterLimitRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// List all semester sport limits
	ListSemesterLimits(ctx context.Context, in *ListSemesterLimitsRequest, opts ...grpc.CallOption) (*ListSemesterLimitsResponse, error)
	// Create a new daily booking limit
	CreateDailyBookingLimit(ctx context.Context, in *CreateDailyBookingLimitRequest, opts ...grpc.CallOption) (*DailyBookingLimitResponse, error)
	// Get a daily booking limit by ID
	GetDailyBookingLimit(ctx context.Context, in *GetDailyBookingLimitRequest, opts ...grpc.CallOption) (*DailyBookingLimitResponse, error)
	// Get a daily booking limit by semester ID
	GetDailyBookingLimitBySemester(ctx context.Context, in *GetDailyBookingLimitBySemesterRequest, opts ...grpc.CallOption) (*DailyBookingLimitResponse, error)
	// Update an existing daily booking limit
	UpdateDailyBookingLimit(ctx context.Context, in *UpdateDailyBookingLimitRequest, opts ...grpc.CallOption) (*DailyBookingLimitResponse, error)
	// Delete a daily booking limit
	DeleteDailyBookingLimit(ctx context.Context, in *DeleteDailyBookingLimitRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// List all daily booking limits
	ListDailyBookingLimits(ctx context.Context, in *ListDailyBookingLimitsRequest, opts ...grpc.CallOption) (*ListDailyBookingLimitsResponse, error)
}

type semesterLimitServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSemesterLimitServiceClient(cc grpc.ClientConnInterface) SemesterLimitServiceClient {
	return &semesterLimitServiceClient{cc}
}

func (c *semesterLimitServiceClient) CreateSemesterLimit(ctx context.Context, in *CreateSemesterLimitRequest, opts ...grpc.CallOption) (*SemesterLimitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SemesterLimitResponse)
	err := c.cc.Invoke(ctx, SemesterLimitService_CreateSemesterLimit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterLimitServiceClient) GetSemesterLimit(ctx context.Context, in *GetSemesterLimitRequest, opts ...grpc.CallOption) (*SemesterLimitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SemesterLimitResponse)
	err := c.cc.Invoke(ctx, SemesterLimitService_GetSemesterLimit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterLimitServiceClient) GetSemesterLimitBySemester(ctx context.Context, in *GetSemesterLimitBySemesterRequest, opts ...grpc.CallOption) (*SemesterLimitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SemesterLimitResponse)
	err := c.cc.Invoke(ctx, SemesterLimitService_GetSemesterLimitBySemester_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterLimitServiceClient) UpdateSemesterLimit(ctx context.Context, in *UpdateSemesterLimitRequest, opts ...grpc.CallOption) (*SemesterLimitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SemesterLimitResponse)
	err := c.cc.Invoke(ctx, SemesterLimitService_UpdateSemesterLimit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterLimitServiceClient) DeleteSemesterLimit(ctx context.Context, in *DeleteSemesterLimitRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SemesterLimitService_DeleteSemesterLimit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterLimitServiceClient) ListSemesterLimits(ctx context.Context, in *ListSemesterLimitsRequest, opts ...grpc.CallOption) (*ListSemesterLimitsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSemesterLimitsResponse)
	err := c.cc.Invoke(ctx, SemesterLimitService_ListSemesterLimits_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterLimitServiceClient) CreateDailyBookingLimit(ctx context.Context, in *CreateDailyBookingLimitRequest, opts ...grpc.CallOption) (*DailyBookingLimitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DailyBookingLimitResponse)
	err := c.cc.Invoke(ctx, SemesterLimitService_CreateDailyBookingLimit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterLimitServiceClient) GetDailyBookingLimit(ctx context.Context, in *GetDailyBookingLimitRequest, opts ...grpc.CallOption) (*DailyBookingLimitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DailyBookingLimitResponse)
	err := c.cc.Invoke(ctx, SemesterLimitService_GetDailyBookingLimit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterLimitServiceClient) GetDailyBookingLimitBySemester(ctx context.Context, in *GetDailyBookingLimitBySemesterRequest, opts ...grpc.CallOption) (*DailyBookingLimitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DailyBookingLimitResponse)
	err := c.cc.Invoke(ctx, SemesterLimitService_GetDailyBookingLimitBySemester_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterLimitServiceClient) UpdateDailyBookingLimit(ctx context.Context, in *UpdateDailyBookingLimitRequest, opts ...grpc.CallOption) (*DailyBookingLimitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DailyBookingLimitResponse)
	err := c.cc.Invoke(ctx, SemesterLimitService_UpdateDailyBookingLimit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterLimitServiceClient) DeleteDailyBookingLimit(ctx context.Context, in *DeleteDailyBookingLimitRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, SemesterLimitService_DeleteDailyBookingLimit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterLimitServiceClient) ListDailyBookingLimits(ctx context.Context, in *ListDailyBookingLimitsRequest, opts ...grpc.CallOption) (*ListDailyBookingLimitsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDailyBookingLimitsResponse)
	err := c.cc.Invoke(ctx, SemesterLimitService_ListDailyBookingLimits_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SemesterLimitServiceServer is the server API for SemesterLimitService service.
// All implementations must embed UnimplementedSemesterLimitServiceServer
// for forward compatibility.
type SemesterLimitServiceServer interface {
	// Create a new semester sport limit
	CreateSemesterLimit(context.Context, *CreateSemesterLimitRequest) (*SemesterLimitResponse, error)
	// Get a semester sport limit by ID
	GetSemesterLimit(context.Context, *GetSemesterLimitRequest) (*SemesterLimitResponse, error)
	// Get a semester sport limit by semester ID
	GetSemesterLimitBySemester(context.Context, *GetSemesterLimitBySemesterRequest) (*SemesterLimitResponse, error)
	// Update an existing semester sport limit
	UpdateSemesterLimit(context.Context, *UpdateSemesterLimitRequest) (*SemesterLimitResponse, error)
	// Delete a semester sport limit
	DeleteSemesterLimit(context.Context, *DeleteSemesterLimitRequest) (*emptypb.Empty, error)
	// List all semester sport limits
	ListSemesterLimits(context.Context, *ListSemesterLimitsRequest) (*ListSemesterLimitsResponse, error)
	// Create a new daily booking limit
	CreateDailyBookingLimit(context.Context, *CreateDailyBookingLimitRequest) (*DailyBookingLimitResponse, error)
	// Get a daily booking limit by ID
	GetDailyBookingLimit(context.Context, *GetDailyBookingLimitRequest) (*DailyBookingLimitResponse, error)
	// Get a daily booking limit by semester ID
	GetDailyBookingLimitBySemester(context.Context, *GetDailyBookingLimitBySemesterRequest) (*DailyBookingLimitResponse, error)
	// Update an existing daily booking limit
	UpdateDailyBookingLimit(context.Context, *UpdateDailyBookingLimitRequest) (*DailyBookingLimitResponse, error)
	// Delete a daily booking limit
	DeleteDailyBookingLimit(context.Context, *DeleteDailyBookingLimitRequest) (*emptypb.Empty, error)
	// List all daily booking limits
	ListDailyBookingLimits(context.Context, *ListDailyBookingLimitsRequest) (*ListDailyBookingLimitsResponse, error)
	mustEmbedUnimplementedSemesterLimitServiceServer()
}

// UnimplementedSemesterLimitServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSemesterLimitServiceServer struct{}

func (UnimplementedSemesterLimitServiceServer) CreateSemesterLimit(context.Context, *CreateSemesterLimitRequest) (*SemesterLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSemesterLimit not implemented")
}
func (UnimplementedSemesterLimitServiceServer) GetSemesterLimit(context.Context, *GetSemesterLimitRequest) (*SemesterLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSemesterLimit not implemented")
}
func (UnimplementedSemesterLimitServiceServer) GetSemesterLimitBySemester(context.Context, *GetSemesterLimitBySemesterRequest) (*SemesterLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSemesterLimitBySemester not implemented")
}
func (UnimplementedSemesterLimitServiceServer) UpdateSemesterLimit(context.Context, *UpdateSemesterLimitRequest) (*SemesterLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSemesterLimit not implemented")
}
func (UnimplementedSemesterLimitServiceServer) DeleteSemesterLimit(context.Context, *DeleteSemesterLimitRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSemesterLimit not implemented")
}
func (UnimplementedSemesterLimitServiceServer) ListSemesterLimits(context.Context, *ListSemesterLimitsRequest) (*ListSemesterLimitsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSemesterLimits not implemented")
}
func (UnimplementedSemesterLimitServiceServer) CreateDailyBookingLimit(context.Context, *CreateDailyBookingLimitRequest) (*DailyBookingLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDailyBookingLimit not implemented")
}
func (UnimplementedSemesterLimitServiceServer) GetDailyBookingLimit(context.Context, *GetDailyBookingLimitRequest) (*DailyBookingLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDailyBookingLimit not implemented")
}
func (UnimplementedSemesterLimitServiceServer) GetDailyBookingLimitBySemester(context.Context, *GetDailyBookingLimitBySemesterRequest) (*DailyBookingLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDailyBookingLimitBySemester not implemented")
}
func (UnimplementedSemesterLimitServiceServer) UpdateDailyBookingLimit(context.Context, *UpdateDailyBookingLimitRequest) (*DailyBookingLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDailyBookingLimit not implemented")
}
func (UnimplementedSemesterLimitServiceServer) DeleteDailyBookingLimit(context.Context, *DeleteDailyBookingLimitRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDailyBookingLimit not implemented")
}
func (UnimplementedSemesterLimitServiceServer) ListDailyBookingLimits(context.Context, *ListDailyBookingLimitsRequest) (*ListDailyBookingLimitsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDailyBookingLimits not implemented")
}
func (UnimplementedSemesterLimitServiceServer) mustEmbedUnimplementedSemesterLimitServiceServer() {}
func (UnimplementedSemesterLimitServiceServer) testEmbeddedByValue()                              {}

// UnsafeSemesterLimitServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SemesterLimitServiceServer will
// result in compilation errors.
type UnsafeSemesterLimitServiceServer interface {
	mustEmbedUnimplementedSemesterLimitServiceServer()
}

func RegisterSemesterLimitServiceServer(s grpc.ServiceRegistrar, srv SemesterLimitServiceServer) {
	// If the following call pancis, it indicates UnimplementedSemesterLimitServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SemesterLimitService_ServiceDesc, srv)
}

func _SemesterLimitService_CreateSemesterLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSemesterLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterLimitServiceServer).CreateSemesterLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterLimitService_CreateSemesterLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterLimitServiceServer).CreateSemesterLimit(ctx, req.(*CreateSemesterLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterLimitService_GetSemesterLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSemesterLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterLimitServiceServer).GetSemesterLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterLimitService_GetSemesterLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterLimitServiceServer).GetSemesterLimit(ctx, req.(*GetSemesterLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterLimitService_GetSemesterLimitBySemester_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSemesterLimitBySemesterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterLimitServiceServer).GetSemesterLimitBySemester(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterLimitService_GetSemesterLimitBySemester_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterLimitServiceServer).GetSemesterLimitBySemester(ctx, req.(*GetSemesterLimitBySemesterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterLimitService_UpdateSemesterLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSemesterLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterLimitServiceServer).UpdateSemesterLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterLimitService_UpdateSemesterLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterLimitServiceServer).UpdateSemesterLimit(ctx, req.(*UpdateSemesterLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterLimitService_DeleteSemesterLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSemesterLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterLimitServiceServer).DeleteSemesterLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterLimitService_DeleteSemesterLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterLimitServiceServer).DeleteSemesterLimit(ctx, req.(*DeleteSemesterLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterLimitService_ListSemesterLimits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSemesterLimitsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterLimitServiceServer).ListSemesterLimits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterLimitService_ListSemesterLimits_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterLimitServiceServer).ListSemesterLimits(ctx, req.(*ListSemesterLimitsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterLimitService_CreateDailyBookingLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDailyBookingLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterLimitServiceServer).CreateDailyBookingLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterLimitService_CreateDailyBookingLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterLimitServiceServer).CreateDailyBookingLimit(ctx, req.(*CreateDailyBookingLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterLimitService_GetDailyBookingLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDailyBookingLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterLimitServiceServer).GetDailyBookingLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterLimitService_GetDailyBookingLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterLimitServiceServer).GetDailyBookingLimit(ctx, req.(*GetDailyBookingLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterLimitService_GetDailyBookingLimitBySemester_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDailyBookingLimitBySemesterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterLimitServiceServer).GetDailyBookingLimitBySemester(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterLimitService_GetDailyBookingLimitBySemester_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterLimitServiceServer).GetDailyBookingLimitBySemester(ctx, req.(*GetDailyBookingLimitBySemesterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterLimitService_UpdateDailyBookingLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDailyBookingLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterLimitServiceServer).UpdateDailyBookingLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterLimitService_UpdateDailyBookingLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterLimitServiceServer).UpdateDailyBookingLimit(ctx, req.(*UpdateDailyBookingLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterLimitService_DeleteDailyBookingLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDailyBookingLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterLimitServiceServer).DeleteDailyBookingLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterLimitService_DeleteDailyBookingLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterLimitServiceServer).DeleteDailyBookingLimit(ctx, req.(*DeleteDailyBookingLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterLimitService_ListDailyBookingLimits_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDailyBookingLimitsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterLimitServiceServer).ListDailyBookingLimits(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterLimitService_ListDailyBookingLimits_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterLimitServiceServer).ListDailyBookingLimits(ctx, req.(*ListDailyBookingLimitsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SemesterLimitService_ServiceDesc is the grpc.ServiceDesc for SemesterLimitService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SemesterLimitService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sportpb.SemesterLimitService",
	HandlerType: (*SemesterLimitServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSemesterLimit",
			Handler:    _SemesterLimitService_CreateSemesterLimit_Handler,
		},
		{
			MethodName: "GetSemesterLimit",
			Handler:    _SemesterLimitService_GetSemesterLimit_Handler,
		},
		{
			MethodName: "GetSemesterLimitBySemester",
			Handler:    _SemesterLimitService_GetSemesterLimitBySemester_Handler,
		},
		{
			MethodName: "UpdateSemesterLimit",
			Handler:    _SemesterLimitService_UpdateSemesterLimit_Handler,
		},
		{
			MethodName: "DeleteSemesterLimit",
			Handler:    _SemesterLimitService_DeleteSemesterLimit_Handler,
		},
		{
			MethodName: "ListSemesterLimits",
			Handler:    _SemesterLimitService_ListSemesterLimits_Handler,
		},
		{
			MethodName: "CreateDailyBookingLimit",
			Handler:    _SemesterLimitService_CreateDailyBookingLimit_Handler,
		},
		{
			MethodName: "GetDailyBookingLimit",
			Handler:    _SemesterLimitService_GetDailyBookingLimit_Handler,
		},
		{
			MethodName: "GetDailyBookingLimitBySemester",
			Handler:    _SemesterLimitService_GetDailyBookingLimitBySemester_Handler,
		},
		{
			MethodName: "UpdateDailyBookingLimit",
			Handler:    _SemesterLimitService_UpdateDailyBookingLimit_Handler,
		},
		{
			MethodName: "DeleteDailyBookingLimit",
			Handler:    _SemesterLimitService_DeleteDailyBookingLimit_Handler,
		},
		{
			MethodName: "ListDailyBookingLimits",
			Handler:    _SemesterLimitService_ListDailyBookingLimits_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/semester_limit.proto",
}
