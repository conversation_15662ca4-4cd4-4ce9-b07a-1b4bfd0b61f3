// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/medical_certificate.proto

package sportpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Certificate status enum
type CertificateStatus int32

const (
	CertificateStatus_CERTIFICATE_PENDING  CertificateStatus = 0
	CertificateStatus_CERTIFICATE_APPROVED CertificateStatus = 1
	CertificateStatus_CERTIFICATE_REJECTED CertificateStatus = 2
)

// Enum value maps for CertificateStatus.
var (
	CertificateStatus_name = map[int32]string{
		0: "CERTIFICATE_PENDING",
		1: "CERTIFICATE_APPROVED",
		2: "CERTIFICATE_REJECTED",
	}
	CertificateStatus_value = map[string]int32{
		"CERTIFICATE_PENDING":  0,
		"CERTIFICATE_APPROVED": 1,
		"CERTIFICATE_REJECTED": 2,
	}
)

func (x CertificateStatus) Enum() *CertificateStatus {
	p := new(CertificateStatus)
	*p = x
	return p
}

func (x CertificateStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CertificateStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_medical_certificate_proto_enumTypes[0].Descriptor()
}

func (CertificateStatus) Type() protoreflect.EnumType {
	return &file_proto_medical_certificate_proto_enumTypes[0]
}

func (x CertificateStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CertificateStatus.Descriptor instead.
func (CertificateStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_medical_certificate_proto_rawDescGZIP(), []int{0}
}

// Upload certificate request
type UploadCertificateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FileUrl       string                 `protobuf:"bytes,2,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	ValidFrom     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=valid_from,json=validFrom,proto3" json:"valid_from,omitempty"`
	ValidUntil    *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=valid_until,json=validUntil,proto3" json:"valid_until,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadCertificateRequest) Reset() {
	*x = UploadCertificateRequest{}
	mi := &file_proto_medical_certificate_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadCertificateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadCertificateRequest) ProtoMessage() {}

func (x *UploadCertificateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_medical_certificate_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadCertificateRequest.ProtoReflect.Descriptor instead.
func (*UploadCertificateRequest) Descriptor() ([]byte, []int) {
	return file_proto_medical_certificate_proto_rawDescGZIP(), []int{0}
}

func (x *UploadCertificateRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UploadCertificateRequest) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

func (x *UploadCertificateRequest) GetValidFrom() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidFrom
	}
	return nil
}

func (x *UploadCertificateRequest) GetValidUntil() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidUntil
	}
	return nil
}

// Get certificate request
type GetCertificateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCertificateRequest) Reset() {
	*x = GetCertificateRequest{}
	mi := &file_proto_medical_certificate_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCertificateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCertificateRequest) ProtoMessage() {}

func (x *GetCertificateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_medical_certificate_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCertificateRequest.ProtoReflect.Descriptor instead.
func (*GetCertificateRequest) Descriptor() ([]byte, []int) {
	return file_proto_medical_certificate_proto_rawDescGZIP(), []int{1}
}

func (x *GetCertificateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Approve certificate request
type ApproveCertificateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ReviewerId    int64                  `protobuf:"varint,2,opt,name=reviewer_id,json=reviewerId,proto3" json:"reviewer_id,omitempty"`
	ValidUntil    *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=valid_until,json=validUntil,proto3" json:"valid_until,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApproveCertificateRequest) Reset() {
	*x = ApproveCertificateRequest{}
	mi := &file_proto_medical_certificate_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApproveCertificateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApproveCertificateRequest) ProtoMessage() {}

func (x *ApproveCertificateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_medical_certificate_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApproveCertificateRequest.ProtoReflect.Descriptor instead.
func (*ApproveCertificateRequest) Descriptor() ([]byte, []int) {
	return file_proto_medical_certificate_proto_rawDescGZIP(), []int{2}
}

func (x *ApproveCertificateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ApproveCertificateRequest) GetReviewerId() int64 {
	if x != nil {
		return x.ReviewerId
	}
	return 0
}

func (x *ApproveCertificateRequest) GetValidUntil() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidUntil
	}
	return nil
}

// Reject certificate request
type RejectCertificateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ReviewerId    int64                  `protobuf:"varint,2,opt,name=reviewer_id,json=reviewerId,proto3" json:"reviewer_id,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RejectCertificateRequest) Reset() {
	*x = RejectCertificateRequest{}
	mi := &file_proto_medical_certificate_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RejectCertificateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectCertificateRequest) ProtoMessage() {}

func (x *RejectCertificateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_medical_certificate_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectCertificateRequest.ProtoReflect.Descriptor instead.
func (*RejectCertificateRequest) Descriptor() ([]byte, []int) {
	return file_proto_medical_certificate_proto_rawDescGZIP(), []int{3}
}

func (x *RejectCertificateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RejectCertificateRequest) GetReviewerId() int64 {
	if x != nil {
		return x.ReviewerId
	}
	return 0
}

func (x *RejectCertificateRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// List user certificates request
type ListUserCertificatesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserCertificatesRequest) Reset() {
	*x = ListUserCertificatesRequest{}
	mi := &file_proto_medical_certificate_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserCertificatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserCertificatesRequest) ProtoMessage() {}

func (x *ListUserCertificatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_medical_certificate_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserCertificatesRequest.ProtoReflect.Descriptor instead.
func (*ListUserCertificatesRequest) Descriptor() ([]byte, []int) {
	return file_proto_medical_certificate_proto_rawDescGZIP(), []int{4}
}

func (x *ListUserCertificatesRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *ListUserCertificatesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListUserCertificatesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// List pending certificates request
type ListPendingCertificatesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPendingCertificatesRequest) Reset() {
	*x = ListPendingCertificatesRequest{}
	mi := &file_proto_medical_certificate_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPendingCertificatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPendingCertificatesRequest) ProtoMessage() {}

func (x *ListPendingCertificatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_medical_certificate_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPendingCertificatesRequest.ProtoReflect.Descriptor instead.
func (*ListPendingCertificatesRequest) Descriptor() ([]byte, []int) {
	return file_proto_medical_certificate_proto_rawDescGZIP(), []int{5}
}

func (x *ListPendingCertificatesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListPendingCertificatesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Has valid certificate request
type HasValidCertificateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HasValidCertificateRequest) Reset() {
	*x = HasValidCertificateRequest{}
	mi := &file_proto_medical_certificate_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HasValidCertificateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HasValidCertificateRequest) ProtoMessage() {}

func (x *HasValidCertificateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_medical_certificate_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HasValidCertificateRequest.ProtoReflect.Descriptor instead.
func (*HasValidCertificateRequest) Descriptor() ([]byte, []int) {
	return file_proto_medical_certificate_proto_rawDescGZIP(), []int{6}
}

func (x *HasValidCertificateRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// Has valid certificate response
type HasValidCertificateResponse struct {
	state               protoimpl.MessageState      `protogen:"open.v1"`
	HasValidCertificate bool                        `protobuf:"varint,1,opt,name=has_valid_certificate,json=hasValidCertificate,proto3" json:"has_valid_certificate,omitempty"`
	Certificate         *MedicalCertificateResponse `protobuf:"bytes,2,opt,name=certificate,proto3" json:"certificate,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *HasValidCertificateResponse) Reset() {
	*x = HasValidCertificateResponse{}
	mi := &file_proto_medical_certificate_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HasValidCertificateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HasValidCertificateResponse) ProtoMessage() {}

func (x *HasValidCertificateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_medical_certificate_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HasValidCertificateResponse.ProtoReflect.Descriptor instead.
func (*HasValidCertificateResponse) Descriptor() ([]byte, []int) {
	return file_proto_medical_certificate_proto_rawDescGZIP(), []int{7}
}

func (x *HasValidCertificateResponse) GetHasValidCertificate() bool {
	if x != nil {
		return x.HasValidCertificate
	}
	return false
}

func (x *HasValidCertificateResponse) GetCertificate() *MedicalCertificateResponse {
	if x != nil {
		return x.Certificate
	}
	return nil
}

// Medical certificate response
type MedicalCertificateResponse struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	Id           int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId       int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FileUrl      string                 `protobuf:"bytes,3,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	Status       CertificateStatus      `protobuf:"varint,4,opt,name=status,proto3,enum=sportpb.CertificateStatus" json:"status,omitempty"`
	ReviewedBy   int64                  `protobuf:"varint,5,opt,name=reviewed_by,json=reviewedBy,proto3" json:"reviewed_by,omitempty"`
	RejectReason string                 `protobuf:"bytes,6,opt,name=reject_reason,json=rejectReason,proto3" json:"reject_reason,omitempty"`
	ValidFrom    *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=valid_from,json=validFrom,proto3" json:"valid_from,omitempty"`
	ValidUntil   *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=valid_until,json=validUntil,proto3" json:"valid_until,omitempty"`
	CreatedAt    *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt    *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Additional information
	User          *UserInfo `protobuf:"bytes,11,opt,name=user,proto3" json:"user,omitempty"`
	Reviewer      *UserInfo `protobuf:"bytes,12,opt,name=reviewer,proto3" json:"reviewer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MedicalCertificateResponse) Reset() {
	*x = MedicalCertificateResponse{}
	mi := &file_proto_medical_certificate_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MedicalCertificateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MedicalCertificateResponse) ProtoMessage() {}

func (x *MedicalCertificateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_medical_certificate_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MedicalCertificateResponse.ProtoReflect.Descriptor instead.
func (*MedicalCertificateResponse) Descriptor() ([]byte, []int) {
	return file_proto_medical_certificate_proto_rawDescGZIP(), []int{8}
}

func (x *MedicalCertificateResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MedicalCertificateResponse) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *MedicalCertificateResponse) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

func (x *MedicalCertificateResponse) GetStatus() CertificateStatus {
	if x != nil {
		return x.Status
	}
	return CertificateStatus_CERTIFICATE_PENDING
}

func (x *MedicalCertificateResponse) GetReviewedBy() int64 {
	if x != nil {
		return x.ReviewedBy
	}
	return 0
}

func (x *MedicalCertificateResponse) GetRejectReason() string {
	if x != nil {
		return x.RejectReason
	}
	return ""
}

func (x *MedicalCertificateResponse) GetValidFrom() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidFrom
	}
	return nil
}

func (x *MedicalCertificateResponse) GetValidUntil() *timestamppb.Timestamp {
	if x != nil {
		return x.ValidUntil
	}
	return nil
}

func (x *MedicalCertificateResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *MedicalCertificateResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *MedicalCertificateResponse) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *MedicalCertificateResponse) GetReviewer() *UserInfo {
	if x != nil {
		return x.Reviewer
	}
	return nil
}

// List certificates response
type ListCertificatesResponse struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Certificates  []*MedicalCertificateResponse `protobuf:"bytes,1,rep,name=certificates,proto3" json:"certificates,omitempty"`
	Total         int32                         `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                         `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                         `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCertificatesResponse) Reset() {
	*x = ListCertificatesResponse{}
	mi := &file_proto_medical_certificate_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCertificatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCertificatesResponse) ProtoMessage() {}

func (x *ListCertificatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_medical_certificate_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCertificatesResponse.ProtoReflect.Descriptor instead.
func (*ListCertificatesResponse) Descriptor() ([]byte, []int) {
	return file_proto_medical_certificate_proto_rawDescGZIP(), []int{9}
}

func (x *ListCertificatesResponse) GetCertificates() []*MedicalCertificateResponse {
	if x != nil {
		return x.Certificates
	}
	return nil
}

func (x *ListCertificatesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListCertificatesResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListCertificatesResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

var File_proto_medical_certificate_proto protoreflect.FileDescriptor

const file_proto_medical_certificate_proto_rawDesc = "" +
	"\n" +
	"\x1fproto/medical_certificate.proto\x12\asportpb\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x13proto/booking.proto\"\xc6\x01\n" +
	"\x18UploadCertificateRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x19\n" +
	"\bfile_url\x18\x02 \x01(\tR\afileUrl\x129\n" +
	"\n" +
	"valid_from\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tvalidFrom\x12;\n" +
	"\vvalid_until\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"validUntil\"'\n" +
	"\x15GetCertificateRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\x89\x01\n" +
	"\x19ApproveCertificateRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vreviewer_id\x18\x02 \x01(\x03R\n" +
	"reviewerId\x12;\n" +
	"\vvalid_until\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"validUntil\"c\n" +
	"\x18RejectCertificateRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vreviewer_id\x18\x02 \x01(\x03R\n" +
	"reviewerId\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"g\n" +
	"\x1bListUserCertificatesRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\"Q\n" +
	"\x1eListPendingCertificatesRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\"5\n" +
	"\x1aHasValidCertificateRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\"\x98\x01\n" +
	"\x1bHasValidCertificateResponse\x122\n" +
	"\x15has_valid_certificate\x18\x01 \x01(\bR\x13hasValidCertificate\x12E\n" +
	"\vcertificate\x18\x02 \x01(\v2#.sportpb.MedicalCertificateResponseR\vcertificate\"\x9e\x04\n" +
	"\x1aMedicalCertificateResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\x12\x19\n" +
	"\bfile_url\x18\x03 \x01(\tR\afileUrl\x122\n" +
	"\x06status\x18\x04 \x01(\x0e2\x1a.sportpb.CertificateStatusR\x06status\x12\x1f\n" +
	"\vreviewed_by\x18\x05 \x01(\x03R\n" +
	"reviewedBy\x12#\n" +
	"\rreject_reason\x18\x06 \x01(\tR\frejectReason\x129\n" +
	"\n" +
	"valid_from\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tvalidFrom\x12;\n" +
	"\vvalid_until\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"validUntil\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12%\n" +
	"\x04user\x18\v \x01(\v2\x11.sportpb.UserInfoR\x04user\x12-\n" +
	"\breviewer\x18\f \x01(\v2\x11.sportpb.UserInfoR\breviewer\"\xaa\x01\n" +
	"\x18ListCertificatesResponse\x12G\n" +
	"\fcertificates\x18\x01 \x03(\v2#.sportpb.MedicalCertificateResponseR\fcertificates\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize*`\n" +
	"\x11CertificateStatus\x12\x17\n" +
	"\x13CERTIFICATE_PENDING\x10\x00\x12\x18\n" +
	"\x14CERTIFICATE_APPROVED\x10\x01\x12\x18\n" +
	"\x14CERTIFICATE_REJECTED\x10\x022\xc3\x05\n" +
	"\x19MedicalCertificateService\x12]\n" +
	"\x11UploadCertificate\x12!.sportpb.UploadCertificateRequest\x1a#.sportpb.MedicalCertificateResponse\"\x00\x12W\n" +
	"\x0eGetCertificate\x12\x1e.sportpb.GetCertificateRequest\x1a#.sportpb.MedicalCertificateResponse\"\x00\x12_\n" +
	"\x12ApproveCertificate\x12\".sportpb.ApproveCertificateRequest\x1a#.sportpb.MedicalCertificateResponse\"\x00\x12]\n" +
	"\x11RejectCertificate\x12!.sportpb.RejectCertificateRequest\x1a#.sportpb.MedicalCertificateResponse\"\x00\x12a\n" +
	"\x14ListUserCertificates\x12$.sportpb.ListUserCertificatesRequest\x1a!.sportpb.ListCertificatesResponse\"\x00\x12g\n" +
	"\x17ListPendingCertificates\x12'.sportpb.ListPendingCertificatesRequest\x1a!.sportpb.ListCertificatesResponse\"\x00\x12b\n" +
	"\x13HasValidCertificate\x12#.sportpb.HasValidCertificateRequest\x1a$.sportpb.HasValidCertificateResponse\"\x00B<Z:github.com/olzzhas/edunite-server/sport_service/pb/sportpbb\x06proto3"

var (
	file_proto_medical_certificate_proto_rawDescOnce sync.Once
	file_proto_medical_certificate_proto_rawDescData []byte
)

func file_proto_medical_certificate_proto_rawDescGZIP() []byte {
	file_proto_medical_certificate_proto_rawDescOnce.Do(func() {
		file_proto_medical_certificate_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_medical_certificate_proto_rawDesc), len(file_proto_medical_certificate_proto_rawDesc)))
	})
	return file_proto_medical_certificate_proto_rawDescData
}

var file_proto_medical_certificate_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_medical_certificate_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_proto_medical_certificate_proto_goTypes = []any{
	(CertificateStatus)(0),                 // 0: sportpb.CertificateStatus
	(*UploadCertificateRequest)(nil),       // 1: sportpb.UploadCertificateRequest
	(*GetCertificateRequest)(nil),          // 2: sportpb.GetCertificateRequest
	(*ApproveCertificateRequest)(nil),      // 3: sportpb.ApproveCertificateRequest
	(*RejectCertificateRequest)(nil),       // 4: sportpb.RejectCertificateRequest
	(*ListUserCertificatesRequest)(nil),    // 5: sportpb.ListUserCertificatesRequest
	(*ListPendingCertificatesRequest)(nil), // 6: sportpb.ListPendingCertificatesRequest
	(*HasValidCertificateRequest)(nil),     // 7: sportpb.HasValidCertificateRequest
	(*HasValidCertificateResponse)(nil),    // 8: sportpb.HasValidCertificateResponse
	(*MedicalCertificateResponse)(nil),     // 9: sportpb.MedicalCertificateResponse
	(*ListCertificatesResponse)(nil),       // 10: sportpb.ListCertificatesResponse
	(*timestamppb.Timestamp)(nil),          // 11: google.protobuf.Timestamp
	(*UserInfo)(nil),                       // 12: sportpb.UserInfo
}
var file_proto_medical_certificate_proto_depIdxs = []int32{
	11, // 0: sportpb.UploadCertificateRequest.valid_from:type_name -> google.protobuf.Timestamp
	11, // 1: sportpb.UploadCertificateRequest.valid_until:type_name -> google.protobuf.Timestamp
	11, // 2: sportpb.ApproveCertificateRequest.valid_until:type_name -> google.protobuf.Timestamp
	9,  // 3: sportpb.HasValidCertificateResponse.certificate:type_name -> sportpb.MedicalCertificateResponse
	0,  // 4: sportpb.MedicalCertificateResponse.status:type_name -> sportpb.CertificateStatus
	11, // 5: sportpb.MedicalCertificateResponse.valid_from:type_name -> google.protobuf.Timestamp
	11, // 6: sportpb.MedicalCertificateResponse.valid_until:type_name -> google.protobuf.Timestamp
	11, // 7: sportpb.MedicalCertificateResponse.created_at:type_name -> google.protobuf.Timestamp
	11, // 8: sportpb.MedicalCertificateResponse.updated_at:type_name -> google.protobuf.Timestamp
	12, // 9: sportpb.MedicalCertificateResponse.user:type_name -> sportpb.UserInfo
	12, // 10: sportpb.MedicalCertificateResponse.reviewer:type_name -> sportpb.UserInfo
	9,  // 11: sportpb.ListCertificatesResponse.certificates:type_name -> sportpb.MedicalCertificateResponse
	1,  // 12: sportpb.MedicalCertificateService.UploadCertificate:input_type -> sportpb.UploadCertificateRequest
	2,  // 13: sportpb.MedicalCertificateService.GetCertificate:input_type -> sportpb.GetCertificateRequest
	3,  // 14: sportpb.MedicalCertificateService.ApproveCertificate:input_type -> sportpb.ApproveCertificateRequest
	4,  // 15: sportpb.MedicalCertificateService.RejectCertificate:input_type -> sportpb.RejectCertificateRequest
	5,  // 16: sportpb.MedicalCertificateService.ListUserCertificates:input_type -> sportpb.ListUserCertificatesRequest
	6,  // 17: sportpb.MedicalCertificateService.ListPendingCertificates:input_type -> sportpb.ListPendingCertificatesRequest
	7,  // 18: sportpb.MedicalCertificateService.HasValidCertificate:input_type -> sportpb.HasValidCertificateRequest
	9,  // 19: sportpb.MedicalCertificateService.UploadCertificate:output_type -> sportpb.MedicalCertificateResponse
	9,  // 20: sportpb.MedicalCertificateService.GetCertificate:output_type -> sportpb.MedicalCertificateResponse
	9,  // 21: sportpb.MedicalCertificateService.ApproveCertificate:output_type -> sportpb.MedicalCertificateResponse
	9,  // 22: sportpb.MedicalCertificateService.RejectCertificate:output_type -> sportpb.MedicalCertificateResponse
	10, // 23: sportpb.MedicalCertificateService.ListUserCertificates:output_type -> sportpb.ListCertificatesResponse
	10, // 24: sportpb.MedicalCertificateService.ListPendingCertificates:output_type -> sportpb.ListCertificatesResponse
	8,  // 25: sportpb.MedicalCertificateService.HasValidCertificate:output_type -> sportpb.HasValidCertificateResponse
	19, // [19:26] is the sub-list for method output_type
	12, // [12:19] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_proto_medical_certificate_proto_init() }
func file_proto_medical_certificate_proto_init() {
	if File_proto_medical_certificate_proto != nil {
		return
	}
	file_proto_booking_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_medical_certificate_proto_rawDesc), len(file_proto_medical_certificate_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_medical_certificate_proto_goTypes,
		DependencyIndexes: file_proto_medical_certificate_proto_depIdxs,
		EnumInfos:         file_proto_medical_certificate_proto_enumTypes,
		MessageInfos:      file_proto_medical_certificate_proto_msgTypes,
	}.Build()
	File_proto_medical_certificate_proto = out.File
	file_proto_medical_certificate_proto_goTypes = nil
	file_proto_medical_certificate_proto_depIdxs = nil
}
