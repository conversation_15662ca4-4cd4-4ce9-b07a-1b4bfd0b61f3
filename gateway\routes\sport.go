package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

// SetupSportRoutes sets up routes for sport-related endpoints
func SetupSportRoutes(r *gin.Engine, authClient *clients.AuthClient, sportHandler *handlers.SportHandler) {
	// Sport Type routes
	sportTypeGroup := r.Group("/sport-types")
	//sportTypeGroup.Use(AuthMiddleware(authClient))
	{
		sportTypeGroup.POST("", sportHandler.CreateSportType)
		sportTypeGroup.GET("", sportHandler.ListSportTypes)
		sportTypeGroup.GET("/:id", sportHandler.GetSportType)
		sportTypeGroup.PUT("/:id", sportHandler.UpdateSportType)
		sportTypeGroup.DELETE("/:id", sportHandler.DeleteSportType)
	}

	// Facility routes
	facilityGroup := r.Group("/facilities")
	//facilityGroup.Use(AuthMiddleware(authClient))
	{
		facilityGroup.POST("", sportHandler.CreateFacility)
		facilityGroup.GET("", sportHandler.ListFacilities)
		facilityGroup.GET("/:id", sportHandler.GetFacility)
		facilityGroup.PUT("/:id", sportHandler.UpdateFacility)
		facilityGroup.DELETE("/:id", sportHandler.DeleteFacility)
	}

	// Physical Education routes
	peGroup := r.Group("/physical-education")
	//peGroup.Use(AuthMiddleware(authClient))
	{
		peGroup.GET("/sport-types", sportHandler.GetAvailableSportTypes)
		peGroup.GET("/facilities", sportHandler.GetAvailableFacilities)
		peGroup.POST("/book", sportHandler.BookSession)
		peGroup.DELETE("/bookings/:id", sportHandler.CancelBooking)
	}

	// Schedule routes
	scheduleGroup := r.Group("/schedules")
	//scheduleGroup.Use(AuthMiddleware(authClient))
	{
		scheduleGroup.GET("", sportHandler.ListSchedules)
		scheduleGroup.GET("/:id", sportHandler.GetSchedule)
		scheduleGroup.POST("/weekly", sportHandler.CreateWeeklySchedules)
		scheduleGroup.POST("/sport-patterns", sportHandler.CreateSportSchedules)
	}
}
