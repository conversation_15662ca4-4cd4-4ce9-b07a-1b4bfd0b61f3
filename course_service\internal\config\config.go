package config

import "os"

type Config struct {
	DatabaseURL string
	Database    Database
}

type Database struct {
	CA       string
	Host     string
	Port     string
	User     string
	Password string
	Name     string
}

func LoadConfig() *Config {

	database := Database{
		CA:       os.Getenv("DATABASE_CA"),
		Host:     os.<PERSON><PERSON><PERSON>("DATABASE_HOST"),
		Port:     os.Getenv("DATABASE_PORT"),
		User:     os.<PERSON>("DATABASE_USER"),
		Password: os.<PERSON>("DATABASE_PASSWORD"),
		Name:     os.<PERSON><PERSON><PERSON>("DATABASE_NAME"),
	}

	return &Config{
		DatabaseURL: os.Getenv("DATABASE_URL"),
		Database:    database,
	}
}
