-- Create sport type category enum (CockroachDB compatible)
CREATE TYPE sport_type_category AS ENUM (
    'normal',
    'lfk'
);

-- CockroachDB doesn't support replacing trigger functions with active triggers
-- We'll skip recreating the function since it already exists from previous migrations

-- Create sport types table
CREATE TABLE IF NOT EXISTS sport_types (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category sport_type_category NOT NULL DEFAULT 'normal',
    requires_certificate BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INT NOT NULL DEFAULT 1
);

-- Create trigger for updated_at
    CREATE TRIGGER trg_sport_types_updated
        BEFORE UPDATE ON sport_types
        FOR EACH ROW
        EXECUTE FUNCTION update_timestamp();

-- Create teacher sport types table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS teacher_sport_types (
    teacher_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    sport_type_id BIGINT NOT NULL REFERENCES sport_types(id) ON DELETE CASCADE,
    can_review_certificates BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (teacher_id, sport_type_id)
);

-- Create trigger for updated_at
CREATE TRIGGER trg_teacher_sport_types_updated
    BEFORE UPDATE ON teacher_sport_types
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();
