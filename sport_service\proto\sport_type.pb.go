// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/sport_type.proto

package sportpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Sport type category enum
type SportTypeCategory int32

const (
	SportTypeCategory_NORMAL SportTypeCategory = 0
	SportTypeCategory_LFK    SportTypeCategory = 1
)

// Enum value maps for SportTypeCategory.
var (
	SportTypeCategory_name = map[int32]string{
		0: "NORMAL",
		1: "LFK",
	}
	SportTypeCategory_value = map[string]int32{
		"NORMAL": 0,
		"LFK":    1,
	}
)

func (x SportTypeCategory) Enum() *SportTypeCategory {
	p := new(SportTypeCategory)
	*p = x
	return p
}

func (x SportTypeCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SportTypeCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_sport_type_proto_enumTypes[0].Descriptor()
}

func (SportTypeCategory) Type() protoreflect.EnumType {
	return &file_proto_sport_type_proto_enumTypes[0]
}

func (x SportTypeCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SportTypeCategory.Descriptor instead.
func (SportTypeCategory) EnumDescriptor() ([]byte, []int) {
	return file_proto_sport_type_proto_rawDescGZIP(), []int{0}
}

// Create sport type request
type CreateSportTypeRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Title               string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Description         string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Category            SportTypeCategory      `protobuf:"varint,3,opt,name=category,proto3,enum=sportpb.SportTypeCategory" json:"category,omitempty"`
	RequiresCertificate bool                   `protobuf:"varint,4,opt,name=requires_certificate,json=requiresCertificate,proto3" json:"requires_certificate,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *CreateSportTypeRequest) Reset() {
	*x = CreateSportTypeRequest{}
	mi := &file_proto_sport_type_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSportTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSportTypeRequest) ProtoMessage() {}

func (x *CreateSportTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sport_type_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSportTypeRequest.ProtoReflect.Descriptor instead.
func (*CreateSportTypeRequest) Descriptor() ([]byte, []int) {
	return file_proto_sport_type_proto_rawDescGZIP(), []int{0}
}

func (x *CreateSportTypeRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CreateSportTypeRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateSportTypeRequest) GetCategory() SportTypeCategory {
	if x != nil {
		return x.Category
	}
	return SportTypeCategory_NORMAL
}

func (x *CreateSportTypeRequest) GetRequiresCertificate() bool {
	if x != nil {
		return x.RequiresCertificate
	}
	return false
}

// Get sport type request
type GetSportTypeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSportTypeRequest) Reset() {
	*x = GetSportTypeRequest{}
	mi := &file_proto_sport_type_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSportTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSportTypeRequest) ProtoMessage() {}

func (x *GetSportTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sport_type_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSportTypeRequest.ProtoReflect.Descriptor instead.
func (*GetSportTypeRequest) Descriptor() ([]byte, []int) {
	return file_proto_sport_type_proto_rawDescGZIP(), []int{1}
}

func (x *GetSportTypeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Update sport type request
type UpdateSportTypeRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Id                  int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title               string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description         string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Category            SportTypeCategory      `protobuf:"varint,4,opt,name=category,proto3,enum=sportpb.SportTypeCategory" json:"category,omitempty"`
	RequiresCertificate bool                   `protobuf:"varint,5,opt,name=requires_certificate,json=requiresCertificate,proto3" json:"requires_certificate,omitempty"`
	Version             int32                  `protobuf:"varint,6,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *UpdateSportTypeRequest) Reset() {
	*x = UpdateSportTypeRequest{}
	mi := &file_proto_sport_type_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSportTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSportTypeRequest) ProtoMessage() {}

func (x *UpdateSportTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sport_type_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSportTypeRequest.ProtoReflect.Descriptor instead.
func (*UpdateSportTypeRequest) Descriptor() ([]byte, []int) {
	return file_proto_sport_type_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateSportTypeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateSportTypeRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdateSportTypeRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateSportTypeRequest) GetCategory() SportTypeCategory {
	if x != nil {
		return x.Category
	}
	return SportTypeCategory_NORMAL
}

func (x *UpdateSportTypeRequest) GetRequiresCertificate() bool {
	if x != nil {
		return x.RequiresCertificate
	}
	return false
}

func (x *UpdateSportTypeRequest) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

// Delete sport type request
type DeleteSportTypeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSportTypeRequest) Reset() {
	*x = DeleteSportTypeRequest{}
	mi := &file_proto_sport_type_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSportTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSportTypeRequest) ProtoMessage() {}

func (x *DeleteSportTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sport_type_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSportTypeRequest.ProtoReflect.Descriptor instead.
func (*DeleteSportTypeRequest) Descriptor() ([]byte, []int) {
	return file_proto_sport_type_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteSportTypeRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// List sport types request
type ListSportTypesRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Title               string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Category            SportTypeCategory      `protobuf:"varint,2,opt,name=category,proto3,enum=sportpb.SportTypeCategory" json:"category,omitempty"`
	RequiresCertificate bool                   `protobuf:"varint,3,opt,name=requires_certificate,json=requiresCertificate,proto3" json:"requires_certificate,omitempty"`
	Page                int32                  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize            int32                  `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *ListSportTypesRequest) Reset() {
	*x = ListSportTypesRequest{}
	mi := &file_proto_sport_type_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSportTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSportTypesRequest) ProtoMessage() {}

func (x *ListSportTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sport_type_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSportTypesRequest.ProtoReflect.Descriptor instead.
func (*ListSportTypesRequest) Descriptor() ([]byte, []int) {
	return file_proto_sport_type_proto_rawDescGZIP(), []int{4}
}

func (x *ListSportTypesRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ListSportTypesRequest) GetCategory() SportTypeCategory {
	if x != nil {
		return x.Category
	}
	return SportTypeCategory_NORMAL
}

func (x *ListSportTypesRequest) GetRequiresCertificate() bool {
	if x != nil {
		return x.RequiresCertificate
	}
	return false
}

func (x *ListSportTypesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSportTypesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Sport type response
type SportTypeResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Id                  int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title               string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description         string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Category            SportTypeCategory      `protobuf:"varint,4,opt,name=category,proto3,enum=sportpb.SportTypeCategory" json:"category,omitempty"`
	RequiresCertificate bool                   `protobuf:"varint,5,opt,name=requires_certificate,json=requiresCertificate,proto3" json:"requires_certificate,omitempty"`
	CreatedAt           *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt           *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Version             int32                  `protobuf:"varint,8,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *SportTypeResponse) Reset() {
	*x = SportTypeResponse{}
	mi := &file_proto_sport_type_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SportTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SportTypeResponse) ProtoMessage() {}

func (x *SportTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sport_type_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SportTypeResponse.ProtoReflect.Descriptor instead.
func (*SportTypeResponse) Descriptor() ([]byte, []int) {
	return file_proto_sport_type_proto_rawDescGZIP(), []int{5}
}

func (x *SportTypeResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SportTypeResponse) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *SportTypeResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SportTypeResponse) GetCategory() SportTypeCategory {
	if x != nil {
		return x.Category
	}
	return SportTypeCategory_NORMAL
}

func (x *SportTypeResponse) GetRequiresCertificate() bool {
	if x != nil {
		return x.RequiresCertificate
	}
	return false
}

func (x *SportTypeResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SportTypeResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *SportTypeResponse) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

// List sport types response
type ListSportTypesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SportTypes    []*SportTypeResponse   `protobuf:"bytes,1,rep,name=sport_types,json=sportTypes,proto3" json:"sport_types,omitempty"`
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSportTypesResponse) Reset() {
	*x = ListSportTypesResponse{}
	mi := &file_proto_sport_type_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSportTypesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSportTypesResponse) ProtoMessage() {}

func (x *ListSportTypesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sport_type_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSportTypesResponse.ProtoReflect.Descriptor instead.
func (*ListSportTypesResponse) Descriptor() ([]byte, []int) {
	return file_proto_sport_type_proto_rawDescGZIP(), []int{6}
}

func (x *ListSportTypesResponse) GetSportTypes() []*SportTypeResponse {
	if x != nil {
		return x.SportTypes
	}
	return nil
}

func (x *ListSportTypesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListSportTypesResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSportTypesResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Assign teacher request
type AssignTeacherRequest struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	TeacherId             int64                  `protobuf:"varint,1,opt,name=teacher_id,json=teacherId,proto3" json:"teacher_id,omitempty"`
	SportTypeId           int64                  `protobuf:"varint,2,opt,name=sport_type_id,json=sportTypeId,proto3" json:"sport_type_id,omitempty"`
	CanReviewCertificates bool                   `protobuf:"varint,3,opt,name=can_review_certificates,json=canReviewCertificates,proto3" json:"can_review_certificates,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *AssignTeacherRequest) Reset() {
	*x = AssignTeacherRequest{}
	mi := &file_proto_sport_type_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignTeacherRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignTeacherRequest) ProtoMessage() {}

func (x *AssignTeacherRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sport_type_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignTeacherRequest.ProtoReflect.Descriptor instead.
func (*AssignTeacherRequest) Descriptor() ([]byte, []int) {
	return file_proto_sport_type_proto_rawDescGZIP(), []int{7}
}

func (x *AssignTeacherRequest) GetTeacherId() int64 {
	if x != nil {
		return x.TeacherId
	}
	return 0
}

func (x *AssignTeacherRequest) GetSportTypeId() int64 {
	if x != nil {
		return x.SportTypeId
	}
	return 0
}

func (x *AssignTeacherRequest) GetCanReviewCertificates() bool {
	if x != nil {
		return x.CanReviewCertificates
	}
	return false
}

// Remove teacher request
type RemoveTeacherRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TeacherId     int64                  `protobuf:"varint,1,opt,name=teacher_id,json=teacherId,proto3" json:"teacher_id,omitempty"`
	SportTypeId   int64                  `protobuf:"varint,2,opt,name=sport_type_id,json=sportTypeId,proto3" json:"sport_type_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveTeacherRequest) Reset() {
	*x = RemoveTeacherRequest{}
	mi := &file_proto_sport_type_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveTeacherRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveTeacherRequest) ProtoMessage() {}

func (x *RemoveTeacherRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sport_type_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveTeacherRequest.ProtoReflect.Descriptor instead.
func (*RemoveTeacherRequest) Descriptor() ([]byte, []int) {
	return file_proto_sport_type_proto_rawDescGZIP(), []int{8}
}

func (x *RemoveTeacherRequest) GetTeacherId() int64 {
	if x != nil {
		return x.TeacherId
	}
	return 0
}

func (x *RemoveTeacherRequest) GetSportTypeId() int64 {
	if x != nil {
		return x.SportTypeId
	}
	return 0
}

// List teachers for sport type request
type ListTeachersForSportTypeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SportTypeId   int64                  `protobuf:"varint,1,opt,name=sport_type_id,json=sportTypeId,proto3" json:"sport_type_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTeachersForSportTypeRequest) Reset() {
	*x = ListTeachersForSportTypeRequest{}
	mi := &file_proto_sport_type_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTeachersForSportTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTeachersForSportTypeRequest) ProtoMessage() {}

func (x *ListTeachersForSportTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sport_type_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTeachersForSportTypeRequest.ProtoReflect.Descriptor instead.
func (*ListTeachersForSportTypeRequest) Descriptor() ([]byte, []int) {
	return file_proto_sport_type_proto_rawDescGZIP(), []int{9}
}

func (x *ListTeachersForSportTypeRequest) GetSportTypeId() int64 {
	if x != nil {
		return x.SportTypeId
	}
	return 0
}

// List sport types for teacher request
type ListSportTypesForTeacherRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TeacherId     int64                  `protobuf:"varint,1,opt,name=teacher_id,json=teacherId,proto3" json:"teacher_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSportTypesForTeacherRequest) Reset() {
	*x = ListSportTypesForTeacherRequest{}
	mi := &file_proto_sport_type_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSportTypesForTeacherRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSportTypesForTeacherRequest) ProtoMessage() {}

func (x *ListSportTypesForTeacherRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sport_type_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSportTypesForTeacherRequest.ProtoReflect.Descriptor instead.
func (*ListSportTypesForTeacherRequest) Descriptor() ([]byte, []int) {
	return file_proto_sport_type_proto_rawDescGZIP(), []int{10}
}

func (x *ListSportTypesForTeacherRequest) GetTeacherId() int64 {
	if x != nil {
		return x.TeacherId
	}
	return 0
}

// Teacher response
type TeacherResponse struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Id                    int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                  string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	CanReviewCertificates bool                   `protobuf:"varint,3,opt,name=can_review_certificates,json=canReviewCertificates,proto3" json:"can_review_certificates,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *TeacherResponse) Reset() {
	*x = TeacherResponse{}
	mi := &file_proto_sport_type_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TeacherResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeacherResponse) ProtoMessage() {}

func (x *TeacherResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sport_type_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeacherResponse.ProtoReflect.Descriptor instead.
func (*TeacherResponse) Descriptor() ([]byte, []int) {
	return file_proto_sport_type_proto_rawDescGZIP(), []int{11}
}

func (x *TeacherResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TeacherResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TeacherResponse) GetCanReviewCertificates() bool {
	if x != nil {
		return x.CanReviewCertificates
	}
	return false
}

// List teachers response
type ListTeachersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Teachers      []*TeacherResponse     `protobuf:"bytes,1,rep,name=teachers,proto3" json:"teachers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTeachersResponse) Reset() {
	*x = ListTeachersResponse{}
	mi := &file_proto_sport_type_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTeachersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTeachersResponse) ProtoMessage() {}

func (x *ListTeachersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sport_type_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTeachersResponse.ProtoReflect.Descriptor instead.
func (*ListTeachersResponse) Descriptor() ([]byte, []int) {
	return file_proto_sport_type_proto_rawDescGZIP(), []int{12}
}

func (x *ListTeachersResponse) GetTeachers() []*TeacherResponse {
	if x != nil {
		return x.Teachers
	}
	return nil
}

var File_proto_sport_type_proto protoreflect.FileDescriptor

const file_proto_sport_type_proto_rawDesc = "" +
	"\n" +
	"\x16proto/sport_type.proto\x12\asportpb\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\"\xbb\x01\n" +
	"\x16CreateSportTypeRequest\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x126\n" +
	"\bcategory\x18\x03 \x01(\x0e2\x1a.sportpb.SportTypeCategoryR\bcategory\x121\n" +
	"\x14requires_certificate\x18\x04 \x01(\bR\x13requiresCertificate\"%\n" +
	"\x13GetSportTypeRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\xe5\x01\n" +
	"\x16UpdateSportTypeRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x126\n" +
	"\bcategory\x18\x04 \x01(\x0e2\x1a.sportpb.SportTypeCategoryR\bcategory\x121\n" +
	"\x14requires_certificate\x18\x05 \x01(\bR\x13requiresCertificate\x12\x18\n" +
	"\aversion\x18\x06 \x01(\x05R\aversion\"(\n" +
	"\x16DeleteSportTypeRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\xc9\x01\n" +
	"\x15ListSportTypesRequest\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x126\n" +
	"\bcategory\x18\x02 \x01(\x0e2\x1a.sportpb.SportTypeCategoryR\bcategory\x121\n" +
	"\x14requires_certificate\x18\x03 \x01(\bR\x13requiresCertificate\x12\x12\n" +
	"\x04page\x18\x04 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x05 \x01(\x05R\bpageSize\"\xd6\x02\n" +
	"\x11SportTypeResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x126\n" +
	"\bcategory\x18\x04 \x01(\x0e2\x1a.sportpb.SportTypeCategoryR\bcategory\x121\n" +
	"\x14requires_certificate\x18\x05 \x01(\bR\x13requiresCertificate\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12\x18\n" +
	"\aversion\x18\b \x01(\x05R\aversion\"\x9c\x01\n" +
	"\x16ListSportTypesResponse\x12;\n" +
	"\vsport_types\x18\x01 \x03(\v2\x1a.sportpb.SportTypeResponseR\n" +
	"sportTypes\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"\x91\x01\n" +
	"\x14AssignTeacherRequest\x12\x1d\n" +
	"\n" +
	"teacher_id\x18\x01 \x01(\x03R\tteacherId\x12\"\n" +
	"\rsport_type_id\x18\x02 \x01(\x03R\vsportTypeId\x126\n" +
	"\x17can_review_certificates\x18\x03 \x01(\bR\x15canReviewCertificates\"Y\n" +
	"\x14RemoveTeacherRequest\x12\x1d\n" +
	"\n" +
	"teacher_id\x18\x01 \x01(\x03R\tteacherId\x12\"\n" +
	"\rsport_type_id\x18\x02 \x01(\x03R\vsportTypeId\"E\n" +
	"\x1fListTeachersForSportTypeRequest\x12\"\n" +
	"\rsport_type_id\x18\x01 \x01(\x03R\vsportTypeId\"@\n" +
	"\x1fListSportTypesForTeacherRequest\x12\x1d\n" +
	"\n" +
	"teacher_id\x18\x01 \x01(\x03R\tteacherId\"m\n" +
	"\x0fTeacherResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x126\n" +
	"\x17can_review_certificates\x18\x03 \x01(\bR\x15canReviewCertificates\"L\n" +
	"\x14ListTeachersResponse\x124\n" +
	"\bteachers\x18\x01 \x03(\v2\x18.sportpb.TeacherResponseR\bteachers*(\n" +
	"\x11SportTypeCategory\x12\n" +
	"\n" +
	"\x06NORMAL\x10\x00\x12\a\n" +
	"\x03LFK\x10\x012\xa1\x06\n" +
	"\x10SportTypeService\x12P\n" +
	"\x0fCreateSportType\x12\x1f.sportpb.CreateSportTypeRequest\x1a\x1a.sportpb.SportTypeResponse\"\x00\x12J\n" +
	"\fGetSportType\x12\x1c.sportpb.GetSportTypeRequest\x1a\x1a.sportpb.SportTypeResponse\"\x00\x12P\n" +
	"\x0fUpdateSportType\x12\x1f.sportpb.UpdateSportTypeRequest\x1a\x1a.sportpb.SportTypeResponse\"\x00\x12L\n" +
	"\x0fDeleteSportType\x12\x1f.sportpb.DeleteSportTypeRequest\x1a\x16.google.protobuf.Empty\"\x00\x12S\n" +
	"\x0eListSportTypes\x12\x1e.sportpb.ListSportTypesRequest\x1a\x1f.sportpb.ListSportTypesResponse\"\x00\x12S\n" +
	"\x18AssignTeacherToSportType\x12\x1d.sportpb.AssignTeacherRequest\x1a\x16.google.protobuf.Empty\"\x00\x12U\n" +
	"\x1aRemoveTeacherFromSportType\x12\x1d.sportpb.RemoveTeacherRequest\x1a\x16.google.protobuf.Empty\"\x00\x12e\n" +
	"\x18ListTeachersForSportType\x12(.sportpb.ListTeachersForSportTypeRequest\x1a\x1d.sportpb.ListTeachersResponse\"\x00\x12g\n" +
	"\x18ListSportTypesForTeacher\x12(.sportpb.ListSportTypesForTeacherRequest\x1a\x1f.sportpb.ListSportTypesResponse\"\x00B<Z:github.com/olzzhas/edunite-server/sport_service/pb/sportpbb\x06proto3"

var (
	file_proto_sport_type_proto_rawDescOnce sync.Once
	file_proto_sport_type_proto_rawDescData []byte
)

func file_proto_sport_type_proto_rawDescGZIP() []byte {
	file_proto_sport_type_proto_rawDescOnce.Do(func() {
		file_proto_sport_type_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_sport_type_proto_rawDesc), len(file_proto_sport_type_proto_rawDesc)))
	})
	return file_proto_sport_type_proto_rawDescData
}

var file_proto_sport_type_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_sport_type_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_proto_sport_type_proto_goTypes = []any{
	(SportTypeCategory)(0),                  // 0: sportpb.SportTypeCategory
	(*CreateSportTypeRequest)(nil),          // 1: sportpb.CreateSportTypeRequest
	(*GetSportTypeRequest)(nil),             // 2: sportpb.GetSportTypeRequest
	(*UpdateSportTypeRequest)(nil),          // 3: sportpb.UpdateSportTypeRequest
	(*DeleteSportTypeRequest)(nil),          // 4: sportpb.DeleteSportTypeRequest
	(*ListSportTypesRequest)(nil),           // 5: sportpb.ListSportTypesRequest
	(*SportTypeResponse)(nil),               // 6: sportpb.SportTypeResponse
	(*ListSportTypesResponse)(nil),          // 7: sportpb.ListSportTypesResponse
	(*AssignTeacherRequest)(nil),            // 8: sportpb.AssignTeacherRequest
	(*RemoveTeacherRequest)(nil),            // 9: sportpb.RemoveTeacherRequest
	(*ListTeachersForSportTypeRequest)(nil), // 10: sportpb.ListTeachersForSportTypeRequest
	(*ListSportTypesForTeacherRequest)(nil), // 11: sportpb.ListSportTypesForTeacherRequest
	(*TeacherResponse)(nil),                 // 12: sportpb.TeacherResponse
	(*ListTeachersResponse)(nil),            // 13: sportpb.ListTeachersResponse
	(*timestamppb.Timestamp)(nil),           // 14: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                   // 15: google.protobuf.Empty
}
var file_proto_sport_type_proto_depIdxs = []int32{
	0,  // 0: sportpb.CreateSportTypeRequest.category:type_name -> sportpb.SportTypeCategory
	0,  // 1: sportpb.UpdateSportTypeRequest.category:type_name -> sportpb.SportTypeCategory
	0,  // 2: sportpb.ListSportTypesRequest.category:type_name -> sportpb.SportTypeCategory
	0,  // 3: sportpb.SportTypeResponse.category:type_name -> sportpb.SportTypeCategory
	14, // 4: sportpb.SportTypeResponse.created_at:type_name -> google.protobuf.Timestamp
	14, // 5: sportpb.SportTypeResponse.updated_at:type_name -> google.protobuf.Timestamp
	6,  // 6: sportpb.ListSportTypesResponse.sport_types:type_name -> sportpb.SportTypeResponse
	12, // 7: sportpb.ListTeachersResponse.teachers:type_name -> sportpb.TeacherResponse
	1,  // 8: sportpb.SportTypeService.CreateSportType:input_type -> sportpb.CreateSportTypeRequest
	2,  // 9: sportpb.SportTypeService.GetSportType:input_type -> sportpb.GetSportTypeRequest
	3,  // 10: sportpb.SportTypeService.UpdateSportType:input_type -> sportpb.UpdateSportTypeRequest
	4,  // 11: sportpb.SportTypeService.DeleteSportType:input_type -> sportpb.DeleteSportTypeRequest
	5,  // 12: sportpb.SportTypeService.ListSportTypes:input_type -> sportpb.ListSportTypesRequest
	8,  // 13: sportpb.SportTypeService.AssignTeacherToSportType:input_type -> sportpb.AssignTeacherRequest
	9,  // 14: sportpb.SportTypeService.RemoveTeacherFromSportType:input_type -> sportpb.RemoveTeacherRequest
	10, // 15: sportpb.SportTypeService.ListTeachersForSportType:input_type -> sportpb.ListTeachersForSportTypeRequest
	11, // 16: sportpb.SportTypeService.ListSportTypesForTeacher:input_type -> sportpb.ListSportTypesForTeacherRequest
	6,  // 17: sportpb.SportTypeService.CreateSportType:output_type -> sportpb.SportTypeResponse
	6,  // 18: sportpb.SportTypeService.GetSportType:output_type -> sportpb.SportTypeResponse
	6,  // 19: sportpb.SportTypeService.UpdateSportType:output_type -> sportpb.SportTypeResponse
	15, // 20: sportpb.SportTypeService.DeleteSportType:output_type -> google.protobuf.Empty
	7,  // 21: sportpb.SportTypeService.ListSportTypes:output_type -> sportpb.ListSportTypesResponse
	15, // 22: sportpb.SportTypeService.AssignTeacherToSportType:output_type -> google.protobuf.Empty
	15, // 23: sportpb.SportTypeService.RemoveTeacherFromSportType:output_type -> google.protobuf.Empty
	13, // 24: sportpb.SportTypeService.ListTeachersForSportType:output_type -> sportpb.ListTeachersResponse
	7,  // 25: sportpb.SportTypeService.ListSportTypesForTeacher:output_type -> sportpb.ListSportTypesResponse
	17, // [17:26] is the sub-list for method output_type
	8,  // [8:17] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_proto_sport_type_proto_init() }
func file_proto_sport_type_proto_init() {
	if File_proto_sport_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_sport_type_proto_rawDesc), len(file_proto_sport_type_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_sport_type_proto_goTypes,
		DependencyIndexes: file_proto_sport_type_proto_depIdxs,
		EnumInfos:         file_proto_sport_type_proto_enumTypes,
		MessageInfos:      file_proto_sport_type_proto_msgTypes,
	}.Build()
	File_proto_sport_type_proto = out.File
	file_proto_sport_type_proto_goTypes = nil
	file_proto_sport_type_proto_depIdxs = nil
}
