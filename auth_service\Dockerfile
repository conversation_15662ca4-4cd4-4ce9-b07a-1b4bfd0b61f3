FROM golang:1.23-alpine AS builder

WORKDIR /app

# Copy go.mod and go.sum files
COPY go.mod ./
COPY go.sum ./

# Download dependencies
RUN go mod download

# Copy the source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o /auth_service ./cmd/main.go

# Create a minimal image
FROM alpine:latest

WORKDIR /

# Copy the binary from the builder stage
COPY --from=builder /auth_service /auth_service

# Expose the port
EXPOSE 50052

# Run the application
CMD ["/auth_service"]
