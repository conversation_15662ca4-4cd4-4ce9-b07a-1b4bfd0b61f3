package repository

import (
	"context"
	"time"

	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

// BookingRepository defines the interface for booking data access
type BookingRepository interface {
	// Create creates a new booking
	Create(ctx context.Context, booking *domain.Booking) error

	// GetByID retrieves a booking by ID
	GetByID(ctx context.Context, id int64) (*domain.Booking, error)

	// Update updates an existing booking
	Update(ctx context.Context, booking *domain.Booking) error

	// Delete deletes a booking by ID
	Delete(ctx context.Context, id int64) error

	// List retrieves bookings based on filters
	List(ctx context.Context, filter domain.BookingFilter) ([]*domain.Booking, error)

	// Count counts bookings based on filters
	Count(ctx context.Context, filter domain.BookingFilter) (int, error)

	// GetByUserAndSchedule retrieves a booking by user ID and schedule ID
	GetByUserAndSchedule(ctx context.Context, userID, scheduleID int64) (*domain.Booking, error)

	// CountBySchedule counts bookings for a schedule
	CountBySchedule(ctx context.Context, scheduleID int64) (int, error)

	// GetUserBookingsForDate retrieves a user's bookings for a specific date
	GetUserBookingsForDate(ctx context.Context, userID int64, date time.Time) ([]*domain.Booking, error)

	// CountUserBookingsInSemester counts a user's bookings in a semester (sportTypeID=0 means all sport types)
	CountUserBookingsInSemester(ctx context.Context, userID, semesterID, sportTypeID int64) (int, error)

	// GetStats retrieves booking statistics
	GetStats(ctx context.Context, filter domain.BookingFilter) (*domain.BookingStats, error)

	// CancelBooking cancels a booking
	CancelBooking(ctx context.Context, id int64) error
}
