package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

// SetupAdminRoutes sets up routes for admin functionality
func SetupAdminRoutes(r *gin.Engine, authClient *clients.AuthClient, userClient *clients.UserClient, adminHandler *handlers.AdminHandler) {
	// Create a group for admin routes that requires authentication and admin role
	adminGroup := r.Group("/admin")
	adminGroup.Use(AuthMiddleware(authClient))
	adminGroup.Use(RoleMiddleware(authClient, "admin"))
	{
		// Admin-only routes
		adminGroup.GET("/users", adminHandler.GetAllUsersHandler)
		adminGroup.PUT("/users/:id", adminHandler.UpdateUserHandler)
		adminGroup.DELETE("/users/:id", adminHandler.DeleteUserHandler)
	}
}

// SetupTeacherRoutes sets up routes for teacher functionality
func SetupTeacherRoutes(r *gin.Engine, authClient *clients.AuthClient, userClient *clients.UserClient, teacherHandler *handlers.TeacherHandler) {
	// Create a group for teacher routes that requires authentication and teacher role
	teacherGroup := r.Group("/teacher")
	teacherGroup.Use(AuthMiddleware(authClient))
	teacherGroup.Use(RoleMiddleware(authClient, "teacher"))
	{
		// Teacher-only routes
		teacherGroup.GET("/courses", teacherHandler.GetCoursesHandler)
		teacherGroup.POST("/courses", teacherHandler.CreateCourseHandler)
		teacherGroup.PUT("/courses/:id", teacherHandler.UpdateCourseHandler)

		// Assignment routes
		teacherGroup.GET("/assignments/:id", teacherHandler.GetAssignmentDetailsForTeacher)
		teacherGroup.POST("/assignments/:id/grade", teacherHandler.GradeAssignmentHandler)
		teacherGroup.GET("/threads/:id/submissions", teacherHandler.GetThreadSubmissionsHandler)

		// Thread routes for teachers
		teacherGroup.GET("/my-threads", teacherHandler.ListThreadsForTeacherHandler)
	}
}

// SetupModeratorRoutes sets up routes for moderator functionality
func SetupModeratorRoutes(r *gin.Engine, authClient *clients.AuthClient, userClient *clients.UserClient, moderatorHandler *handlers.ModeratorHandler) {
	// Create a group for moderator routes that requires authentication and moderator role
	moderatorGroup := r.Group("/moderator")
	moderatorGroup.Use(AuthMiddleware(authClient))
	moderatorGroup.Use(RoleMiddleware(authClient, "moderator"))
	{
		// Moderator-only routes
		moderatorGroup.GET("/reports", moderatorHandler.GetReportsHandler)
		moderatorGroup.PUT("/reports/:id", moderatorHandler.UpdateReportHandler)
	}
}
