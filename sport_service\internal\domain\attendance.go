package domain

import (
	"time"
)

// AttendanceStatus represents the status of attendance
type AttendanceStatus string

const (
	AttendanceStatusPresent AttendanceStatus = "present"
	AttendanceStatusAbsent  AttendanceStatus = "absent"
	AttendanceStatusExcused AttendanceStatus = "excused"
)

// Attendance represents a record of whether a user attended a booked session
type Attendance struct {
	BookingID  int64            `json:"booking_id"`
	UserID     int64            `json:"user_id"`
	ScheduleID int64            `json:"schedule_id"`
	Status     AttendanceStatus `json:"status"`
	Notes      string           `json:"notes"`
	MarkedBy   int64            `json:"marked_by"` // ID of the user who marked attendance
	CreatedAt  time.Time        `json:"created_at"`
	UpdatedAt  time.Time        `json:"updated_at"`
}

// AttendanceFilter represents filters for querying attendance records
type AttendanceFilter struct {
	UserID     int64
	ScheduleID int64
	FacilityID int64
	Status     AttendanceStatus
	StartDate  time.Time
	EndDate    time.Time
	Page       int
	PageSize   int
}

// AttendanceStats represents statistics about attendance
type AttendanceStats struct {
	TotalSessions    int     `json:"total_sessions"`
	PresentSessions  int     `json:"present_sessions"`
	AbsentSessions   int     `json:"absent_sessions"`
	ExcusedSessions  int     `json:"excused_sessions"`
	AttendanceRate   float64 `json:"attendance_rate"` // Percentage of present + excused sessions
}

// Error definitions for attendance operations
var (
	ErrAttendanceNotFound      = Error{"attendance record not found"}
	ErrAttendanceAlreadyMarked = Error{"attendance already marked for this booking"}
)
