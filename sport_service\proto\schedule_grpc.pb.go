// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: proto/schedule.proto

package sportpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ScheduleService_CreateSchedule_FullMethodName          = "/sportpb.ScheduleService/CreateSchedule"
	ScheduleService_GetSchedule_FullMethodName             = "/sportpb.ScheduleService/GetSchedule"
	ScheduleService_UpdateSchedule_FullMethodName          = "/sportpb.ScheduleService/UpdateSchedule"
	ScheduleService_DeleteSchedule_FullMethodName          = "/sportpb.ScheduleService/DeleteSchedule"
	ScheduleService_ListSchedules_FullMethodName           = "/sportpb.ScheduleService/ListSchedules"
	ScheduleService_CreateWeeklySchedules_FullMethodName   = "/sportpb.ScheduleService/CreateWeeklySchedules"
	ScheduleService_GetSchedulesForTeacher_FullMethodName  = "/sportpb.ScheduleService/GetSchedulesForTeacher"
	ScheduleService_GetSchedulesForFacility_FullMethodName = "/sportpb.ScheduleService/GetSchedulesForFacility"
	ScheduleService_GetSchedulesForSemester_FullMethodName = "/sportpb.ScheduleService/GetSchedulesForSemester"
)

// ScheduleServiceClient is the client API for ScheduleService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ScheduleServiceClient interface {
	// Create a new schedule
	CreateSchedule(ctx context.Context, in *CreateScheduleRequest, opts ...grpc.CallOption) (*ScheduleResponse, error)
	// Get a schedule by ID
	GetSchedule(ctx context.Context, in *GetScheduleRequest, opts ...grpc.CallOption) (*ScheduleResponse, error)
	// Update an existing schedule
	UpdateSchedule(ctx context.Context, in *UpdateScheduleRequest, opts ...grpc.CallOption) (*ScheduleResponse, error)
	// Delete a schedule
	DeleteSchedule(ctx context.Context, in *DeleteScheduleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// List schedules with filtering
	ListSchedules(ctx context.Context, in *ListSchedulesRequest, opts ...grpc.CallOption) (*ListSchedulesResponse, error)
	// Create schedules from a weekly template
	CreateWeeklySchedules(ctx context.Context, in *CreateWeeklySchedulesRequest, opts ...grpc.CallOption) (*CreateWeeklySchedulesResponse, error)
	// Get schedules for a teacher
	GetSchedulesForTeacher(ctx context.Context, in *GetSchedulesForTeacherRequest, opts ...grpc.CallOption) (*ListSchedulesResponse, error)
	// Get schedules for a facility
	GetSchedulesForFacility(ctx context.Context, in *GetSchedulesForFacilityRequest, opts ...grpc.CallOption) (*ListSchedulesResponse, error)
	// Get schedules for a semester
	GetSchedulesForSemester(ctx context.Context, in *GetSchedulesForSemesterRequest, opts ...grpc.CallOption) (*ListSchedulesResponse, error)
}

type scheduleServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewScheduleServiceClient(cc grpc.ClientConnInterface) ScheduleServiceClient {
	return &scheduleServiceClient{cc}
}

func (c *scheduleServiceClient) CreateSchedule(ctx context.Context, in *CreateScheduleRequest, opts ...grpc.CallOption) (*ScheduleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ScheduleResponse)
	err := c.cc.Invoke(ctx, ScheduleService_CreateSchedule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleServiceClient) GetSchedule(ctx context.Context, in *GetScheduleRequest, opts ...grpc.CallOption) (*ScheduleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ScheduleResponse)
	err := c.cc.Invoke(ctx, ScheduleService_GetSchedule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleServiceClient) UpdateSchedule(ctx context.Context, in *UpdateScheduleRequest, opts ...grpc.CallOption) (*ScheduleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ScheduleResponse)
	err := c.cc.Invoke(ctx, ScheduleService_UpdateSchedule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleServiceClient) DeleteSchedule(ctx context.Context, in *DeleteScheduleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ScheduleService_DeleteSchedule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleServiceClient) ListSchedules(ctx context.Context, in *ListSchedulesRequest, opts ...grpc.CallOption) (*ListSchedulesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSchedulesResponse)
	err := c.cc.Invoke(ctx, ScheduleService_ListSchedules_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleServiceClient) CreateWeeklySchedules(ctx context.Context, in *CreateWeeklySchedulesRequest, opts ...grpc.CallOption) (*CreateWeeklySchedulesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateWeeklySchedulesResponse)
	err := c.cc.Invoke(ctx, ScheduleService_CreateWeeklySchedules_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleServiceClient) GetSchedulesForTeacher(ctx context.Context, in *GetSchedulesForTeacherRequest, opts ...grpc.CallOption) (*ListSchedulesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSchedulesResponse)
	err := c.cc.Invoke(ctx, ScheduleService_GetSchedulesForTeacher_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleServiceClient) GetSchedulesForFacility(ctx context.Context, in *GetSchedulesForFacilityRequest, opts ...grpc.CallOption) (*ListSchedulesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSchedulesResponse)
	err := c.cc.Invoke(ctx, ScheduleService_GetSchedulesForFacility_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleServiceClient) GetSchedulesForSemester(ctx context.Context, in *GetSchedulesForSemesterRequest, opts ...grpc.CallOption) (*ListSchedulesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSchedulesResponse)
	err := c.cc.Invoke(ctx, ScheduleService_GetSchedulesForSemester_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ScheduleServiceServer is the server API for ScheduleService service.
// All implementations must embed UnimplementedScheduleServiceServer
// for forward compatibility.
type ScheduleServiceServer interface {
	// Create a new schedule
	CreateSchedule(context.Context, *CreateScheduleRequest) (*ScheduleResponse, error)
	// Get a schedule by ID
	GetSchedule(context.Context, *GetScheduleRequest) (*ScheduleResponse, error)
	// Update an existing schedule
	UpdateSchedule(context.Context, *UpdateScheduleRequest) (*ScheduleResponse, error)
	// Delete a schedule
	DeleteSchedule(context.Context, *DeleteScheduleRequest) (*emptypb.Empty, error)
	// List schedules with filtering
	ListSchedules(context.Context, *ListSchedulesRequest) (*ListSchedulesResponse, error)
	// Create schedules from a weekly template
	CreateWeeklySchedules(context.Context, *CreateWeeklySchedulesRequest) (*CreateWeeklySchedulesResponse, error)
	// Get schedules for a teacher
	GetSchedulesForTeacher(context.Context, *GetSchedulesForTeacherRequest) (*ListSchedulesResponse, error)
	// Get schedules for a facility
	GetSchedulesForFacility(context.Context, *GetSchedulesForFacilityRequest) (*ListSchedulesResponse, error)
	// Get schedules for a semester
	GetSchedulesForSemester(context.Context, *GetSchedulesForSemesterRequest) (*ListSchedulesResponse, error)
	mustEmbedUnimplementedScheduleServiceServer()
}

// UnimplementedScheduleServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedScheduleServiceServer struct{}

func (UnimplementedScheduleServiceServer) CreateSchedule(context.Context, *CreateScheduleRequest) (*ScheduleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSchedule not implemented")
}
func (UnimplementedScheduleServiceServer) GetSchedule(context.Context, *GetScheduleRequest) (*ScheduleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSchedule not implemented")
}
func (UnimplementedScheduleServiceServer) UpdateSchedule(context.Context, *UpdateScheduleRequest) (*ScheduleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSchedule not implemented")
}
func (UnimplementedScheduleServiceServer) DeleteSchedule(context.Context, *DeleteScheduleRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSchedule not implemented")
}
func (UnimplementedScheduleServiceServer) ListSchedules(context.Context, *ListSchedulesRequest) (*ListSchedulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSchedules not implemented")
}
func (UnimplementedScheduleServiceServer) CreateWeeklySchedules(context.Context, *CreateWeeklySchedulesRequest) (*CreateWeeklySchedulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWeeklySchedules not implemented")
}
func (UnimplementedScheduleServiceServer) GetSchedulesForTeacher(context.Context, *GetSchedulesForTeacherRequest) (*ListSchedulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSchedulesForTeacher not implemented")
}
func (UnimplementedScheduleServiceServer) GetSchedulesForFacility(context.Context, *GetSchedulesForFacilityRequest) (*ListSchedulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSchedulesForFacility not implemented")
}
func (UnimplementedScheduleServiceServer) GetSchedulesForSemester(context.Context, *GetSchedulesForSemesterRequest) (*ListSchedulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSchedulesForSemester not implemented")
}
func (UnimplementedScheduleServiceServer) mustEmbedUnimplementedScheduleServiceServer() {}
func (UnimplementedScheduleServiceServer) testEmbeddedByValue()                         {}

// UnsafeScheduleServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ScheduleServiceServer will
// result in compilation errors.
type UnsafeScheduleServiceServer interface {
	mustEmbedUnimplementedScheduleServiceServer()
}

func RegisterScheduleServiceServer(s grpc.ServiceRegistrar, srv ScheduleServiceServer) {
	// If the following call pancis, it indicates UnimplementedScheduleServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ScheduleService_ServiceDesc, srv)
}

func _ScheduleService_CreateSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleServiceServer).CreateSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScheduleService_CreateSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleServiceServer).CreateSchedule(ctx, req.(*CreateScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleService_GetSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleServiceServer).GetSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScheduleService_GetSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleServiceServer).GetSchedule(ctx, req.(*GetScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleService_UpdateSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleServiceServer).UpdateSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScheduleService_UpdateSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleServiceServer).UpdateSchedule(ctx, req.(*UpdateScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleService_DeleteSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleServiceServer).DeleteSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScheduleService_DeleteSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleServiceServer).DeleteSchedule(ctx, req.(*DeleteScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleService_ListSchedules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSchedulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleServiceServer).ListSchedules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScheduleService_ListSchedules_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleServiceServer).ListSchedules(ctx, req.(*ListSchedulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleService_CreateWeeklySchedules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWeeklySchedulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleServiceServer).CreateWeeklySchedules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScheduleService_CreateWeeklySchedules_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleServiceServer).CreateWeeklySchedules(ctx, req.(*CreateWeeklySchedulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleService_GetSchedulesForTeacher_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSchedulesForTeacherRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleServiceServer).GetSchedulesForTeacher(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScheduleService_GetSchedulesForTeacher_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleServiceServer).GetSchedulesForTeacher(ctx, req.(*GetSchedulesForTeacherRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleService_GetSchedulesForFacility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSchedulesForFacilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleServiceServer).GetSchedulesForFacility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScheduleService_GetSchedulesForFacility_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleServiceServer).GetSchedulesForFacility(ctx, req.(*GetSchedulesForFacilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleService_GetSchedulesForSemester_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSchedulesForSemesterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleServiceServer).GetSchedulesForSemester(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ScheduleService_GetSchedulesForSemester_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleServiceServer).GetSchedulesForSemester(ctx, req.(*GetSchedulesForSemesterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ScheduleService_ServiceDesc is the grpc.ServiceDesc for ScheduleService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ScheduleService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sportpb.ScheduleService",
	HandlerType: (*ScheduleServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSchedule",
			Handler:    _ScheduleService_CreateSchedule_Handler,
		},
		{
			MethodName: "GetSchedule",
			Handler:    _ScheduleService_GetSchedule_Handler,
		},
		{
			MethodName: "UpdateSchedule",
			Handler:    _ScheduleService_UpdateSchedule_Handler,
		},
		{
			MethodName: "DeleteSchedule",
			Handler:    _ScheduleService_DeleteSchedule_Handler,
		},
		{
			MethodName: "ListSchedules",
			Handler:    _ScheduleService_ListSchedules_Handler,
		},
		{
			MethodName: "CreateWeeklySchedules",
			Handler:    _ScheduleService_CreateWeeklySchedules_Handler,
		},
		{
			MethodName: "GetSchedulesForTeacher",
			Handler:    _ScheduleService_GetSchedulesForTeacher_Handler,
		},
		{
			MethodName: "GetSchedulesForFacility",
			Handler:    _ScheduleService_GetSchedulesForFacility_Handler,
		},
		{
			MethodName: "GetSchedulesForSemester",
			Handler:    _ScheduleService_GetSchedulesForSemester_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/schedule.proto",
}
