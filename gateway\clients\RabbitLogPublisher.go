package clients

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/streadway/amqp"
)

// RabbitLogPublisher публикует логи в очередь "logs".
type RabbitLogPublisher struct {
	conn    *amqp.Connection
	channel *amqp.Channel
	queue   amqp.Queue
}

func NewRabbitLogPublisher(amqpURI string) (*RabbitLogPublisher, error) {
	conn, err := amqp.Dial(amqpURI)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to RabbitMQ: %w", err)
	}
	ch, err := conn.Channel()
	if err != nil {
		return nil, fmt.Errorf("failed to open channel: %w", err)
	}

	// Объявляем очередь "logs"
	q, err := ch.QueueDeclare(
		"logs", // name
		true,   // durable
		false,  // autoDelete
		false,  // exclusive
		false,  // noWait
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to declare queue logs: %w", err)
	}

	return &RabbitLogPublisher{
		conn:    conn,
		channel: ch,
		queue:   q,
	}, nil
}

// PublishLog отправляет лог-событие в RabbitMQ
func (p *RabbitLogPublisher) PublishLog(level, message, service string, data map[string]any) error {
	logEvent := map[string]interface{}{
		"level":    level,
		"message":  message,
		"service":  service,
		"data":     data,
		"datetime": GetNowUTC(),
	}

	body, err := json.Marshal(logEvent)
	if err != nil {
		log.Printf("PublishLog: failed to marshal: %v", err)
		return err
	}

	err = p.channel.Publish(
		"",           // exchange (пустая строка => publish в queue напрямую)
		p.queue.Name, // routing key = "logs"
		false,
		false,
		amqp.Publishing{
			ContentType: "application/json",
			Body:        body,
		},
	)
	if err != nil {
		log.Printf("PublishLog: failed to publish: %v", err)
		return err
	}

	log.Printf("PublishLog: log event published to queue '%s': %s", p.queue.Name, string(body))
	return nil
}

func (p *RabbitLogPublisher) Close() {
	p.channel.Close()
	p.conn.Close()
}

// GetNowUTC упрощённая функция
func GetNowUTC() string {
	return time.Now().UTC().Format(time.RFC3339)
}
