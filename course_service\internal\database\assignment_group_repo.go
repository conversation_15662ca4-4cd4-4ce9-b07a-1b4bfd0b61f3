package database

import (
	"context"
	"errors"
	"time"

	"github.com/jackc/pgx/v4/pgxpool"
)

var (
	ErrAssignmentGroupNotFound = errors.New("assignment_group not found")
	ErrWeightExceeded          = errors.New("total weight for thread exceeds 1.0")
)

// AssignmentGroup модель таблицы assignment_groups
type AssignmentGroup struct {
	ID        int64     `json:"id"`
	ThreadID  int64     `json:"thread_id"`
	Name      string    `json:"name"`
	GroupType string    `json:"group_type"`
	Weight    float32   `json:"weight"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type AssignmentGroupRepository interface {
	CreateAssignmentGroup(ctx context.Context, ag *AssignmentGroup) error
	GetAssignmentGroup(ctx context.Context, id int64) (*AssignmentGroup, error)
	GetAssignmentGroupsForThread(ctx context.Context, threadID int64) ([]*AssignmentGroup, error)
	UpdateAssignmentGroup(ctx context.Context, ag *AssignmentGroup) error
	DeleteAssignmentGroup(ctx context.Context, id int64) error
}

type assignmentGroupRepository struct {
	db *pgxpool.Pool
}

// NewAssignmentGroupRepository конструктор
func NewAssignmentGroupRepository(db *pgxpool.Pool) AssignmentGroupRepository {
	return &assignmentGroupRepository{db: db}
}

func (r *assignmentGroupRepository) CreateAssignmentGroup(ctx context.Context, ag *AssignmentGroup) error {
	query := `
        INSERT INTO assignment_groups (thread_id, name, group_type, weight)
        VALUES ($1, $2, $3, $4)
        RETURNING id, created_at, updated_at
    `
	row := r.db.QueryRow(ctx, query, ag.ThreadID, ag.Name, ag.GroupType, ag.Weight)
	if err := row.Scan(&ag.ID, &ag.CreatedAt, &ag.UpdatedAt); err != nil {
		return err
	}
	return nil
}

func (r *assignmentGroupRepository) GetAssignmentGroup(ctx context.Context, id int64) (*AssignmentGroup, error) {
	query := `
        SELECT id, thread_id, name, group_type, weight, created_at, updated_at
        FROM assignment_groups
        WHERE id = $1
    `
	row := r.db.QueryRow(ctx, query, id)
	var ag AssignmentGroup
	if err := row.Scan(
		&ag.ID,
		&ag.ThreadID,
		&ag.Name,
		&ag.GroupType,
		&ag.Weight,
		&ag.CreatedAt,
		&ag.UpdatedAt,
	); err != nil {
		return nil, ErrAssignmentGroupNotFound
	}
	return &ag, nil
}

func (r *assignmentGroupRepository) GetAssignmentGroupsForThread(ctx context.Context, threadID int64) ([]*AssignmentGroup, error) {
	query := `
        SELECT id, thread_id, name, group_type, weight, created_at, updated_at
        FROM assignment_groups
        WHERE thread_id = $1
        ORDER BY id
    `
	rows, err := r.db.Query(ctx, query, threadID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var groups []*AssignmentGroup
	for rows.Next() {
		var ag AssignmentGroup
		if err := rows.Scan(
			&ag.ID,
			&ag.ThreadID,
			&ag.Name,
			&ag.GroupType,
			&ag.Weight,
			&ag.CreatedAt,
			&ag.UpdatedAt,
		); err != nil {
			return nil, err
		}
		groups = append(groups, &ag)
	}
	return groups, rows.Err()
}

func (r *assignmentGroupRepository) UpdateAssignmentGroup(ctx context.Context, ag *AssignmentGroup) error {
	query := `
        UPDATE assignment_groups
        SET thread_id = $1,
            name = $2,
            group_type = $3,
            weight = $4,
            updated_at = NOW()
        WHERE id = $5
        RETURNING updated_at
    `
	row := r.db.QueryRow(ctx, query,
		ag.ThreadID,
		ag.Name,
		ag.GroupType,
		ag.Weight,
		ag.ID,
	)
	if err := row.Scan(&ag.UpdatedAt); err != nil {
		return ErrAssignmentGroupNotFound
	}
	return nil
}

func (r *assignmentGroupRepository) DeleteAssignmentGroup(ctx context.Context, id int64) error {
	cmdTag, err := r.db.Exec(ctx, `
        DELETE FROM assignment_groups
        WHERE id = $1
    `, id)
	if err != nil {
		return err
	}
	if cmdTag.RowsAffected() == 0 {
		return ErrAssignmentGroupNotFound
	}
	return nil
}
