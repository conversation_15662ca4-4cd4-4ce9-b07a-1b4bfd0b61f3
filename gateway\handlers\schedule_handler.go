package handlers

import (
	"fmt"
	"github.com/gin-gonic/gin"

	threadpb "github.com/olzzhas/edunite-server/course_service/pb/thread"
	"net/http"
	"strconv"
)

// CreateThreadScheduleHandler creates a schedule entry for a thread
func (h *ThreadHandler) CreateThreadScheduleHandler(c *gin.Context) {
	// Получаем thread_id из URL
	threadIDStr := c.Param("id")
	threadID, err := strconv.ParseInt(threadIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid thread id"})
		return
	}

	var req threadpb.ThreadScheduleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}
	// Устанавливаем thread_id из URL
	req.ThreadId = threadID

	resp, err := h.ThreadService.CreateThreadSchedule(c.Request.Context(), &req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", fmt.Sprintf("CreateThreadSchedule error: %v", err), "thread_schedule", map[string]any{"thread_id": threadID})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create schedule"})
		return
	}
	c.JSON(http.StatusCreated, resp)
}

// ListThreadSchedulesHandler lists all schedules for a thread
func (h *ThreadHandler) ListThreadSchedulesHandler(c *gin.Context) {
	// Получаем thread_id из URL
	threadIDStr := c.Param("id")
	threadID, err := strconv.ParseInt(threadIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid thread id"})
		return
	}

	req := threadpb.ThreadSchedulesRequest{ThreadId: threadID}
	resp, err := h.ThreadService.ListThreadSchedules(c.Request.Context(), &req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", fmt.Sprintf("ListThreadSchedules error: %v", err), "thread_schedule", map[string]any{"thread_id": threadID})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list schedules"})
		return
	}
	c.JSON(http.StatusOK, resp.Schedules)
}

// GetThreadScheduleByIDHandler gets one schedule by ID
func (h *ThreadHandler) GetThreadScheduleByIDHandler(c *gin.Context) {
	schedIDStr := c.Param("schedule_id")
	schedID, err := strconv.ParseInt(schedIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid schedule_id"})
		return
	}

	req := threadpb.ThreadScheduleByID{ScheduleId: schedID}
	resp, err := h.ThreadService.GetThreadScheduleByID(c.Request.Context(), &req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", fmt.Sprintf("GetThreadScheduleByID error: %v", err), "thread_schedule", map[string]any{"schedule_id": schedID})
		c.JSON(http.StatusNotFound, gin.H{"error": "schedule not found"})
		return
	}
	c.JSON(http.StatusOK, resp)
}

// UpdateThreadScheduleHandler updates an existing schedule
func (h *ThreadHandler) UpdateThreadScheduleHandler(c *gin.Context) {
	schedIDStr := c.Param("schedule_id")
	schedID, err := strconv.ParseInt(schedIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid schedule_id"})
		return
	}

	var req threadpb.ThreadScheduleUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}
	req.Id = schedID

	resp, err := h.ThreadService.UpdateThreadSchedule(c.Request.Context(), &req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", fmt.Sprintf("UpdateThreadSchedule error: %v", err), "thread_schedule", map[string]any{"schedule_id": schedID})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update schedule"})
		return
	}
	c.JSON(http.StatusOK, resp)
}

// DeleteThreadScheduleByIDHandler deletes a schedule by ID
func (h *ThreadHandler) DeleteThreadScheduleByIDHandler(c *gin.Context) {
	schedIDStr := c.Param("schedule_id")
	schedID, err := strconv.ParseInt(schedIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid schedule_id"})
		return
	}

	req := threadpb.ThreadScheduleByID{ScheduleId: schedID}
	_, err = h.ThreadService.DeleteThreadScheduleByID(c.Request.Context(), &req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", fmt.Sprintf("DeleteThreadScheduleByID error: %v", err), "thread_schedule", map[string]any{"schedule_id": schedID})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete schedule"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "schedule deleted"})
}
