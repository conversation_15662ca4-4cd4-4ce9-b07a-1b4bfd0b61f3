// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.2
// source: pb/assignment/assignment.proto

package assignmentpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AssignmentService_CreateAssignmentGroup_FullMethodName                     = "/assignmentpb.AssignmentService/CreateAssignmentGroup"
	AssignmentService_ListAssignmentGroupsForThread_FullMethodName             = "/assignmentpb.AssignmentService/ListAssignmentGroupsForThread"
	AssignmentService_GetAssignmentGroupByID_FullMethodName                    = "/assignmentpb.AssignmentService/GetAssignmentGroupByID"
	AssignmentService_UpdateAssignmentGroupByID_FullMethodName                 = "/assignmentpb.AssignmentService/UpdateAssignmentGroupByID"
	AssignmentService_DeleteAssignmentGroupByID_FullMethodName                 = "/assignmentpb.AssignmentService/DeleteAssignmentGroupByID"
	AssignmentService_CreateAssignment_FullMethodName                          = "/assignmentpb.AssignmentService/CreateAssignment"
	AssignmentService_ListAssignmentsForWeek_FullMethodName                    = "/assignmentpb.AssignmentService/ListAssignmentsForWeek"
	AssignmentService_GetAssignmentByID_FullMethodName                         = "/assignmentpb.AssignmentService/GetAssignmentByID"
	AssignmentService_UpdateAssignmentByID_FullMethodName                      = "/assignmentpb.AssignmentService/UpdateAssignmentByID"
	AssignmentService_DeleteAssignmentByID_FullMethodName                      = "/assignmentpb.AssignmentService/DeleteAssignmentByID"
	AssignmentService_CreateAssignmentAttachment_FullMethodName                = "/assignmentpb.AssignmentService/CreateAssignmentAttachment"
	AssignmentService_GetAssignmentAttachmentsByAssignmentID_FullMethodName    = "/assignmentpb.AssignmentService/GetAssignmentAttachmentsByAssignmentID"
	AssignmentService_DeleteAssignmentAttachmentByID_FullMethodName            = "/assignmentpb.AssignmentService/DeleteAssignmentAttachmentByID"
	AssignmentService_CreateAssignmentSubmission_FullMethodName                = "/assignmentpb.AssignmentService/CreateAssignmentSubmission"
	AssignmentService_GetAssignmentSubmissionByID_FullMethodName               = "/assignmentpb.AssignmentService/GetAssignmentSubmissionByID"
	AssignmentService_ListAssignmentSubmissionsByAssignmentID_FullMethodName   = "/assignmentpb.AssignmentService/ListAssignmentSubmissionsByAssignmentID"
	AssignmentService_UpdateAssignmentSubmissionScore_FullMethodName           = "/assignmentpb.AssignmentService/UpdateAssignmentSubmissionScore"
	AssignmentService_DeleteAssignmentSubmissionByID_FullMethodName            = "/assignmentpb.AssignmentService/DeleteAssignmentSubmissionByID"
	AssignmentService_ListAssignmentsWithSubmissionForThread_FullMethodName    = "/assignmentpb.AssignmentService/ListAssignmentsWithSubmissionForThread"
	AssignmentService_ListAssignmentsWithoutSubmissionForThread_FullMethodName = "/assignmentpb.AssignmentService/ListAssignmentsWithoutSubmissionForThread"
	AssignmentService_GetAssignmentDetailsForStudent_FullMethodName            = "/assignmentpb.AssignmentService/GetAssignmentDetailsForStudent"
)

// AssignmentServiceClient is the client API for AssignmentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AssignmentServiceClient interface {
	// assignment groups entity
	CreateAssignmentGroup(ctx context.Context, in *AssignmentGroupRequest, opts ...grpc.CallOption) (*AssignmentGroupResponse, error)
	ListAssignmentGroupsForThread(ctx context.Context, in *AssignmentGroupsForThread, opts ...grpc.CallOption) (*AssignmentGroupsResponse, error)
	GetAssignmentGroupByID(ctx context.Context, in *AssignmentGroupByID, opts ...grpc.CallOption) (*AssignmentGroupResponse, error)
	UpdateAssignmentGroupByID(ctx context.Context, in *AssignmentGroupUpdateRequest, opts ...grpc.CallOption) (*AssignmentGroupResponse, error)
	DeleteAssignmentGroupByID(ctx context.Context, in *AssignmentGroupByID, opts ...grpc.CallOption) (*AssignmentEmptyResponse, error)
	// Assignment
	CreateAssignment(ctx context.Context, in *AssignmentRequest, opts ...grpc.CallOption) (*AssignmentResponse, error)
	ListAssignmentsForWeek(ctx context.Context, in *AssignmentsForWeekRequest, opts ...grpc.CallOption) (*AssignmentsResponse, error)
	GetAssignmentByID(ctx context.Context, in *AssignmentByID, opts ...grpc.CallOption) (*AssignmentResponse, error)
	UpdateAssignmentByID(ctx context.Context, in *AssignmentUpdateRequest, opts ...grpc.CallOption) (*AssignmentResponse, error)
	DeleteAssignmentByID(ctx context.Context, in *AssignmentByID, opts ...grpc.CallOption) (*AssignmentEmptyResponse, error)
	// Attachment RPC
	CreateAssignmentAttachment(ctx context.Context, in *CreateAssignmentAttachmentRequest, opts ...grpc.CallOption) (*AssignmentAttachmentResponse, error)
	GetAssignmentAttachmentsByAssignmentID(ctx context.Context, in *AssignmentIDRequest, opts ...grpc.CallOption) (*AssignmentAttachmentsResponse, error)
	DeleteAssignmentAttachmentByID(ctx context.Context, in *AssignmentAttachmentByIDRequest, opts ...grpc.CallOption) (*AssignmentEmptyResponse, error)
	// Submission RPC
	CreateAssignmentSubmission(ctx context.Context, in *CreateAssignmentSubmissionRequest, opts ...grpc.CallOption) (*AssignmentSubmissionResponse, error)
	GetAssignmentSubmissionByID(ctx context.Context, in *AssignmentSubmissionByIDRequest, opts ...grpc.CallOption) (*AssignmentSubmissionResponse, error)
	ListAssignmentSubmissionsByAssignmentID(ctx context.Context, in *AssignmentIDRequest, opts ...grpc.CallOption) (*AssignmentSubmissionsResponse, error)
	UpdateAssignmentSubmissionScore(ctx context.Context, in *UpdateAssignmentSubmissionScoreRequest, opts ...grpc.CallOption) (*AssignmentSubmissionResponse, error)
	DeleteAssignmentSubmissionByID(ctx context.Context, in *AssignmentSubmissionByIDRequest, opts ...grpc.CallOption) (*AssignmentEmptyResponse, error)
	ListAssignmentsWithSubmissionForThread(ctx context.Context, in *AssignmentsWithSubmissionRequest, opts ...grpc.CallOption) (*AssignmentsWithSubmissionResponse, error)
	// List assignments without submissions for a student
	ListAssignmentsWithoutSubmissionForThread(ctx context.Context, in *AssignmentsWithSubmissionRequest, opts ...grpc.CallOption) (*AssignmentsWithSubmissionResponse, error)
	// Comprehensive assignment details for a student
	GetAssignmentDetailsForStudent(ctx context.Context, in *AssignmentDetailsForStudentRequest, opts ...grpc.CallOption) (*AssignmentDetailsForStudentResponse, error)
}

type assignmentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAssignmentServiceClient(cc grpc.ClientConnInterface) AssignmentServiceClient {
	return &assignmentServiceClient{cc}
}

func (c *assignmentServiceClient) CreateAssignmentGroup(ctx context.Context, in *AssignmentGroupRequest, opts ...grpc.CallOption) (*AssignmentGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentGroupResponse)
	err := c.cc.Invoke(ctx, AssignmentService_CreateAssignmentGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) ListAssignmentGroupsForThread(ctx context.Context, in *AssignmentGroupsForThread, opts ...grpc.CallOption) (*AssignmentGroupsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentGroupsResponse)
	err := c.cc.Invoke(ctx, AssignmentService_ListAssignmentGroupsForThread_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) GetAssignmentGroupByID(ctx context.Context, in *AssignmentGroupByID, opts ...grpc.CallOption) (*AssignmentGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentGroupResponse)
	err := c.cc.Invoke(ctx, AssignmentService_GetAssignmentGroupByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) UpdateAssignmentGroupByID(ctx context.Context, in *AssignmentGroupUpdateRequest, opts ...grpc.CallOption) (*AssignmentGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentGroupResponse)
	err := c.cc.Invoke(ctx, AssignmentService_UpdateAssignmentGroupByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) DeleteAssignmentGroupByID(ctx context.Context, in *AssignmentGroupByID, opts ...grpc.CallOption) (*AssignmentEmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentEmptyResponse)
	err := c.cc.Invoke(ctx, AssignmentService_DeleteAssignmentGroupByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) CreateAssignment(ctx context.Context, in *AssignmentRequest, opts ...grpc.CallOption) (*AssignmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentResponse)
	err := c.cc.Invoke(ctx, AssignmentService_CreateAssignment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) ListAssignmentsForWeek(ctx context.Context, in *AssignmentsForWeekRequest, opts ...grpc.CallOption) (*AssignmentsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentsResponse)
	err := c.cc.Invoke(ctx, AssignmentService_ListAssignmentsForWeek_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) GetAssignmentByID(ctx context.Context, in *AssignmentByID, opts ...grpc.CallOption) (*AssignmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentResponse)
	err := c.cc.Invoke(ctx, AssignmentService_GetAssignmentByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) UpdateAssignmentByID(ctx context.Context, in *AssignmentUpdateRequest, opts ...grpc.CallOption) (*AssignmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentResponse)
	err := c.cc.Invoke(ctx, AssignmentService_UpdateAssignmentByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) DeleteAssignmentByID(ctx context.Context, in *AssignmentByID, opts ...grpc.CallOption) (*AssignmentEmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentEmptyResponse)
	err := c.cc.Invoke(ctx, AssignmentService_DeleteAssignmentByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) CreateAssignmentAttachment(ctx context.Context, in *CreateAssignmentAttachmentRequest, opts ...grpc.CallOption) (*AssignmentAttachmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentAttachmentResponse)
	err := c.cc.Invoke(ctx, AssignmentService_CreateAssignmentAttachment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) GetAssignmentAttachmentsByAssignmentID(ctx context.Context, in *AssignmentIDRequest, opts ...grpc.CallOption) (*AssignmentAttachmentsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentAttachmentsResponse)
	err := c.cc.Invoke(ctx, AssignmentService_GetAssignmentAttachmentsByAssignmentID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) DeleteAssignmentAttachmentByID(ctx context.Context, in *AssignmentAttachmentByIDRequest, opts ...grpc.CallOption) (*AssignmentEmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentEmptyResponse)
	err := c.cc.Invoke(ctx, AssignmentService_DeleteAssignmentAttachmentByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) CreateAssignmentSubmission(ctx context.Context, in *CreateAssignmentSubmissionRequest, opts ...grpc.CallOption) (*AssignmentSubmissionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentSubmissionResponse)
	err := c.cc.Invoke(ctx, AssignmentService_CreateAssignmentSubmission_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) GetAssignmentSubmissionByID(ctx context.Context, in *AssignmentSubmissionByIDRequest, opts ...grpc.CallOption) (*AssignmentSubmissionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentSubmissionResponse)
	err := c.cc.Invoke(ctx, AssignmentService_GetAssignmentSubmissionByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) ListAssignmentSubmissionsByAssignmentID(ctx context.Context, in *AssignmentIDRequest, opts ...grpc.CallOption) (*AssignmentSubmissionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentSubmissionsResponse)
	err := c.cc.Invoke(ctx, AssignmentService_ListAssignmentSubmissionsByAssignmentID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) UpdateAssignmentSubmissionScore(ctx context.Context, in *UpdateAssignmentSubmissionScoreRequest, opts ...grpc.CallOption) (*AssignmentSubmissionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentSubmissionResponse)
	err := c.cc.Invoke(ctx, AssignmentService_UpdateAssignmentSubmissionScore_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) DeleteAssignmentSubmissionByID(ctx context.Context, in *AssignmentSubmissionByIDRequest, opts ...grpc.CallOption) (*AssignmentEmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentEmptyResponse)
	err := c.cc.Invoke(ctx, AssignmentService_DeleteAssignmentSubmissionByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) ListAssignmentsWithSubmissionForThread(ctx context.Context, in *AssignmentsWithSubmissionRequest, opts ...grpc.CallOption) (*AssignmentsWithSubmissionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentsWithSubmissionResponse)
	err := c.cc.Invoke(ctx, AssignmentService_ListAssignmentsWithSubmissionForThread_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) ListAssignmentsWithoutSubmissionForThread(ctx context.Context, in *AssignmentsWithSubmissionRequest, opts ...grpc.CallOption) (*AssignmentsWithSubmissionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentsWithSubmissionResponse)
	err := c.cc.Invoke(ctx, AssignmentService_ListAssignmentsWithoutSubmissionForThread_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *assignmentServiceClient) GetAssignmentDetailsForStudent(ctx context.Context, in *AssignmentDetailsForStudentRequest, opts ...grpc.CallOption) (*AssignmentDetailsForStudentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentDetailsForStudentResponse)
	err := c.cc.Invoke(ctx, AssignmentService_GetAssignmentDetailsForStudent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AssignmentServiceServer is the server API for AssignmentService service.
// All implementations must embed UnimplementedAssignmentServiceServer
// for forward compatibility.
type AssignmentServiceServer interface {
	// assignment groups entity
	CreateAssignmentGroup(context.Context, *AssignmentGroupRequest) (*AssignmentGroupResponse, error)
	ListAssignmentGroupsForThread(context.Context, *AssignmentGroupsForThread) (*AssignmentGroupsResponse, error)
	GetAssignmentGroupByID(context.Context, *AssignmentGroupByID) (*AssignmentGroupResponse, error)
	UpdateAssignmentGroupByID(context.Context, *AssignmentGroupUpdateRequest) (*AssignmentGroupResponse, error)
	DeleteAssignmentGroupByID(context.Context, *AssignmentGroupByID) (*AssignmentEmptyResponse, error)
	// Assignment
	CreateAssignment(context.Context, *AssignmentRequest) (*AssignmentResponse, error)
	ListAssignmentsForWeek(context.Context, *AssignmentsForWeekRequest) (*AssignmentsResponse, error)
	GetAssignmentByID(context.Context, *AssignmentByID) (*AssignmentResponse, error)
	UpdateAssignmentByID(context.Context, *AssignmentUpdateRequest) (*AssignmentResponse, error)
	DeleteAssignmentByID(context.Context, *AssignmentByID) (*AssignmentEmptyResponse, error)
	// Attachment RPC
	CreateAssignmentAttachment(context.Context, *CreateAssignmentAttachmentRequest) (*AssignmentAttachmentResponse, error)
	GetAssignmentAttachmentsByAssignmentID(context.Context, *AssignmentIDRequest) (*AssignmentAttachmentsResponse, error)
	DeleteAssignmentAttachmentByID(context.Context, *AssignmentAttachmentByIDRequest) (*AssignmentEmptyResponse, error)
	// Submission RPC
	CreateAssignmentSubmission(context.Context, *CreateAssignmentSubmissionRequest) (*AssignmentSubmissionResponse, error)
	GetAssignmentSubmissionByID(context.Context, *AssignmentSubmissionByIDRequest) (*AssignmentSubmissionResponse, error)
	ListAssignmentSubmissionsByAssignmentID(context.Context, *AssignmentIDRequest) (*AssignmentSubmissionsResponse, error)
	UpdateAssignmentSubmissionScore(context.Context, *UpdateAssignmentSubmissionScoreRequest) (*AssignmentSubmissionResponse, error)
	DeleteAssignmentSubmissionByID(context.Context, *AssignmentSubmissionByIDRequest) (*AssignmentEmptyResponse, error)
	ListAssignmentsWithSubmissionForThread(context.Context, *AssignmentsWithSubmissionRequest) (*AssignmentsWithSubmissionResponse, error)
	// List assignments without submissions for a student
	ListAssignmentsWithoutSubmissionForThread(context.Context, *AssignmentsWithSubmissionRequest) (*AssignmentsWithSubmissionResponse, error)
	// Comprehensive assignment details for a student
	GetAssignmentDetailsForStudent(context.Context, *AssignmentDetailsForStudentRequest) (*AssignmentDetailsForStudentResponse, error)
	mustEmbedUnimplementedAssignmentServiceServer()
}

// UnimplementedAssignmentServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAssignmentServiceServer struct{}

func (UnimplementedAssignmentServiceServer) CreateAssignmentGroup(context.Context, *AssignmentGroupRequest) (*AssignmentGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAssignmentGroup not implemented")
}
func (UnimplementedAssignmentServiceServer) ListAssignmentGroupsForThread(context.Context, *AssignmentGroupsForThread) (*AssignmentGroupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAssignmentGroupsForThread not implemented")
}
func (UnimplementedAssignmentServiceServer) GetAssignmentGroupByID(context.Context, *AssignmentGroupByID) (*AssignmentGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssignmentGroupByID not implemented")
}
func (UnimplementedAssignmentServiceServer) UpdateAssignmentGroupByID(context.Context, *AssignmentGroupUpdateRequest) (*AssignmentGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAssignmentGroupByID not implemented")
}
func (UnimplementedAssignmentServiceServer) DeleteAssignmentGroupByID(context.Context, *AssignmentGroupByID) (*AssignmentEmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAssignmentGroupByID not implemented")
}
func (UnimplementedAssignmentServiceServer) CreateAssignment(context.Context, *AssignmentRequest) (*AssignmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAssignment not implemented")
}
func (UnimplementedAssignmentServiceServer) ListAssignmentsForWeek(context.Context, *AssignmentsForWeekRequest) (*AssignmentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAssignmentsForWeek not implemented")
}
func (UnimplementedAssignmentServiceServer) GetAssignmentByID(context.Context, *AssignmentByID) (*AssignmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssignmentByID not implemented")
}
func (UnimplementedAssignmentServiceServer) UpdateAssignmentByID(context.Context, *AssignmentUpdateRequest) (*AssignmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAssignmentByID not implemented")
}
func (UnimplementedAssignmentServiceServer) DeleteAssignmentByID(context.Context, *AssignmentByID) (*AssignmentEmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAssignmentByID not implemented")
}
func (UnimplementedAssignmentServiceServer) CreateAssignmentAttachment(context.Context, *CreateAssignmentAttachmentRequest) (*AssignmentAttachmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAssignmentAttachment not implemented")
}
func (UnimplementedAssignmentServiceServer) GetAssignmentAttachmentsByAssignmentID(context.Context, *AssignmentIDRequest) (*AssignmentAttachmentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssignmentAttachmentsByAssignmentID not implemented")
}
func (UnimplementedAssignmentServiceServer) DeleteAssignmentAttachmentByID(context.Context, *AssignmentAttachmentByIDRequest) (*AssignmentEmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAssignmentAttachmentByID not implemented")
}
func (UnimplementedAssignmentServiceServer) CreateAssignmentSubmission(context.Context, *CreateAssignmentSubmissionRequest) (*AssignmentSubmissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAssignmentSubmission not implemented")
}
func (UnimplementedAssignmentServiceServer) GetAssignmentSubmissionByID(context.Context, *AssignmentSubmissionByIDRequest) (*AssignmentSubmissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssignmentSubmissionByID not implemented")
}
func (UnimplementedAssignmentServiceServer) ListAssignmentSubmissionsByAssignmentID(context.Context, *AssignmentIDRequest) (*AssignmentSubmissionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAssignmentSubmissionsByAssignmentID not implemented")
}
func (UnimplementedAssignmentServiceServer) UpdateAssignmentSubmissionScore(context.Context, *UpdateAssignmentSubmissionScoreRequest) (*AssignmentSubmissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAssignmentSubmissionScore not implemented")
}
func (UnimplementedAssignmentServiceServer) DeleteAssignmentSubmissionByID(context.Context, *AssignmentSubmissionByIDRequest) (*AssignmentEmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAssignmentSubmissionByID not implemented")
}
func (UnimplementedAssignmentServiceServer) ListAssignmentsWithSubmissionForThread(context.Context, *AssignmentsWithSubmissionRequest) (*AssignmentsWithSubmissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAssignmentsWithSubmissionForThread not implemented")
}
func (UnimplementedAssignmentServiceServer) ListAssignmentsWithoutSubmissionForThread(context.Context, *AssignmentsWithSubmissionRequest) (*AssignmentsWithSubmissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAssignmentsWithoutSubmissionForThread not implemented")
}
func (UnimplementedAssignmentServiceServer) GetAssignmentDetailsForStudent(context.Context, *AssignmentDetailsForStudentRequest) (*AssignmentDetailsForStudentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssignmentDetailsForStudent not implemented")
}
func (UnimplementedAssignmentServiceServer) mustEmbedUnimplementedAssignmentServiceServer() {}
func (UnimplementedAssignmentServiceServer) testEmbeddedByValue()                           {}

// UnsafeAssignmentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AssignmentServiceServer will
// result in compilation errors.
type UnsafeAssignmentServiceServer interface {
	mustEmbedUnimplementedAssignmentServiceServer()
}

func RegisterAssignmentServiceServer(s grpc.ServiceRegistrar, srv AssignmentServiceServer) {
	// If the following call pancis, it indicates UnimplementedAssignmentServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AssignmentService_ServiceDesc, srv)
}

func _AssignmentService_CreateAssignmentGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).CreateAssignmentGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_CreateAssignmentGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).CreateAssignmentGroup(ctx, req.(*AssignmentGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_ListAssignmentGroupsForThread_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentGroupsForThread)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).ListAssignmentGroupsForThread(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_ListAssignmentGroupsForThread_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).ListAssignmentGroupsForThread(ctx, req.(*AssignmentGroupsForThread))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_GetAssignmentGroupByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentGroupByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).GetAssignmentGroupByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_GetAssignmentGroupByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).GetAssignmentGroupByID(ctx, req.(*AssignmentGroupByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_UpdateAssignmentGroupByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentGroupUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).UpdateAssignmentGroupByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_UpdateAssignmentGroupByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).UpdateAssignmentGroupByID(ctx, req.(*AssignmentGroupUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_DeleteAssignmentGroupByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentGroupByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).DeleteAssignmentGroupByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_DeleteAssignmentGroupByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).DeleteAssignmentGroupByID(ctx, req.(*AssignmentGroupByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_CreateAssignment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).CreateAssignment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_CreateAssignment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).CreateAssignment(ctx, req.(*AssignmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_ListAssignmentsForWeek_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentsForWeekRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).ListAssignmentsForWeek(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_ListAssignmentsForWeek_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).ListAssignmentsForWeek(ctx, req.(*AssignmentsForWeekRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_GetAssignmentByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).GetAssignmentByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_GetAssignmentByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).GetAssignmentByID(ctx, req.(*AssignmentByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_UpdateAssignmentByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).UpdateAssignmentByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_UpdateAssignmentByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).UpdateAssignmentByID(ctx, req.(*AssignmentUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_DeleteAssignmentByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).DeleteAssignmentByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_DeleteAssignmentByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).DeleteAssignmentByID(ctx, req.(*AssignmentByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_CreateAssignmentAttachment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAssignmentAttachmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).CreateAssignmentAttachment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_CreateAssignmentAttachment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).CreateAssignmentAttachment(ctx, req.(*CreateAssignmentAttachmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_GetAssignmentAttachmentsByAssignmentID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).GetAssignmentAttachmentsByAssignmentID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_GetAssignmentAttachmentsByAssignmentID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).GetAssignmentAttachmentsByAssignmentID(ctx, req.(*AssignmentIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_DeleteAssignmentAttachmentByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentAttachmentByIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).DeleteAssignmentAttachmentByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_DeleteAssignmentAttachmentByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).DeleteAssignmentAttachmentByID(ctx, req.(*AssignmentAttachmentByIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_CreateAssignmentSubmission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAssignmentSubmissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).CreateAssignmentSubmission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_CreateAssignmentSubmission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).CreateAssignmentSubmission(ctx, req.(*CreateAssignmentSubmissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_GetAssignmentSubmissionByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentSubmissionByIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).GetAssignmentSubmissionByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_GetAssignmentSubmissionByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).GetAssignmentSubmissionByID(ctx, req.(*AssignmentSubmissionByIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_ListAssignmentSubmissionsByAssignmentID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).ListAssignmentSubmissionsByAssignmentID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_ListAssignmentSubmissionsByAssignmentID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).ListAssignmentSubmissionsByAssignmentID(ctx, req.(*AssignmentIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_UpdateAssignmentSubmissionScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAssignmentSubmissionScoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).UpdateAssignmentSubmissionScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_UpdateAssignmentSubmissionScore_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).UpdateAssignmentSubmissionScore(ctx, req.(*UpdateAssignmentSubmissionScoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_DeleteAssignmentSubmissionByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentSubmissionByIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).DeleteAssignmentSubmissionByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_DeleteAssignmentSubmissionByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).DeleteAssignmentSubmissionByID(ctx, req.(*AssignmentSubmissionByIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_ListAssignmentsWithSubmissionForThread_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentsWithSubmissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).ListAssignmentsWithSubmissionForThread(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_ListAssignmentsWithSubmissionForThread_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).ListAssignmentsWithSubmissionForThread(ctx, req.(*AssignmentsWithSubmissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_ListAssignmentsWithoutSubmissionForThread_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentsWithSubmissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).ListAssignmentsWithoutSubmissionForThread(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_ListAssignmentsWithoutSubmissionForThread_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).ListAssignmentsWithoutSubmissionForThread(ctx, req.(*AssignmentsWithSubmissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AssignmentService_GetAssignmentDetailsForStudent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentDetailsForStudentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AssignmentServiceServer).GetAssignmentDetailsForStudent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AssignmentService_GetAssignmentDetailsForStudent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AssignmentServiceServer).GetAssignmentDetailsForStudent(ctx, req.(*AssignmentDetailsForStudentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AssignmentService_ServiceDesc is the grpc.ServiceDesc for AssignmentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AssignmentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "assignmentpb.AssignmentService",
	HandlerType: (*AssignmentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAssignmentGroup",
			Handler:    _AssignmentService_CreateAssignmentGroup_Handler,
		},
		{
			MethodName: "ListAssignmentGroupsForThread",
			Handler:    _AssignmentService_ListAssignmentGroupsForThread_Handler,
		},
		{
			MethodName: "GetAssignmentGroupByID",
			Handler:    _AssignmentService_GetAssignmentGroupByID_Handler,
		},
		{
			MethodName: "UpdateAssignmentGroupByID",
			Handler:    _AssignmentService_UpdateAssignmentGroupByID_Handler,
		},
		{
			MethodName: "DeleteAssignmentGroupByID",
			Handler:    _AssignmentService_DeleteAssignmentGroupByID_Handler,
		},
		{
			MethodName: "CreateAssignment",
			Handler:    _AssignmentService_CreateAssignment_Handler,
		},
		{
			MethodName: "ListAssignmentsForWeek",
			Handler:    _AssignmentService_ListAssignmentsForWeek_Handler,
		},
		{
			MethodName: "GetAssignmentByID",
			Handler:    _AssignmentService_GetAssignmentByID_Handler,
		},
		{
			MethodName: "UpdateAssignmentByID",
			Handler:    _AssignmentService_UpdateAssignmentByID_Handler,
		},
		{
			MethodName: "DeleteAssignmentByID",
			Handler:    _AssignmentService_DeleteAssignmentByID_Handler,
		},
		{
			MethodName: "CreateAssignmentAttachment",
			Handler:    _AssignmentService_CreateAssignmentAttachment_Handler,
		},
		{
			MethodName: "GetAssignmentAttachmentsByAssignmentID",
			Handler:    _AssignmentService_GetAssignmentAttachmentsByAssignmentID_Handler,
		},
		{
			MethodName: "DeleteAssignmentAttachmentByID",
			Handler:    _AssignmentService_DeleteAssignmentAttachmentByID_Handler,
		},
		{
			MethodName: "CreateAssignmentSubmission",
			Handler:    _AssignmentService_CreateAssignmentSubmission_Handler,
		},
		{
			MethodName: "GetAssignmentSubmissionByID",
			Handler:    _AssignmentService_GetAssignmentSubmissionByID_Handler,
		},
		{
			MethodName: "ListAssignmentSubmissionsByAssignmentID",
			Handler:    _AssignmentService_ListAssignmentSubmissionsByAssignmentID_Handler,
		},
		{
			MethodName: "UpdateAssignmentSubmissionScore",
			Handler:    _AssignmentService_UpdateAssignmentSubmissionScore_Handler,
		},
		{
			MethodName: "DeleteAssignmentSubmissionByID",
			Handler:    _AssignmentService_DeleteAssignmentSubmissionByID_Handler,
		},
		{
			MethodName: "ListAssignmentsWithSubmissionForThread",
			Handler:    _AssignmentService_ListAssignmentsWithSubmissionForThread_Handler,
		},
		{
			MethodName: "ListAssignmentsWithoutSubmissionForThread",
			Handler:    _AssignmentService_ListAssignmentsWithoutSubmissionForThread_Handler,
		},
		{
			MethodName: "GetAssignmentDetailsForStudent",
			Handler:    _AssignmentService_GetAssignmentDetailsForStudent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/assignment/assignment.proto",
}
