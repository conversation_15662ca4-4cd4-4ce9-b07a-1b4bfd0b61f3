// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/schedule.proto

package sportpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Create schedule request
type CreateScheduleRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	FacilityId           int64                  `protobuf:"varint,1,opt,name=facility_id,json=facilityId,proto3" json:"facility_id,omitempty"`
	TeacherId            int64                  `protobuf:"varint,2,opt,name=teacher_id,json=teacherId,proto3" json:"teacher_id,omitempty"`
	SemesterId           int64                  `protobuf:"varint,3,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	StartTime            *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	CancellationDeadline *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=cancellation_deadline,json=cancellationDeadline,proto3" json:"cancellation_deadline,omitempty"`
	Location             string                 `protobuf:"bytes,7,opt,name=location,proto3" json:"location,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *CreateScheduleRequest) Reset() {
	*x = CreateScheduleRequest{}
	mi := &file_proto_schedule_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateScheduleRequest) ProtoMessage() {}

func (x *CreateScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_schedule_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateScheduleRequest.ProtoReflect.Descriptor instead.
func (*CreateScheduleRequest) Descriptor() ([]byte, []int) {
	return file_proto_schedule_proto_rawDescGZIP(), []int{0}
}

func (x *CreateScheduleRequest) GetFacilityId() int64 {
	if x != nil {
		return x.FacilityId
	}
	return 0
}

func (x *CreateScheduleRequest) GetTeacherId() int64 {
	if x != nil {
		return x.TeacherId
	}
	return 0
}

func (x *CreateScheduleRequest) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *CreateScheduleRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *CreateScheduleRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *CreateScheduleRequest) GetCancellationDeadline() *timestamppb.Timestamp {
	if x != nil {
		return x.CancellationDeadline
	}
	return nil
}

func (x *CreateScheduleRequest) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

// Get schedule request
type GetScheduleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetScheduleRequest) Reset() {
	*x = GetScheduleRequest{}
	mi := &file_proto_schedule_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScheduleRequest) ProtoMessage() {}

func (x *GetScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_schedule_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScheduleRequest.ProtoReflect.Descriptor instead.
func (*GetScheduleRequest) Descriptor() ([]byte, []int) {
	return file_proto_schedule_proto_rawDescGZIP(), []int{1}
}

func (x *GetScheduleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Update schedule request
type UpdateScheduleRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Id                   int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FacilityId           int64                  `protobuf:"varint,2,opt,name=facility_id,json=facilityId,proto3" json:"facility_id,omitempty"`
	TeacherId            int64                  `protobuf:"varint,3,opt,name=teacher_id,json=teacherId,proto3" json:"teacher_id,omitempty"`
	SemesterId           int64                  `protobuf:"varint,4,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	StartTime            *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	CancellationDeadline *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=cancellation_deadline,json=cancellationDeadline,proto3" json:"cancellation_deadline,omitempty"`
	Location             string                 `protobuf:"bytes,8,opt,name=location,proto3" json:"location,omitempty"`
	Version              int32                  `protobuf:"varint,9,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *UpdateScheduleRequest) Reset() {
	*x = UpdateScheduleRequest{}
	mi := &file_proto_schedule_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateScheduleRequest) ProtoMessage() {}

func (x *UpdateScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_schedule_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateScheduleRequest.ProtoReflect.Descriptor instead.
func (*UpdateScheduleRequest) Descriptor() ([]byte, []int) {
	return file_proto_schedule_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateScheduleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateScheduleRequest) GetFacilityId() int64 {
	if x != nil {
		return x.FacilityId
	}
	return 0
}

func (x *UpdateScheduleRequest) GetTeacherId() int64 {
	if x != nil {
		return x.TeacherId
	}
	return 0
}

func (x *UpdateScheduleRequest) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *UpdateScheduleRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *UpdateScheduleRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *UpdateScheduleRequest) GetCancellationDeadline() *timestamppb.Timestamp {
	if x != nil {
		return x.CancellationDeadline
	}
	return nil
}

func (x *UpdateScheduleRequest) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *UpdateScheduleRequest) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

// Delete schedule request
type DeleteScheduleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteScheduleRequest) Reset() {
	*x = DeleteScheduleRequest{}
	mi := &file_proto_schedule_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteScheduleRequest) ProtoMessage() {}

func (x *DeleteScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_schedule_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteScheduleRequest.ProtoReflect.Descriptor instead.
func (*DeleteScheduleRequest) Descriptor() ([]byte, []int) {
	return file_proto_schedule_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteScheduleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// List schedules request
type ListSchedulesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FacilityId    int64                  `protobuf:"varint,1,opt,name=facility_id,json=facilityId,proto3" json:"facility_id,omitempty"`
	TeacherId     int64                  `protobuf:"varint,2,opt,name=teacher_id,json=teacherId,proto3" json:"teacher_id,omitempty"`
	SemesterId    int64                  `protobuf:"varint,3,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Location      string                 `protobuf:"bytes,6,opt,name=location,proto3" json:"location,omitempty"`
	Page          int32                  `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,8,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSchedulesRequest) Reset() {
	*x = ListSchedulesRequest{}
	mi := &file_proto_schedule_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSchedulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSchedulesRequest) ProtoMessage() {}

func (x *ListSchedulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_schedule_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSchedulesRequest.ProtoReflect.Descriptor instead.
func (*ListSchedulesRequest) Descriptor() ([]byte, []int) {
	return file_proto_schedule_proto_rawDescGZIP(), []int{4}
}

func (x *ListSchedulesRequest) GetFacilityId() int64 {
	if x != nil {
		return x.FacilityId
	}
	return 0
}

func (x *ListSchedulesRequest) GetTeacherId() int64 {
	if x != nil {
		return x.TeacherId
	}
	return 0
}

func (x *ListSchedulesRequest) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *ListSchedulesRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ListSchedulesRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ListSchedulesRequest) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *ListSchedulesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSchedulesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Schedule response
type ScheduleResponse struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Id                   int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FacilityId           int64                  `protobuf:"varint,2,opt,name=facility_id,json=facilityId,proto3" json:"facility_id,omitempty"`
	TeacherId            int64                  `protobuf:"varint,3,opt,name=teacher_id,json=teacherId,proto3" json:"teacher_id,omitempty"`
	SemesterId           int64                  `protobuf:"varint,4,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	StartTime            *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	CancellationDeadline *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=cancellation_deadline,json=cancellationDeadline,proto3" json:"cancellation_deadline,omitempty"`
	Location             string                 `protobuf:"bytes,8,opt,name=location,proto3" json:"location,omitempty"`
	CreatedAt            *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt            *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Version              int32                  `protobuf:"varint,11,opt,name=version,proto3" json:"version,omitempty"`
	// Additional information
	Facility       *FacilityInfo `protobuf:"bytes,12,opt,name=facility,proto3" json:"facility,omitempty"`
	Teacher        *TeacherInfo  `protobuf:"bytes,13,opt,name=teacher,proto3" json:"teacher,omitempty"`
	Semester       *SemesterInfo `protobuf:"bytes,14,opt,name=semester,proto3" json:"semester,omitempty"`
	AvailableSpots int32         `protobuf:"varint,15,opt,name=available_spots,json=availableSpots,proto3" json:"available_spots,omitempty"`
	TotalSpots     int32         `protobuf:"varint,16,opt,name=total_spots,json=totalSpots,proto3" json:"total_spots,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ScheduleResponse) Reset() {
	*x = ScheduleResponse{}
	mi := &file_proto_schedule_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScheduleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleResponse) ProtoMessage() {}

func (x *ScheduleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_schedule_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleResponse.ProtoReflect.Descriptor instead.
func (*ScheduleResponse) Descriptor() ([]byte, []int) {
	return file_proto_schedule_proto_rawDescGZIP(), []int{5}
}

func (x *ScheduleResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ScheduleResponse) GetFacilityId() int64 {
	if x != nil {
		return x.FacilityId
	}
	return 0
}

func (x *ScheduleResponse) GetTeacherId() int64 {
	if x != nil {
		return x.TeacherId
	}
	return 0
}

func (x *ScheduleResponse) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *ScheduleResponse) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ScheduleResponse) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *ScheduleResponse) GetCancellationDeadline() *timestamppb.Timestamp {
	if x != nil {
		return x.CancellationDeadline
	}
	return nil
}

func (x *ScheduleResponse) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *ScheduleResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ScheduleResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *ScheduleResponse) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *ScheduleResponse) GetFacility() *FacilityInfo {
	if x != nil {
		return x.Facility
	}
	return nil
}

func (x *ScheduleResponse) GetTeacher() *TeacherInfo {
	if x != nil {
		return x.Teacher
	}
	return nil
}

func (x *ScheduleResponse) GetSemester() *SemesterInfo {
	if x != nil {
		return x.Semester
	}
	return nil
}

func (x *ScheduleResponse) GetAvailableSpots() int32 {
	if x != nil {
		return x.AvailableSpots
	}
	return 0
}

func (x *ScheduleResponse) GetTotalSpots() int32 {
	if x != nil {
		return x.TotalSpots
	}
	return 0
}

// List schedules response
type ListSchedulesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Schedules     []*ScheduleResponse    `protobuf:"bytes,1,rep,name=schedules,proto3" json:"schedules,omitempty"`
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSchedulesResponse) Reset() {
	*x = ListSchedulesResponse{}
	mi := &file_proto_schedule_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSchedulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSchedulesResponse) ProtoMessage() {}

func (x *ListSchedulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_schedule_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSchedulesResponse.ProtoReflect.Descriptor instead.
func (*ListSchedulesResponse) Descriptor() ([]byte, []int) {
	return file_proto_schedule_proto_rawDescGZIP(), []int{6}
}

func (x *ListSchedulesResponse) GetSchedules() []*ScheduleResponse {
	if x != nil {
		return x.Schedules
	}
	return nil
}

func (x *ListSchedulesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListSchedulesResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSchedulesResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Create weekly schedules request
type CreateWeeklySchedulesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FacilityId    int64                  `protobuf:"varint,1,opt,name=facility_id,json=facilityId,proto3" json:"facility_id,omitempty"`
	TeacherId     int64                  `protobuf:"varint,2,opt,name=teacher_id,json=teacherId,proto3" json:"teacher_id,omitempty"`
	SemesterId    int64                  `protobuf:"varint,3,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	SportTypeId   int64                  `protobuf:"varint,4,opt,name=sport_type_id,json=sportTypeId,proto3" json:"sport_type_id,omitempty"`
	DayOfWeek     int32                  `protobuf:"varint,5,opt,name=day_of_week,json=dayOfWeek,proto3" json:"day_of_week,omitempty"` // 1=Monday, 7=Sunday
	StartTime     string                 `protobuf:"bytes,6,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`    // Format: "HH:MM:SS"
	EndTime       string                 `protobuf:"bytes,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`          // Format: "HH:MM:SS"
	Location      string                 `protobuf:"bytes,8,opt,name=location,proto3" json:"location,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"` // First occurrence
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`      // Last occurrence
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateWeeklySchedulesRequest) Reset() {
	*x = CreateWeeklySchedulesRequest{}
	mi := &file_proto_schedule_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWeeklySchedulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWeeklySchedulesRequest) ProtoMessage() {}

func (x *CreateWeeklySchedulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_schedule_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWeeklySchedulesRequest.ProtoReflect.Descriptor instead.
func (*CreateWeeklySchedulesRequest) Descriptor() ([]byte, []int) {
	return file_proto_schedule_proto_rawDescGZIP(), []int{7}
}

func (x *CreateWeeklySchedulesRequest) GetFacilityId() int64 {
	if x != nil {
		return x.FacilityId
	}
	return 0
}

func (x *CreateWeeklySchedulesRequest) GetTeacherId() int64 {
	if x != nil {
		return x.TeacherId
	}
	return 0
}

func (x *CreateWeeklySchedulesRequest) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *CreateWeeklySchedulesRequest) GetSportTypeId() int64 {
	if x != nil {
		return x.SportTypeId
	}
	return 0
}

func (x *CreateWeeklySchedulesRequest) GetDayOfWeek() int32 {
	if x != nil {
		return x.DayOfWeek
	}
	return 0
}

func (x *CreateWeeklySchedulesRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *CreateWeeklySchedulesRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *CreateWeeklySchedulesRequest) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *CreateWeeklySchedulesRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *CreateWeeklySchedulesRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

// Create weekly schedules response
type CreateWeeklySchedulesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Schedules     []*ScheduleResponse    `protobuf:"bytes,1,rep,name=schedules,proto3" json:"schedules,omitempty"`
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateWeeklySchedulesResponse) Reset() {
	*x = CreateWeeklySchedulesResponse{}
	mi := &file_proto_schedule_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWeeklySchedulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWeeklySchedulesResponse) ProtoMessage() {}

func (x *CreateWeeklySchedulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_schedule_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWeeklySchedulesResponse.ProtoReflect.Descriptor instead.
func (*CreateWeeklySchedulesResponse) Descriptor() ([]byte, []int) {
	return file_proto_schedule_proto_rawDescGZIP(), []int{8}
}

func (x *CreateWeeklySchedulesResponse) GetSchedules() []*ScheduleResponse {
	if x != nil {
		return x.Schedules
	}
	return nil
}

func (x *CreateWeeklySchedulesResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// Get schedules for teacher request
type GetSchedulesForTeacherRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TeacherId     int64                  `protobuf:"varint,1,opt,name=teacher_id,json=teacherId,proto3" json:"teacher_id,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSchedulesForTeacherRequest) Reset() {
	*x = GetSchedulesForTeacherRequest{}
	mi := &file_proto_schedule_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSchedulesForTeacherRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSchedulesForTeacherRequest) ProtoMessage() {}

func (x *GetSchedulesForTeacherRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_schedule_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSchedulesForTeacherRequest.ProtoReflect.Descriptor instead.
func (*GetSchedulesForTeacherRequest) Descriptor() ([]byte, []int) {
	return file_proto_schedule_proto_rawDescGZIP(), []int{9}
}

func (x *GetSchedulesForTeacherRequest) GetTeacherId() int64 {
	if x != nil {
		return x.TeacherId
	}
	return 0
}

func (x *GetSchedulesForTeacherRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetSchedulesForTeacherRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

// Get schedules for facility request
type GetSchedulesForFacilityRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FacilityId    int64                  `protobuf:"varint,1,opt,name=facility_id,json=facilityId,proto3" json:"facility_id,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSchedulesForFacilityRequest) Reset() {
	*x = GetSchedulesForFacilityRequest{}
	mi := &file_proto_schedule_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSchedulesForFacilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSchedulesForFacilityRequest) ProtoMessage() {}

func (x *GetSchedulesForFacilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_schedule_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSchedulesForFacilityRequest.ProtoReflect.Descriptor instead.
func (*GetSchedulesForFacilityRequest) Descriptor() ([]byte, []int) {
	return file_proto_schedule_proto_rawDescGZIP(), []int{10}
}

func (x *GetSchedulesForFacilityRequest) GetFacilityId() int64 {
	if x != nil {
		return x.FacilityId
	}
	return 0
}

func (x *GetSchedulesForFacilityRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetSchedulesForFacilityRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

// Get schedules for semester request
type GetSchedulesForSemesterRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SemesterId    int64                  `protobuf:"varint,1,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSchedulesForSemesterRequest) Reset() {
	*x = GetSchedulesForSemesterRequest{}
	mi := &file_proto_schedule_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSchedulesForSemesterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSchedulesForSemesterRequest) ProtoMessage() {}

func (x *GetSchedulesForSemesterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_schedule_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSchedulesForSemesterRequest.ProtoReflect.Descriptor instead.
func (*GetSchedulesForSemesterRequest) Descriptor() ([]byte, []int) {
	return file_proto_schedule_proto_rawDescGZIP(), []int{11}
}

func (x *GetSchedulesForSemesterRequest) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

// Facility info (simplified)
type FacilityInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	MaxCapacity   int32                  `protobuf:"varint,3,opt,name=max_capacity,json=maxCapacity,proto3" json:"max_capacity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FacilityInfo) Reset() {
	*x = FacilityInfo{}
	mi := &file_proto_schedule_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FacilityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FacilityInfo) ProtoMessage() {}

func (x *FacilityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_schedule_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FacilityInfo.ProtoReflect.Descriptor instead.
func (*FacilityInfo) Descriptor() ([]byte, []int) {
	return file_proto_schedule_proto_rawDescGZIP(), []int{12}
}

func (x *FacilityInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FacilityInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FacilityInfo) GetMaxCapacity() int32 {
	if x != nil {
		return x.MaxCapacity
	}
	return 0
}

// Teacher info (simplified)
type TeacherInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TeacherInfo) Reset() {
	*x = TeacherInfo{}
	mi := &file_proto_schedule_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TeacherInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TeacherInfo) ProtoMessage() {}

func (x *TeacherInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_schedule_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TeacherInfo.ProtoReflect.Descriptor instead.
func (*TeacherInfo) Descriptor() ([]byte, []int) {
	return file_proto_schedule_proto_rawDescGZIP(), []int{13}
}

func (x *TeacherInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TeacherInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Semester info (simplified)
type SemesterInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SemesterInfo) Reset() {
	*x = SemesterInfo{}
	mi := &file_proto_schedule_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SemesterInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SemesterInfo) ProtoMessage() {}

func (x *SemesterInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_schedule_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SemesterInfo.ProtoReflect.Descriptor instead.
func (*SemesterInfo) Descriptor() ([]byte, []int) {
	return file_proto_schedule_proto_rawDescGZIP(), []int{14}
}

func (x *SemesterInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SemesterInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SemesterInfo) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *SemesterInfo) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

var File_proto_schedule_proto protoreflect.FileDescriptor

const file_proto_schedule_proto_rawDesc = "" +
	"\n" +
	"\x14proto/schedule.proto\x12\asportpb\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\"\xd7\x02\n" +
	"\x15CreateScheduleRequest\x12\x1f\n" +
	"\vfacility_id\x18\x01 \x01(\x03R\n" +
	"facilityId\x12\x1d\n" +
	"\n" +
	"teacher_id\x18\x02 \x01(\x03R\tteacherId\x12\x1f\n" +
	"\vsemester_id\x18\x03 \x01(\x03R\n" +
	"semesterId\x129\n" +
	"\n" +
	"start_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12O\n" +
	"\x15cancellation_deadline\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\x14cancellationDeadline\x12\x1a\n" +
	"\blocation\x18\a \x01(\tR\blocation\"$\n" +
	"\x12GetScheduleRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\x81\x03\n" +
	"\x15UpdateScheduleRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vfacility_id\x18\x02 \x01(\x03R\n" +
	"facilityId\x12\x1d\n" +
	"\n" +
	"teacher_id\x18\x03 \x01(\x03R\tteacherId\x12\x1f\n" +
	"\vsemester_id\x18\x04 \x01(\x03R\n" +
	"semesterId\x129\n" +
	"\n" +
	"start_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12O\n" +
	"\x15cancellation_deadline\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\x14cancellationDeadline\x12\x1a\n" +
	"\blocation\x18\b \x01(\tR\blocation\x12\x18\n" +
	"\aversion\x18\t \x01(\x05R\aversion\"'\n" +
	"\x15DeleteScheduleRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\xb6\x02\n" +
	"\x14ListSchedulesRequest\x12\x1f\n" +
	"\vfacility_id\x18\x01 \x01(\x03R\n" +
	"facilityId\x12\x1d\n" +
	"\n" +
	"teacher_id\x18\x02 \x01(\x03R\tteacherId\x12\x1f\n" +
	"\vsemester_id\x18\x03 \x01(\x03R\n" +
	"semesterId\x129\n" +
	"\n" +
	"start_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\x12\x1a\n" +
	"\blocation\x18\x06 \x01(\tR\blocation\x12\x12\n" +
	"\x04page\x18\a \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\b \x01(\x05R\bpageSize\"\xd2\x05\n" +
	"\x10ScheduleResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vfacility_id\x18\x02 \x01(\x03R\n" +
	"facilityId\x12\x1d\n" +
	"\n" +
	"teacher_id\x18\x03 \x01(\x03R\tteacherId\x12\x1f\n" +
	"\vsemester_id\x18\x04 \x01(\x03R\n" +
	"semesterId\x129\n" +
	"\n" +
	"start_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12O\n" +
	"\x15cancellation_deadline\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\x14cancellationDeadline\x12\x1a\n" +
	"\blocation\x18\b \x01(\tR\blocation\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12\x18\n" +
	"\aversion\x18\v \x01(\x05R\aversion\x121\n" +
	"\bfacility\x18\f \x01(\v2\x15.sportpb.FacilityInfoR\bfacility\x12.\n" +
	"\ateacher\x18\r \x01(\v2\x14.sportpb.TeacherInfoR\ateacher\x121\n" +
	"\bsemester\x18\x0e \x01(\v2\x15.sportpb.SemesterInfoR\bsemester\x12'\n" +
	"\x0favailable_spots\x18\x0f \x01(\x05R\x0eavailableSpots\x12\x1f\n" +
	"\vtotal_spots\x18\x10 \x01(\x05R\n" +
	"totalSpots\"\x97\x01\n" +
	"\x15ListSchedulesResponse\x127\n" +
	"\tschedules\x18\x01 \x03(\v2\x19.sportpb.ScheduleResponseR\tschedules\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"\x8b\x03\n" +
	"\x1cCreateWeeklySchedulesRequest\x12\x1f\n" +
	"\vfacility_id\x18\x01 \x01(\x03R\n" +
	"facilityId\x12\x1d\n" +
	"\n" +
	"teacher_id\x18\x02 \x01(\x03R\tteacherId\x12\x1f\n" +
	"\vsemester_id\x18\x03 \x01(\x03R\n" +
	"semesterId\x12\"\n" +
	"\rsport_type_id\x18\x04 \x01(\x03R\vsportTypeId\x12\x1e\n" +
	"\vday_of_week\x18\x05 \x01(\x05R\tdayOfWeek\x12\x1d\n" +
	"\n" +
	"start_time\x18\x06 \x01(\tR\tstartTime\x12\x19\n" +
	"\bend_time\x18\a \x01(\tR\aendTime\x12\x1a\n" +
	"\blocation\x18\b \x01(\tR\blocation\x129\n" +
	"\n" +
	"start_date\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\"n\n" +
	"\x1dCreateWeeklySchedulesResponse\x127\n" +
	"\tschedules\x18\x01 \x03(\v2\x19.sportpb.ScheduleResponseR\tschedules\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\"\xb0\x01\n" +
	"\x1dGetSchedulesForTeacherRequest\x12\x1d\n" +
	"\n" +
	"teacher_id\x18\x01 \x01(\x03R\tteacherId\x129\n" +
	"\n" +
	"start_date\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\"\xb3\x01\n" +
	"\x1eGetSchedulesForFacilityRequest\x12\x1f\n" +
	"\vfacility_id\x18\x01 \x01(\x03R\n" +
	"facilityId\x129\n" +
	"\n" +
	"start_date\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\"A\n" +
	"\x1eGetSchedulesForSemesterRequest\x12\x1f\n" +
	"\vsemester_id\x18\x01 \x01(\x03R\n" +
	"semesterId\"W\n" +
	"\fFacilityInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12!\n" +
	"\fmax_capacity\x18\x03 \x01(\x05R\vmaxCapacity\"1\n" +
	"\vTeacherInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"\xa4\x01\n" +
	"\fSemesterInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x129\n" +
	"\n" +
	"start_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate2\xb0\x06\n" +
	"\x0fScheduleService\x12M\n" +
	"\x0eCreateSchedule\x12\x1e.sportpb.CreateScheduleRequest\x1a\x19.sportpb.ScheduleResponse\"\x00\x12G\n" +
	"\vGetSchedule\x12\x1b.sportpb.GetScheduleRequest\x1a\x19.sportpb.ScheduleResponse\"\x00\x12M\n" +
	"\x0eUpdateSchedule\x12\x1e.sportpb.UpdateScheduleRequest\x1a\x19.sportpb.ScheduleResponse\"\x00\x12J\n" +
	"\x0eDeleteSchedule\x12\x1e.sportpb.DeleteScheduleRequest\x1a\x16.google.protobuf.Empty\"\x00\x12P\n" +
	"\rListSchedules\x12\x1d.sportpb.ListSchedulesRequest\x1a\x1e.sportpb.ListSchedulesResponse\"\x00\x12h\n" +
	"\x15CreateWeeklySchedules\x12%.sportpb.CreateWeeklySchedulesRequest\x1a&.sportpb.CreateWeeklySchedulesResponse\"\x00\x12b\n" +
	"\x16GetSchedulesForTeacher\x12&.sportpb.GetSchedulesForTeacherRequest\x1a\x1e.sportpb.ListSchedulesResponse\"\x00\x12d\n" +
	"\x17GetSchedulesForFacility\x12'.sportpb.GetSchedulesForFacilityRequest\x1a\x1e.sportpb.ListSchedulesResponse\"\x00\x12d\n" +
	"\x17GetSchedulesForSemester\x12'.sportpb.GetSchedulesForSemesterRequest\x1a\x1e.sportpb.ListSchedulesResponse\"\x00B<Z:github.com/olzzhas/edunite-server/sport_service/pb/sportpbb\x06proto3"

var (
	file_proto_schedule_proto_rawDescOnce sync.Once
	file_proto_schedule_proto_rawDescData []byte
)

func file_proto_schedule_proto_rawDescGZIP() []byte {
	file_proto_schedule_proto_rawDescOnce.Do(func() {
		file_proto_schedule_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_schedule_proto_rawDesc), len(file_proto_schedule_proto_rawDesc)))
	})
	return file_proto_schedule_proto_rawDescData
}

var file_proto_schedule_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_proto_schedule_proto_goTypes = []any{
	(*CreateScheduleRequest)(nil),          // 0: sportpb.CreateScheduleRequest
	(*GetScheduleRequest)(nil),             // 1: sportpb.GetScheduleRequest
	(*UpdateScheduleRequest)(nil),          // 2: sportpb.UpdateScheduleRequest
	(*DeleteScheduleRequest)(nil),          // 3: sportpb.DeleteScheduleRequest
	(*ListSchedulesRequest)(nil),           // 4: sportpb.ListSchedulesRequest
	(*ScheduleResponse)(nil),               // 5: sportpb.ScheduleResponse
	(*ListSchedulesResponse)(nil),          // 6: sportpb.ListSchedulesResponse
	(*CreateWeeklySchedulesRequest)(nil),   // 7: sportpb.CreateWeeklySchedulesRequest
	(*CreateWeeklySchedulesResponse)(nil),  // 8: sportpb.CreateWeeklySchedulesResponse
	(*GetSchedulesForTeacherRequest)(nil),  // 9: sportpb.GetSchedulesForTeacherRequest
	(*GetSchedulesForFacilityRequest)(nil), // 10: sportpb.GetSchedulesForFacilityRequest
	(*GetSchedulesForSemesterRequest)(nil), // 11: sportpb.GetSchedulesForSemesterRequest
	(*FacilityInfo)(nil),                   // 12: sportpb.FacilityInfo
	(*TeacherInfo)(nil),                    // 13: sportpb.TeacherInfo
	(*SemesterInfo)(nil),                   // 14: sportpb.SemesterInfo
	(*timestamppb.Timestamp)(nil),          // 15: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                  // 16: google.protobuf.Empty
}
var file_proto_schedule_proto_depIdxs = []int32{
	15, // 0: sportpb.CreateScheduleRequest.start_time:type_name -> google.protobuf.Timestamp
	15, // 1: sportpb.CreateScheduleRequest.end_time:type_name -> google.protobuf.Timestamp
	15, // 2: sportpb.CreateScheduleRequest.cancellation_deadline:type_name -> google.protobuf.Timestamp
	15, // 3: sportpb.UpdateScheduleRequest.start_time:type_name -> google.protobuf.Timestamp
	15, // 4: sportpb.UpdateScheduleRequest.end_time:type_name -> google.protobuf.Timestamp
	15, // 5: sportpb.UpdateScheduleRequest.cancellation_deadline:type_name -> google.protobuf.Timestamp
	15, // 6: sportpb.ListSchedulesRequest.start_date:type_name -> google.protobuf.Timestamp
	15, // 7: sportpb.ListSchedulesRequest.end_date:type_name -> google.protobuf.Timestamp
	15, // 8: sportpb.ScheduleResponse.start_time:type_name -> google.protobuf.Timestamp
	15, // 9: sportpb.ScheduleResponse.end_time:type_name -> google.protobuf.Timestamp
	15, // 10: sportpb.ScheduleResponse.cancellation_deadline:type_name -> google.protobuf.Timestamp
	15, // 11: sportpb.ScheduleResponse.created_at:type_name -> google.protobuf.Timestamp
	15, // 12: sportpb.ScheduleResponse.updated_at:type_name -> google.protobuf.Timestamp
	12, // 13: sportpb.ScheduleResponse.facility:type_name -> sportpb.FacilityInfo
	13, // 14: sportpb.ScheduleResponse.teacher:type_name -> sportpb.TeacherInfo
	14, // 15: sportpb.ScheduleResponse.semester:type_name -> sportpb.SemesterInfo
	5,  // 16: sportpb.ListSchedulesResponse.schedules:type_name -> sportpb.ScheduleResponse
	15, // 17: sportpb.CreateWeeklySchedulesRequest.start_date:type_name -> google.protobuf.Timestamp
	15, // 18: sportpb.CreateWeeklySchedulesRequest.end_date:type_name -> google.protobuf.Timestamp
	5,  // 19: sportpb.CreateWeeklySchedulesResponse.schedules:type_name -> sportpb.ScheduleResponse
	15, // 20: sportpb.GetSchedulesForTeacherRequest.start_date:type_name -> google.protobuf.Timestamp
	15, // 21: sportpb.GetSchedulesForTeacherRequest.end_date:type_name -> google.protobuf.Timestamp
	15, // 22: sportpb.GetSchedulesForFacilityRequest.start_date:type_name -> google.protobuf.Timestamp
	15, // 23: sportpb.GetSchedulesForFacilityRequest.end_date:type_name -> google.protobuf.Timestamp
	15, // 24: sportpb.SemesterInfo.start_date:type_name -> google.protobuf.Timestamp
	15, // 25: sportpb.SemesterInfo.end_date:type_name -> google.protobuf.Timestamp
	0,  // 26: sportpb.ScheduleService.CreateSchedule:input_type -> sportpb.CreateScheduleRequest
	1,  // 27: sportpb.ScheduleService.GetSchedule:input_type -> sportpb.GetScheduleRequest
	2,  // 28: sportpb.ScheduleService.UpdateSchedule:input_type -> sportpb.UpdateScheduleRequest
	3,  // 29: sportpb.ScheduleService.DeleteSchedule:input_type -> sportpb.DeleteScheduleRequest
	4,  // 30: sportpb.ScheduleService.ListSchedules:input_type -> sportpb.ListSchedulesRequest
	7,  // 31: sportpb.ScheduleService.CreateWeeklySchedules:input_type -> sportpb.CreateWeeklySchedulesRequest
	9,  // 32: sportpb.ScheduleService.GetSchedulesForTeacher:input_type -> sportpb.GetSchedulesForTeacherRequest
	10, // 33: sportpb.ScheduleService.GetSchedulesForFacility:input_type -> sportpb.GetSchedulesForFacilityRequest
	11, // 34: sportpb.ScheduleService.GetSchedulesForSemester:input_type -> sportpb.GetSchedulesForSemesterRequest
	5,  // 35: sportpb.ScheduleService.CreateSchedule:output_type -> sportpb.ScheduleResponse
	5,  // 36: sportpb.ScheduleService.GetSchedule:output_type -> sportpb.ScheduleResponse
	5,  // 37: sportpb.ScheduleService.UpdateSchedule:output_type -> sportpb.ScheduleResponse
	16, // 38: sportpb.ScheduleService.DeleteSchedule:output_type -> google.protobuf.Empty
	6,  // 39: sportpb.ScheduleService.ListSchedules:output_type -> sportpb.ListSchedulesResponse
	8,  // 40: sportpb.ScheduleService.CreateWeeklySchedules:output_type -> sportpb.CreateWeeklySchedulesResponse
	6,  // 41: sportpb.ScheduleService.GetSchedulesForTeacher:output_type -> sportpb.ListSchedulesResponse
	6,  // 42: sportpb.ScheduleService.GetSchedulesForFacility:output_type -> sportpb.ListSchedulesResponse
	6,  // 43: sportpb.ScheduleService.GetSchedulesForSemester:output_type -> sportpb.ListSchedulesResponse
	35, // [35:44] is the sub-list for method output_type
	26, // [26:35] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_proto_schedule_proto_init() }
func file_proto_schedule_proto_init() {
	if File_proto_schedule_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_schedule_proto_rawDesc), len(file_proto_schedule_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_schedule_proto_goTypes,
		DependencyIndexes: file_proto_schedule_proto_depIdxs,
		MessageInfos:      file_proto_schedule_proto_msgTypes,
	}.Build()
	File_proto_schedule_proto = out.File
	file_proto_schedule_proto_goTypes = nil
	file_proto_schedule_proto_depIdxs = nil
}
