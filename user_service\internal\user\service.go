package user

import (
	"context"
	"time"

	"github.com/olzzhas/edunite-server/user_service/internal/database"
	"github.com/olzzhas/edunite-server/user_service/pb"
	"github.com/olzzhas/edunite-server/user_service/pkg/validator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type Service struct {
	repo database.UserRepository
	pb.UnimplementedUserServiceServer
}

func NewUserService(repo database.UserRepository) *Service {
	return &Service{repo: repo}
}

// CreateUser Создать пользователя
func (s *Service) CreateUser(ctx context.Context, req *pb.CreateUserRequest) (*pb.UserResponse, error) {
	var role database.UserRole
	switch req.GetRole() {
	case "admin":
		role = database.RoleAdmin
	case "teacher":
		role = database.RoleTeacher
	case "moderator":
		role = database.RoleModerator
	default:
		role = database.RoleStudent
	}

	// Create user object
	user := database.User{
		ID:      req.GetId(),
		Name:    req.GetName(),
		Surname: req.Get<PERSON>urname(),
		Email:   req.GetEmail(),
		Role:    role,
	}

	// Only set KeycloakID if it's not empty
	if req.GetKeycloakID() != "" {
		keycloakID := req.GetKeycloakID()
		user.KeycloakID = &keycloakID
	}

	// Username and password hash will be set by the auth service when a user registers
	err := s.repo.CreateUser(ctx, &user)
	if err != nil {
		return nil, err
	}

	// Create response
	response := &pb.UserResponse{
		Id:        user.ID,
		Name:      user.Name,
		Surname:   user.Surname,
		Role:      string(user.Role),
		Email:     user.Email,
		CreatedAt: user.CreatedAt.Format(time.RFC3339),
		UpdatedAt: user.UpdatedAt.Format(time.RFC3339),
		Version:   int32(user.Version),
	}

	// Only set KeycloakID if it's not nil
	if user.KeycloakID != nil {
		response.KeycloakID = *user.KeycloakID
	}

	return response, nil
}

// GetUser Получить пользователя по ID
func (s *Service) GetUser(ctx context.Context, req *pb.GetUserRequest) (*pb.UserResponse, error) {
	user, err := s.repo.GetUserByID(ctx, req.GetId())
	if err != nil {
		return nil, err
	}
	// Create response
	response := &pb.UserResponse{
		Id:        user.ID,
		Name:      user.Name,
		Surname:   user.Surname,
		Role:      string(user.Role),
		Email:     user.Email,
		CreatedAt: user.CreatedAt.Format(time.RFC3339),
		UpdatedAt: user.UpdatedAt.Format(time.RFC3339),
		Version:   int32(user.Version),
	}

	// Only set KeycloakID if it's not nil
	if user.KeycloakID != nil {
		response.KeycloakID = *user.KeycloakID
	}

	return response, nil
}

// GetAllUsers Получить всех пользователей
// GetAllUsers – расширенная версия с фильтрами, поиском и пагинацией.
func (s *Service) GetAllUsers(
	ctx context.Context,
	req *pb.GetAllUsersRequest,
) (*pb.UsersWithMeta, error) {

	// 1. Подготовим фильтры из запроса
	f := validator.Filters{
		Page:         int(req.Filters.GetPage()),
		PageSize:     int(req.Filters.GetPageSize()),
		Sort:         req.Filters.GetSort(),
		SortSafelist: []string{"id", "name", "surname", "email", "created_at", "-created_at"},
	}
	v := validator.New()
	database.ValidateFilters(v, f) // короткая обёртка-помощник

	// Validate role if provided
	role := req.Filters.GetRole()
	if role != "" {
		// Check if role is valid
		switch role {
		case "student", "teacher", "moderator", "admin":
			// Valid role
		default:
			return nil, status.Errorf(codes.InvalidArgument, "invalid role: %s", role)
		}
	}

	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "%v", v.Errors)
	}

	// 2. Получаем пользователей + метаданные из репозитория
	users, meta, err := s.repo.GetAllUsers(ctx, req.Filters.GetSearch(), f, role)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "repo: %v", err)
	}

	// 3. Маппим в gRPC-структуры
	resp := &pb.UsersWithMeta{
		CurrentPage:  int32(meta.CurrentPage),
		PageSize:     int32(meta.PageSize),
		FirstPage:    int32(meta.FirstPage),
		LastPage:     int32(meta.LastPage),
		TotalRecords: int32(meta.TotalRecords),
	}
	resp.Users = make([]*pb.UserResponse, 0, len(users))
	for _, u := range users {
		// Create response
		userResp := &pb.UserResponse{
			Id:        u.ID,
			Name:      u.Name,
			Surname:   u.Surname,
			Role:      string(u.Role),
			Email:     u.Email,
			CreatedAt: u.CreatedAt.Format(time.RFC3339),
			UpdatedAt: u.UpdatedAt.Format(time.RFC3339),
			Version:   int32(u.Version),
		}

		// Only set KeycloakID if it's not nil
		if u.KeycloakID != nil {
			userResp.KeycloakID = *u.KeycloakID
		}

		resp.Users = append(resp.Users, userResp)
	}
	return resp, nil
}

// DeleteUser Удалить пользователя
func (s *Service) DeleteUser(ctx context.Context, req *pb.DeleteUserRequest) (*pb.EmptyResponse, error) {
	err := s.repo.DeleteUser(ctx, req.GetId())
	if err != nil {
		return nil, err
	}
	return &pb.EmptyResponse{}, nil
}

func (s *Service) GetUserByEmail(ctx context.Context, req *pb.GetUserByEmailRequest) (*pb.UserResponse, error) {
	user, err := s.repo.GetUserByEmail(ctx, req.GetEmail())
	if err != nil {
		return nil, err
	}

	// Create response
	userResponse := &pb.UserResponse{
		Id:        user.ID,
		Name:      user.Name,
		Surname:   user.Surname,
		Role:      string(user.Role),
		Email:     user.Email,
		CreatedAt: user.CreatedAt.Format(time.RFC3339),
		UpdatedAt: user.UpdatedAt.Format(time.RFC3339),
		Version:   int32(user.Version),
	}

	// Only set KeycloakID if it's not nil
	if user.KeycloakID != nil {
		userResponse.KeycloakID = *user.KeycloakID
	}

	return userResponse, nil
}

// UpdateUser обновляет информацию о пользователе
func (s *Service) UpdateUser(ctx context.Context, req *pb.UpdateUserRequest) (*pb.UserResponse, error) {
	// 1. Получаем пользователя по ID
	existingUser, err := s.repo.GetUserByID(ctx, req.GetId())
	if err != nil {
		return nil, status.Errorf(codes.NotFound, "user not found: %v", err)
	}

	// 2. Обновляем поля пользователя
	if req.GetName() != "" {
		existingUser.Name = req.GetName()
	}
	if req.GetSurname() != "" {
		existingUser.Surname = req.GetSurname()
	}
	if req.GetEmail() != "" {
		existingUser.Email = req.GetEmail()
	}
	if req.GetRole() != "" {
		// Проверяем валидность роли
		var role database.UserRole
		switch req.GetRole() {
		case "admin":
			role = database.RoleAdmin
		case "teacher":
			role = database.RoleTeacher
		case "moderator":
			role = database.RoleModerator
		case "student":
			role = database.RoleStudent
		default:
			return nil, status.Errorf(codes.InvalidArgument, "invalid role: %s", req.GetRole())
		}
		existingUser.Role = role
	}

	// 3. Сохраняем обновленного пользователя
	if err := s.repo.UpdateUser(ctx, existingUser); err != nil {
		return nil, status.Errorf(codes.Internal, "failed to update user: %v", err)
	}

	// 4. Возвращаем обновленного пользователя
	// Create response
	response := &pb.UserResponse{
		Id:        existingUser.ID,
		Name:      existingUser.Name,
		Surname:   existingUser.Surname,
		Role:      string(existingUser.Role),
		Email:     existingUser.Email,
		CreatedAt: existingUser.CreatedAt.Format(time.RFC3339),
		UpdatedAt: existingUser.UpdatedAt.Format(time.RFC3339),
		Version:   int32(existingUser.Version),
	}

	// Only set KeycloakID if it's not nil
	if existingUser.KeycloakID != nil {
		response.KeycloakID = *existingUser.KeycloakID
	}

	return response, nil
}
