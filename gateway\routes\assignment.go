package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

// SetupAssignmentRoutes registers assignment routes on the router
func SetupAssignmentRoutes(r *gin.Engine, h *handlers.AssignmentHandler) {
	s := r.Group("/assignments")
	{
		// Groups
		s.POST("/groups", h.CreateAssignmentGroup)
		s.GET("/groups/thread/:threadId", h.ListAssignmentGroupsForThread)
		s.GET("/groups/:id", h.GetAssignmentGroupByID)
		s.PUT("/groups/:id", h.UpdateAssignmentGroupByID)
		s.DELETE("/groups/:id", h.DeleteAssignmentGroupByID)

		// Attachments
		s.POST("/:assignmentId/attachments", h.CreateAssignmentAttachment)
		s.GET("/:assignmentId/attachments", h.GetAssignmentAttachmentsByAssignmentID)
		s.DELETE("/attachments/:id", h.DeleteAssignmentAttachmentByID)

		// Submissions
		s.POST("/:assignmentId/submissions", h.CreateAssignmentSubmission)
		s.GET("/submissions/:id", h.GetAssignmentSubmissionByID)
		s.GET("/:assignmentId/submissions", h.ListAssignmentSubmissionsByAssignmentID)
		s.PUT("/submissions/:id", h.UpdateAssignmentSubmission) // New endpoint for updating submission content
		s.PUT("/submissions/:id/score", h.UpdateAssignmentSubmissionScore)
		s.DELETE("/submissions/:id", h.DeleteAssignmentSubmissionByID)

		// Assignment
		s.POST("/weeks/:weekId/assignments", h.CreateAssignmentHandler)
		s.GET("/weeks/:weekId/assignments", h.ListAssignmentsForWeekHandler)
		s.GET("/assignments/:id", h.GetAssignmentByIDHandler)
		s.PUT("/assignments/:id", h.UpdateAssignmentByIDHandler)
		s.DELETE("/assignments/:id", h.DeleteAssignmentByIDHandler)

		s.GET("/assignments-with-submission", h.ListAssignmentsWithSubmissionForThread)

		// Comprehensive assignment details for students
		s.GET("/assignments/:id/student/:student_id", h.GetAssignmentDetailsForStudent)
	}
}
