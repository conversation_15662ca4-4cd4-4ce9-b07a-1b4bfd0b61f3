package semester

import (
	"context"
	"errors"
	semesterpb "github.com/olzzhas/edunite-server/course_service/pb/semester"
	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/olzzhas/edunite-server/course_service/internal/database"

	"google.golang.org/protobuf/types/known/timestamppb"
)

type Service struct {
	repo database.SemesterRepository
	semesterpb.UnimplementedSemesterServiceServer
}

func NewSemesterService(repo database.SemesterRepository) *Service {
	return &Service{repo: repo}
}

// CreateSemester
func (s *Service) CreateSemester(ctx context.Context, req *semesterpb.SemesterRequest) (*semesterpb.SemesterResponse, error) {
	sem := &database.Semester{
		Name:      req.GetName(),
		StartDate: req.GetStartDate().AsTime(),
		EndDate:   req.GetEndDate().AsTime(),
	}

	v := validator.New()
	database.ValidateSemester(v, sem)
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	err := s.repo.CreateSemester(ctx, sem)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not create semester: %v", err)
	}
	return semesterToPB(sem), nil
}

// GetSemesterByID
func (s *Service) GetSemesterByID(ctx context.Context, req *semesterpb.SemesterByID) (*semesterpb.SemesterResponse, error) {
	sem, err := s.repo.GetSemester(ctx, req.GetId())
	if err != nil {
		if errors.Is(err, database.ErrSemesterNotFound) {
			return nil, status.Errorf(codes.NotFound, err.Error())
		}
		return nil, status.Errorf(codes.Internal, "error fetching semester: %v", err)
	}
	return semesterToPB(sem), nil
}

// GetAllSemesters
func (s *Service) GetAllSemesters(ctx context.Context, req *semesterpb.SemesterEmptyRequest) (*semesterpb.SemestersResponse, error) {
	list, err := s.repo.GetAllSemesters(ctx)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not list semesters: %v", err)
	}
	rep := make([]*semesterpb.SemesterResponse, len(list))
	for i, sem := range list {
		rep[i] = &semesterpb.SemesterResponse{
			Id:        sem.ID,
			Name:      sem.Name,
			StartDate: timestamppb.New(sem.StartDate),
			EndDate:   timestamppb.New(sem.EndDate),
			CreatedAt: timestamppb.New(sem.CreatedAt),
			UpdatedAt: timestamppb.New(sem.UpdatedAt),
		}
	}
	return &semesterpb.SemestersResponse{Semesters: rep}, nil
}

// UpdateSemester
func (s *Service) UpdateSemester(ctx context.Context, req *semesterpb.SemesterUpdateRequest) (*semesterpb.SemesterResponse, error) {
	existing, err := s.repo.GetSemester(ctx, req.GetId())
	if err != nil {
		if errors.Is(err, database.ErrSemesterNotFound) {
			return nil, status.Errorf(codes.NotFound, err.Error())
		}
		return nil, status.Errorf(codes.Internal, "error fetching semester: %v", err)
	}
	existing.Name = req.GetName()
	existing.StartDate = req.GetStartDate().AsTime()
	existing.EndDate = req.GetEndDate().AsTime()

	v := validator.New()
	database.ValidateSemester(v, existing)
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	if err := s.repo.UpdateSemester(ctx, existing); err != nil {
		return nil, status.Errorf(codes.Internal, "could not update semester: %v", err)
	}
	return semesterToPB(existing), nil
}

// DeleteSemester
func (s *Service) Delete(ctx context.Context, req *semesterpb.SemesterByID) (*semesterpb.SemesterEmptyResponse, error) {
	if err := s.repo.DeleteSemester(ctx, req.GetId()); err != nil {
		if errors.Is(err, database.ErrSemesterNotFound) {
			return nil, status.Errorf(codes.NotFound, err.Error())
		}
		return nil, status.Errorf(codes.Internal, "could not delete semester: %v", err)
	}
	return &semesterpb.SemesterEmptyResponse{}, nil
}

// AddSemesterBreak
func (s *Service) AddSemesterBreak(ctx context.Context, req *semesterpb.SemesterBreakRequest) (*semesterpb.SemesterBreakResponse, error) {
	br := &database.SemesterBreak{
		SemesterID:  req.GetSemesterId(),
		BreakDate:   req.GetBreakDate().AsTime(),
		Description: req.GetDescription(),
	}
	v := validator.New()
	database.ValidateSemesterBreak(v, br)
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}
	if err := s.repo.AddSemesterBreak(ctx, br); err != nil {
		switch {
		case errors.Is(err, database.ErrSemesterBreakConflict):
			return nil, status.Errorf(codes.AlreadyExists, err.Error())
		default:
			return nil, status.Errorf(codes.Internal, "could not add break: %v", err)
		}
	}
	return semesterBreakToPB(br), nil
}

// RemoveSemesterBreak
func (s *Service) RemoveSemesterBreak(ctx context.Context, req *semesterpb.SemesterBreakByID) (*semesterpb.SemesterBreakResponse, error) {
	brs, _ := s.repo.ListSemesterBreaks(ctx, req.GetId())
	var found *database.SemesterBreak
	for _, b := range brs {
		if b.ID == req.GetId() {
			found = b
		}
	}
	if err := s.repo.RemoveSemesterBreak(ctx, req.GetId()); err != nil {
		if errors.Is(err, database.ErrSemesterBreakNotFound) {
			return nil, status.Errorf(codes.NotFound, err.Error())
		}
		return nil, status.Errorf(codes.Internal, "could not remove break: %v", err)
	}
	return semesterBreakToPB(found), nil
}

// ListSemesterBreaks
func (s *Service) ListSemesterBreaks(ctx context.Context, req *semesterpb.SemesterByID) (*semesterpb.SemesterBreaksResponse, error) {
	list, err := s.repo.ListSemesterBreaks(ctx, req.GetId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not list breaks: %v", err)
	}
	rep := make([]*semesterpb.SemesterBreakResponse, len(list))
	for i, b := range list {
		rep[i] = &semesterpb.SemesterBreakResponse{
			Id:          b.ID,
			SemesterId:  b.SemesterID,
			BreakDate:   timestamppb.New(b.BreakDate),
			Description: b.Description,
			CreatedAt:   timestamppb.New(b.CreatedAt),
			UpdatedAt:   timestamppb.New(b.UpdatedAt),
		}
	}
	return &semesterpb.SemesterBreaksResponse{SemesterBreaks: rep}, nil
}

// -------------------------
// Вспомогательные функции
// -------------------------
func semesterToPB(s *database.Semester) *semesterpb.SemesterResponse {
	return &semesterpb.SemesterResponse{
		Id:        s.ID,
		Name:      s.Name,
		StartDate: timestamppb.New(s.StartDate),
		EndDate:   timestamppb.New(s.EndDate),
		CreatedAt: timestamppb.New(s.CreatedAt),
		UpdatedAt: timestamppb.New(s.UpdatedAt),
	}
}

func semesterBreakToPB(b *database.SemesterBreak) *semesterpb.SemesterBreakResponse {
	return &semesterpb.SemesterBreakResponse{
		Id:          b.ID,
		SemesterId:  b.SemesterID,
		BreakDate:   timestamppb.New(b.BreakDate),
		Description: b.Description,
		CreatedAt:   timestamppb.New(b.CreatedAt),
		UpdatedAt:   timestamppb.New(b.UpdatedAt),
	}
}
