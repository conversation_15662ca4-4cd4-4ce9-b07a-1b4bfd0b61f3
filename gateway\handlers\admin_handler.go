package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
)

// AdminHandler handles admin-related requests
type AdminHandler struct {
	userClient *clients.UserClient
}

// NewAdminHandler creates a new AdminHandler
func NewAdminHandler(userClient *clients.UserClient) *AdminHandler {
	return &AdminHandler{
		userClient: userClient,
	}
}

// GetAllUsersHandler handles GET /admin/users
func (h *AdminHandler) GetAllUsersHandler(c *gin.Context) {
	// Parse query parameters
	page, _ := strconv.Atoi(c.Default<PERSON>uery("page", "1"))
	pageSize, _ := strconv.Atoi(c.Default<PERSON>uery("page_size", "10"))
	sort := c.DefaultQuery("sort", "id")
	search := c.<PERSON>ult<PERSON>("search", "")
	role := c.<PERSON><PERSON>ult<PERSON><PERSON><PERSON>("role", "")

	// Get users from user service
	filters := clients.ListFilters{
		Page:     int32(page),
		PageSize: int32(pageSize),
		Sort:     sort,
		Search:   search,
		Role:     role,
	}

	users, err := h.userClient.GetAllUsers(c.Request.Context(), filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get users"})
		return
	}

	c.JSON(http.StatusOK, users)
}

// UpdateUserHandler handles PUT /admin/users/:id
func (h *AdminHandler) UpdateUserHandler(c *gin.Context) {
	// Parse user ID
	userID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Parse request body
	var req struct {
		Name    string `json:"name"`
		Surname string `json:"surname"`
		Email   string `json:"email"`
		Role    string `json:"role"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Update user
	user, err := h.userClient.UpdateUser(c.Request.Context(), userID, req.Name, req.Surname, req.Email, req.Role)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// DeleteUserHandler handles DELETE /admin/users/:id
func (h *AdminHandler) DeleteUserHandler(c *gin.Context) {
	// This is a stub - implement actual user deletion logic
	c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}
