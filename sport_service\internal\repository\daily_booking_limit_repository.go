package repository

import (
	"context"
	
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

// DailyBookingLimitRepository defines the interface for daily booking limit data access
type DailyBookingLimitRepository interface {
	// Create creates a new daily booking limit
	Create(ctx context.Context, limit *domain.DailyBookingLimit) error
	
	// GetByID retrieves a daily booking limit by ID
	GetByID(ctx context.Context, id int64) (*domain.DailyBookingLimit, error)
	
	// GetBySemesterID retrieves a daily booking limit by semester ID
	GetBySemesterID(ctx context.Context, semesterID int64) (*domain.DailyBookingLimit, error)
	
	// Update updates an existing daily booking limit
	Update(ctx context.Context, limit *domain.DailyBookingLimit) error
	
	// Delete deletes a daily booking limit by ID
	Delete(ctx context.Context, id int64) error
	
	// List retrieves all daily booking limits
	List(ctx context.Context, page, pageSize int) ([]*domain.DailyBookingLimit, error)
	
	// Count counts all daily booking limits
	Count(ctx context.Context) (int, error)
}
