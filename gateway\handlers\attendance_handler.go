package handlers

import (
	"fmt"
	attendancepb "github.com/olzzhas/edunite-server/course_service/pb/attendance"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
)

// AttendanceHandler handles HTTP requests for attendance
// Register routes with something like:
//
//	r.POST("/attendance", handler.CreateAttendanceHandler)
//	r.GET("/attendance", handler.ListAttendanceHandler)
//	r.PUT("/attendance/:id", handler.UpdateAttendanceHandler)
//	r.DELETE("/attendance/:id", handler.DeleteAttendanceHandler)
type AttendanceHandler struct {
	AttendanceService  *clients.AttendanceClient
	RabbitLogPublisher clients.LogPublisher
}

// CreateAttendanceHandler creates a new attendance record
func (h *AttendanceHandler) CreateAttendanceHandler(c *gin.Context) {
	var body struct {
		ThreadID       int64                         `json:"thread_id" binding:"required"`
		UserID         int64                         `json:"user_id" binding:"required"`
		AttendanceDate string                        `json:"attendance_date" binding:"required"` // YYYY-MM-DD
		Status         attendancepb.AttendanceStatus `json:"status" binding:"required"`
		Reason         string                        `json:"reason"`
	}
	if err := c.ShouldBindJSON(&body); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// Parse date
	date, err := time.Parse("2006-01-02", body.AttendanceDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid date format; use YYYY-MM-DD"})
		return
	}

	// Call gRPC client
	resp, err := h.AttendanceService.CreateAttendance(c.Request.Context(), clients.CreateAttendanceParams{
		ThreadID:       body.ThreadID,
		UserID:         body.UserID,
		AttendanceDate: date,
		Status:         body.Status,
		Reason:         body.Reason,
	})
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", fmt.Sprintf("CreateAttendance error: %v", err), "attendance", map[string]any{
			"thread_id": body.ThreadID,
			"user_id":   body.UserID,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create attendance"})
		return
	}
	c.JSON(http.StatusCreated, resp)
}

// ListAttendanceHandler lists attendance records by thread and date
func (h *AttendanceHandler) ListAttendanceHandler(c *gin.Context) {
	threadIDStr := c.Query("thread_id")
	dateStr := c.Query("attendance_date")
	if threadIDStr == "" || dateStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "thread_id and attendance_date query params required"})
		return
	}

	threadID, err := strconv.ParseInt(threadIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid thread_id"})
		return
	}

	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid date format; use YYYY-MM-DD"})
		return
	}

	records, err := h.AttendanceService.ListAttendance(c.Request.Context(), threadID, date)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", fmt.Sprintf("ListAttendance error: %v", err), "attendance", map[string]any{
			"thread_id":       threadID,
			"attendance_date": dateStr,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list attendance"})
		return
	}
	c.JSON(http.StatusOK, records)
}

// UpdateAttendanceHandler updates an attendance record's status or reason
func (h *AttendanceHandler) UpdateAttendanceHandler(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid id"})
		return
	}

	var body struct {
		Status attendancepb.AttendanceStatus `json:"status" binding:"required"`
		Reason string                        `json:"reason"`
	}
	if err := c.ShouldBindJSON(&body); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	resp, err := h.AttendanceService.UpdateAttendance(c.Request.Context(), id, body.Status, body.Reason)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", fmt.Sprintf("UpdateAttendance error: %v", err), "attendance", map[string]any{"id": id})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update attendance"})
		return
	}
	c.JSON(http.StatusOK, resp)
}

// DeleteAttendanceHandler deletes an attendance record by ID
func (h *AttendanceHandler) DeleteAttendanceHandler(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid id"})
		return
	}

	err = h.AttendanceService.DeleteAttendanceByID(c.Request.Context(), id)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", fmt.Sprintf("DeleteAttendance error: %v", err), "attendance", map[string]any{"id": id})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete attendance"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "attendance deleted"})
}

// DeleteAttendancesByThreadUserHandler удаляет все attendance по потоку и пользователю.
func (h *ThreadHandler) DeleteAttendancesByThreadUserHandler(c *gin.Context) {
	// thread_id берём из :id, user_id из :user_id
	threadID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid thread id"})
		return
	}
	userID, err := strconv.ParseInt(c.Param("user_id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid user id"})
		return
	}

	// вызываем gRPC
	if err := h.AttendanceService.DeleteAttendancesByThreadUser(
		c.Request.Context(),
		threadID, userID,
	); err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("DeleteAttendancesByThreadUser error: %v", err),
			"attendance_cleanup",
			map[string]any{"thread_id": threadID, "user_id": userID},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete attendance"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "attendance records cleaned up"})
}
