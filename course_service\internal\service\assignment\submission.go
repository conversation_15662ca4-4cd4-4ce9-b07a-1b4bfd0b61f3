package assignment

import (
	"context"
	"errors"
	"strings"
	assignmentpb "github.com/olzzhas/edunite-server/course_service/pb/assignment"
	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/olzzhas/edunite-server/course_service/internal/database"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// CreateAssignmentSubmission creates or updates a submission for an assignment
func (s *Service) CreateAssignmentSubmission(ctx context.Context, req *assignmentpb.CreateAssignmentSubmissionRequest) (*assignmentpb.AssignmentSubmissionResponse, error) {
	v := validator.New()
	v.Check(req.GetAssignmentId() > 0, "assignment_id", "must be > 0")
	v.Check(req.GetUserId() > 0, "user_id", "must be > 0")
	v.Check(len(req.GetComment()) <= 1000, "comment", "max length is 1000")
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	// Check if a submission already exists for this user and assignment
	existingSubmissions, err := s.submissionRepo.ListSubmissionsByAssignmentID(ctx, req.GetAssignmentId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not check existing submissions: %v", err)
	}

	var existingSubmission *database.AssignmentSubmission
	for _, sub := range existingSubmissions {
		if sub.UserID == req.GetUserId() {
			existingSubmission = sub
			break
		}
	}

	if existingSubmission != nil {
		// Update existing submission
		existingSubmission.FileURLs = req.GetFileUrls()
		existingSubmission.Comment = req.GetComment()

		// We need to implement an update method in the repository
		if err := s.submissionRepo.UpdateSubmission(ctx, existingSubmission); err != nil {
			return nil, status.Errorf(codes.Internal, "could not update submission: %v", err)
		}

		return submissionToPB(existingSubmission), nil
	}

	// Create new submission
	sub := &database.AssignmentSubmission{
		AssignmentID: req.GetAssignmentId(),
		UserID:       req.GetUserId(),
		FileURLs:     req.GetFileUrls(),
		Comment:      req.GetComment(),
	}

	if err := s.submissionRepo.CreateSubmission(ctx, sub); err != nil {
		// Check if the error is due to unique constraint violation
		if strings.Contains(err.Error(), "unique_user_assignment_submission") {
			return nil, status.Errorf(codes.AlreadyExists, "user already has a submission for this assignment")
		}
		return nil, status.Errorf(codes.Internal, "could not create submission: %v", err)
	}

	return submissionToPB(sub), nil
}

// GetAssignmentSubmissionByID
func (s *Service) GetAssignmentSubmissionByID(ctx context.Context, req *assignmentpb.AssignmentSubmissionByIDRequest) (*assignmentpb.AssignmentSubmissionResponse, error) {
	if req.GetSubmissionId() <= 0 {
		return nil, status.Errorf(codes.InvalidArgument, "submission_id must be > 0")
	}
	sub, err := s.submissionRepo.GetSubmission(ctx, req.GetSubmissionId())
	if err != nil {
		if errors.Is(err, database.ErrSubmissionNotFound) {
			return nil, status.Errorf(codes.NotFound, "submission not found")
		}
		return nil, status.Errorf(codes.Internal, "could not fetch submission: %v", err)
	}
	return submissionToPB(sub), nil
}

// ListAssignmentSubmissionsByAssignmentID
func (s *Service) ListAssignmentSubmissionsByAssignmentID(ctx context.Context, req *assignmentpb.AssignmentIDRequest) (*assignmentpb.AssignmentSubmissionsResponse, error) {
	if req.GetAssignmentId() <= 0 {
		return nil, status.Errorf(codes.InvalidArgument, "assignment_id must be > 0")
	}
	list, err := s.submissionRepo.ListSubmissionsByAssignmentID(ctx, req.GetAssignmentId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not list submissions: %v", err)
	}
	resp := &assignmentpb.AssignmentSubmissionsResponse{}
	for _, sub := range list {
		resp.Submissions = append(resp.Submissions, submissionToPB(sub))
	}
	return resp, nil
}

// UpdateAssignmentSubmissionScore
func (s *Service) UpdateAssignmentSubmissionScore(ctx context.Context, req *assignmentpb.UpdateAssignmentSubmissionScoreRequest) (*assignmentpb.AssignmentSubmissionResponse, error) {
	v := validator.New()
	v.Check(req.GetSubmissionId() > 0, "submission_id", "must be > 0")
	v.Check(req.GetScore() >= 0, "score", "must be >= 0")
	v.Check(len(req.GetFeedback()) <= 1000, "feedback", "max length is 1000")
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	updated, err := s.submissionRepo.UpdateSubmissionScore(ctx, req.GetSubmissionId(), req.GetScore(), req.GetFeedback())
	if err != nil {
		if errors.Is(err, database.ErrSubmissionNotFound) {
			return nil, status.Errorf(codes.NotFound, "submission not found")
		}
		return nil, status.Errorf(codes.Internal, "could not update score: %v", err)
	}

	return submissionToPB(updated), nil
}

// DeleteAssignmentSubmissionByID
func (s *Service) DeleteAssignmentSubmissionByID(ctx context.Context, req *assignmentpb.AssignmentSubmissionByIDRequest) (*assignmentpb.AssignmentEmptyResponse, error) {
	if req.GetSubmissionId() <= 0 {
		return nil, status.Errorf(codes.InvalidArgument, "submission_id must be > 0")
	}
	if err := s.submissionRepo.DeleteSubmission(ctx, req.GetSubmissionId()); err != nil {
		if errors.Is(err, database.ErrSubmissionNotFound) {
			return nil, status.Errorf(codes.NotFound, "submission not found")
		}
		return nil, status.Errorf(codes.Internal, "could not delete submission: %v", err)
	}
	return &assignmentpb.AssignmentEmptyResponse{}, nil
}

// ---------------------
// Вспомогательная функция
// ---------------------
func submissionToPB(sub *database.AssignmentSubmission) *assignmentpb.AssignmentSubmissionResponse {
	resp := &assignmentpb.AssignmentSubmissionResponse{
		Id:           sub.ID,
		AssignmentId: sub.AssignmentID,
		UserId:       sub.UserID,
		SubmittedAt:  timestamppb.New(sub.SubmittedAt),
		FileUrls:     sub.FileURLs,
		Comment:      sub.Comment,
		CreatedAt:    timestamppb.New(sub.CreatedAt),
		UpdatedAt:    timestamppb.New(sub.UpdatedAt),
	}

	// Handle nullable fields
	if sub.Score != nil {
		resp.Score = *sub.Score
	}

	if sub.Feedback != nil {
		resp.Feedback = *sub.Feedback
	}

	return resp
}
