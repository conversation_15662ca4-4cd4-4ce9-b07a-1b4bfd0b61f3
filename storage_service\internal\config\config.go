package config

import (
	"os"
	"strconv"
)

type Config struct {
	// Minio config (legacy)
	MinioEndpoint       string
	MinioAccessKey      string
	MinioSecretKey      string
	MinioUseSSL         bool
	PublicMinioEndpoint string

	// AWS S3 config
	AWSAccessKey string
	AWSSecretKey string
	AWSRegion    string

	// General config
	Environment string
	StorageType string // "minio", "s3", or "local"
}

func LoadConfig() *Config {
	useSSL := false
	if val, ok := os.LookupEnv("MINIO_USE_SSL"); ok {
		parsed, err := strconv.ParseBool(val)
		if err == nil {
			useSSL = parsed
		}
	}

	// Get environment (default to "development")
	environment := os.Getenv("ENVIRONMENT")
	if environment == "" {
		environment = "development"
	}

	// Get public MinIO endpoint (default to the internal endpoint)
	publicEndpoint := os.Getenv("PUBLIC_MINIO_ENDPOINT")
	if publicEndpoint == "" {
		publicEndpoint = os.Getenv("MINIO_ENDPOINT")
	}

	// Determine storage type (default to "s3")
	storageType := os.Getenv("STORAGE_TYPE")
	if storageType == "" {
		storageType = "s3"
	}

	// Get AWS region (default to "us-east-1")
	awsRegion := os.Getenv("AWS_REGION")
	if awsRegion == "" {
		awsRegion = "us-east-1"
	}

	return &Config{
		// Minio config
		MinioEndpoint:       os.Getenv("MINIO_ENDPOINT"),
		MinioAccessKey:      os.Getenv("MINIO_ACCESS_KEY"),
		MinioSecretKey:      os.Getenv("MINIO_SECRET_KEY"),
		MinioUseSSL:         useSSL,
		PublicMinioEndpoint: publicEndpoint,

		// AWS S3 config
		AWSAccessKey: os.Getenv("AWS_ACCESS_KEY"),
		AWSSecretKey: os.Getenv("AWS_SECRET_KEY"),
		AWSRegion:    awsRegion,

		// General config
		Environment: environment,
		StorageType: storageType,
	}
}
