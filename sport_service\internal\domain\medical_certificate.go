package domain

import (
	"time"
)

// CertificateStatus represents the status of a medical certificate
type CertificateStatus string

const (
	CertificateStatusPending  CertificateStatus = "pending"
	CertificateStatusApproved CertificateStatus = "approved"
	CertificateStatusRejected CertificateStatus = "rejected"
)

// MedicalCertificate represents a medical certificate for a user
type MedicalCertificate struct {
	ID           int64             `json:"id"`
	UserID       int64             `json:"user_id"`
	FileURL      string            `json:"file_url"`
	Status       CertificateStatus `json:"status"`
	ReviewedBy   int64             `json:"reviewed_by"`
	RejectReason string            `json:"reject_reason"`
	ValidFrom    time.Time         `json:"valid_from"`
	ValidUntil   time.Time         `json:"valid_until"`
	CreatedAt    time.Time         `json:"created_at"`
	UpdatedAt    time.Time         `json:"updated_at"`
}

// MedicalCertificateFilter represents filters for querying medical certificates
type MedicalCertificateFilter struct {
	UserID     int64
	Status     CertificateStatus
	ValidAt    time.Time
	Page       int
	PageSize   int
}
