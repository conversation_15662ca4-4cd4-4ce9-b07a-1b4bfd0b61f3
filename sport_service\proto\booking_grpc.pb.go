// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: proto/booking.proto

package sportpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	BookingService_CreateBooking_FullMethodName        = "/sportpb.BookingService/CreateBooking"
	BookingService_GetBooking_FullMethodName           = "/sportpb.BookingService/GetBooking"
	BookingService_CancelBooking_FullMethodName        = "/sportpb.BookingService/CancelBooking"
	BookingService_ListUserBookings_FullMethodName     = "/sportpb.BookingService/ListUserBookings"
	BookingService_ListScheduleBookings_FullMethodName = "/sportpb.BookingService/ListScheduleBookings"
	BookingService_GetBookingStats_FullMethodName      = "/sportpb.BookingService/GetBookingStats"
	BookingService_GetUserSemesterStats_FullMethodName = "/sportpb.BookingService/GetUserSemesterStats"
)

// BookingServiceClient is the client API for BookingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BookingServiceClient interface {
	// Create a new booking
	CreateBooking(ctx context.Context, in *CreateBookingRequest, opts ...grpc.CallOption) (*BookingResponse, error)
	// Get a booking by ID
	GetBooking(ctx context.Context, in *GetBookingRequest, opts ...grpc.CallOption) (*BookingResponse, error)
	// Cancel a booking
	CancelBooking(ctx context.Context, in *CancelBookingRequest, opts ...grpc.CallOption) (*BookingResponse, error)
	// List bookings for a user
	ListUserBookings(ctx context.Context, in *ListUserBookingsRequest, opts ...grpc.CallOption) (*ListBookingsResponse, error)
	// List bookings for a schedule
	ListScheduleBookings(ctx context.Context, in *ListScheduleBookingsRequest, opts ...grpc.CallOption) (*ListBookingsResponse, error)
	// Get booking statistics
	GetBookingStats(ctx context.Context, in *GetBookingStatsRequest, opts ...grpc.CallOption) (*BookingStatsResponse, error)
	// Get user semester statistics
	GetUserSemesterStats(ctx context.Context, in *GetUserSemesterStatsRequest, opts ...grpc.CallOption) (*UserSemesterStatsResponse, error)
}

type bookingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBookingServiceClient(cc grpc.ClientConnInterface) BookingServiceClient {
	return &bookingServiceClient{cc}
}

func (c *bookingServiceClient) CreateBooking(ctx context.Context, in *CreateBookingRequest, opts ...grpc.CallOption) (*BookingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BookingResponse)
	err := c.cc.Invoke(ctx, BookingService_CreateBooking_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingServiceClient) GetBooking(ctx context.Context, in *GetBookingRequest, opts ...grpc.CallOption) (*BookingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BookingResponse)
	err := c.cc.Invoke(ctx, BookingService_GetBooking_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingServiceClient) CancelBooking(ctx context.Context, in *CancelBookingRequest, opts ...grpc.CallOption) (*BookingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BookingResponse)
	err := c.cc.Invoke(ctx, BookingService_CancelBooking_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingServiceClient) ListUserBookings(ctx context.Context, in *ListUserBookingsRequest, opts ...grpc.CallOption) (*ListBookingsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBookingsResponse)
	err := c.cc.Invoke(ctx, BookingService_ListUserBookings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingServiceClient) ListScheduleBookings(ctx context.Context, in *ListScheduleBookingsRequest, opts ...grpc.CallOption) (*ListBookingsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBookingsResponse)
	err := c.cc.Invoke(ctx, BookingService_ListScheduleBookings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingServiceClient) GetBookingStats(ctx context.Context, in *GetBookingStatsRequest, opts ...grpc.CallOption) (*BookingStatsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BookingStatsResponse)
	err := c.cc.Invoke(ctx, BookingService_GetBookingStats_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookingServiceClient) GetUserSemesterStats(ctx context.Context, in *GetUserSemesterStatsRequest, opts ...grpc.CallOption) (*UserSemesterStatsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserSemesterStatsResponse)
	err := c.cc.Invoke(ctx, BookingService_GetUserSemesterStats_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BookingServiceServer is the server API for BookingService service.
// All implementations must embed UnimplementedBookingServiceServer
// for forward compatibility.
type BookingServiceServer interface {
	// Create a new booking
	CreateBooking(context.Context, *CreateBookingRequest) (*BookingResponse, error)
	// Get a booking by ID
	GetBooking(context.Context, *GetBookingRequest) (*BookingResponse, error)
	// Cancel a booking
	CancelBooking(context.Context, *CancelBookingRequest) (*BookingResponse, error)
	// List bookings for a user
	ListUserBookings(context.Context, *ListUserBookingsRequest) (*ListBookingsResponse, error)
	// List bookings for a schedule
	ListScheduleBookings(context.Context, *ListScheduleBookingsRequest) (*ListBookingsResponse, error)
	// Get booking statistics
	GetBookingStats(context.Context, *GetBookingStatsRequest) (*BookingStatsResponse, error)
	// Get user semester statistics
	GetUserSemesterStats(context.Context, *GetUserSemesterStatsRequest) (*UserSemesterStatsResponse, error)
	mustEmbedUnimplementedBookingServiceServer()
}

// UnimplementedBookingServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBookingServiceServer struct{}

func (UnimplementedBookingServiceServer) CreateBooking(context.Context, *CreateBookingRequest) (*BookingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBooking not implemented")
}
func (UnimplementedBookingServiceServer) GetBooking(context.Context, *GetBookingRequest) (*BookingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBooking not implemented")
}
func (UnimplementedBookingServiceServer) CancelBooking(context.Context, *CancelBookingRequest) (*BookingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelBooking not implemented")
}
func (UnimplementedBookingServiceServer) ListUserBookings(context.Context, *ListUserBookingsRequest) (*ListBookingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserBookings not implemented")
}
func (UnimplementedBookingServiceServer) ListScheduleBookings(context.Context, *ListScheduleBookingsRequest) (*ListBookingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListScheduleBookings not implemented")
}
func (UnimplementedBookingServiceServer) GetBookingStats(context.Context, *GetBookingStatsRequest) (*BookingStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBookingStats not implemented")
}
func (UnimplementedBookingServiceServer) GetUserSemesterStats(context.Context, *GetUserSemesterStatsRequest) (*UserSemesterStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserSemesterStats not implemented")
}
func (UnimplementedBookingServiceServer) mustEmbedUnimplementedBookingServiceServer() {}
func (UnimplementedBookingServiceServer) testEmbeddedByValue()                        {}

// UnsafeBookingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BookingServiceServer will
// result in compilation errors.
type UnsafeBookingServiceServer interface {
	mustEmbedUnimplementedBookingServiceServer()
}

func RegisterBookingServiceServer(s grpc.ServiceRegistrar, srv BookingServiceServer) {
	// If the following call pancis, it indicates UnimplementedBookingServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BookingService_ServiceDesc, srv)
}

func _BookingService_CreateBooking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingServiceServer).CreateBooking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BookingService_CreateBooking_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingServiceServer).CreateBooking(ctx, req.(*CreateBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingService_GetBooking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingServiceServer).GetBooking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BookingService_GetBooking_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingServiceServer).GetBooking(ctx, req.(*GetBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingService_CancelBooking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingServiceServer).CancelBooking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BookingService_CancelBooking_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingServiceServer).CancelBooking(ctx, req.(*CancelBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingService_ListUserBookings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserBookingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingServiceServer).ListUserBookings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BookingService_ListUserBookings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingServiceServer).ListUserBookings(ctx, req.(*ListUserBookingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingService_ListScheduleBookings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListScheduleBookingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingServiceServer).ListScheduleBookings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BookingService_ListScheduleBookings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingServiceServer).ListScheduleBookings(ctx, req.(*ListScheduleBookingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingService_GetBookingStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBookingStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingServiceServer).GetBookingStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BookingService_GetBookingStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingServiceServer).GetBookingStats(ctx, req.(*GetBookingStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BookingService_GetUserSemesterStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSemesterStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingServiceServer).GetUserSemesterStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BookingService_GetUserSemesterStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingServiceServer).GetUserSemesterStats(ctx, req.(*GetUserSemesterStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BookingService_ServiceDesc is the grpc.ServiceDesc for BookingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BookingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sportpb.BookingService",
	HandlerType: (*BookingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateBooking",
			Handler:    _BookingService_CreateBooking_Handler,
		},
		{
			MethodName: "GetBooking",
			Handler:    _BookingService_GetBooking_Handler,
		},
		{
			MethodName: "CancelBooking",
			Handler:    _BookingService_CancelBooking_Handler,
		},
		{
			MethodName: "ListUserBookings",
			Handler:    _BookingService_ListUserBookings_Handler,
		},
		{
			MethodName: "ListScheduleBookings",
			Handler:    _BookingService_ListScheduleBookings_Handler,
		},
		{
			MethodName: "GetBookingStats",
			Handler:    _BookingService_GetBookingStats_Handler,
		},
		{
			MethodName: "GetUserSemesterStats",
			Handler:    _BookingService_GetUserSemesterStats_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/booking.proto",
}
