openapi: 3.0.0
info:
  title: Edunite Storage API
  description: API documentation for the Storage endpoints of the Edunite platform
  version: 1.0.0
  contact:
    name: Edunite Support
    email: <EMAIL>

servers:
  - url: http://localhost:8081
    description: Local development server
  - url: https://api.edunite.com
    description: Production server

tags:
  - name: storage
    description: File storage operations

paths:
  /storage/upload:
    post:
      tags:
        - storage
      summary: Upload a file
      description: Uploads a file to the storage service
      operationId: uploadFile
      parameters:
        - name: bucket
          in: query
          required: true
          description: Name of the bucket to upload the file to
          schema:
            type: string
            example: "assignments"
        - name: object
          in: query
          required: true
          description: Object name/path for the file
          schema:
            type: string
            example: "assignment1/file.pdf"
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file
              properties:
                file:
                  type: string
                  format: binary
                  description: File to upload
      responses:
        '200':
          description: File uploaded successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  url:
                    type: string
                    description: URL of the uploaded file
                    example: "assignments/assignment1/file.pdf"
                  message:
                    type: string
                    example: "File uploaded successfully"
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /storage/download/{bucket}/{object}:
    get:
      tags:
        - storage
      summary: Download a file
      description: Downloads a file from the storage service
      operationId: downloadFile
      parameters:
        - name: bucket
          in: path
          required: true
          description: Name of the bucket containing the file
          schema:
            type: string
            example: "assignments"
        - name: object
          in: path
          required: true
          description: Object name/path of the file
          schema:
            type: string
            example: "assignment1/file.pdf"
      responses:
        '200':
          description: File content
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: File not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /storage/{bucket}/{object}:
    delete:
      tags:
        - storage
      summary: Delete a file
      description: Deletes a file from the storage service
      operationId: deleteFile
      parameters:
        - name: bucket
          in: path
          required: true
          description: Name of the bucket containing the file
          schema:
            type: string
            example: "assignments"
        - name: object
          in: path
          required: true
          description: Object name/path of the file
          schema:
            type: string
            example: "assignment1/file.pdf"
      responses:
        '200':
          description: File deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "File deleted"
        '400':
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: File not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
          example: "File not found"
