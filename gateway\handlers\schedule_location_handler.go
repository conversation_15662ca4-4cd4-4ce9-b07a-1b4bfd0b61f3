package handlers

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
)

// UpdateScheduleLocationHandler updates the location field for a schedule
func (h *ThreadHandler) UpdateScheduleLocationHandler(c *gin.Context) {
	schedIDStr := c.Param("schedule_id")
	schedID, err := strconv.ParseInt(schedIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid schedule_id"})
		return
	}

	// Get the location from the request body
	var req struct {
		Location string `json:"location"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// Call the service method to update the location
	err = h.ThreadService.UpdateScheduleLocation(c.Request.Context(), schedID, req.Location)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", fmt.Sprintf("UpdateScheduleLocation error: %v", err), "thread_schedule", map[string]any{"schedule_id": schedID})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update schedule location"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "schedule location updated successfully"})
}

// GetScheduleLocationHandler gets the location field for a schedule
func (h *ThreadHandler) GetScheduleLocationHandler(c *gin.Context) {
	schedIDStr := c.Param("schedule_id")
	schedID, err := strconv.ParseInt(schedIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid schedule_id"})
		return
	}

	// Call the service method to get the location
	location, err := h.ThreadService.GetScheduleLocation(c.Request.Context(), schedID)
	if err != nil {
		h.RabbitLogPublisher.PublishLog("error", fmt.Sprintf("GetScheduleLocation error: %v", err), "thread_schedule", map[string]any{"schedule_id": schedID})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get schedule location"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"location": location})
}
