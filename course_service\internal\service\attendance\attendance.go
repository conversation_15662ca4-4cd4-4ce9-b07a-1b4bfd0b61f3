package attendance

import (
	"context"
	"errors"
	"fmt"

	"github.com/olzzhas/edunite-server/course_service/internal/database"
	attendancepb "github.com/olzzhas/edunite-server/course_service/pb/attendance"
	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// Service implements gRPC AttendanceServiceServer
// Register with grpcServer via:
// pb.RegisterAttendanceServiceServer(grpcServer, NewService(repo))
type Service struct {
	repo database.AttendanceRepository
	attendancepb.UnimplementedAttendanceServiceServer
}

// NewAttendanceService creates a new attendance Service
func NewAttendanceService(repo database.AttendanceRepository) *Service {
	return &Service{repo: repo}
}

// CreateAttendance adds a new attendance record
func (s *Service) CreateAttendance(ctx context.Context, req *attendancepb.AttendanceRequest) (*attendancepb.AttendanceResponse, error) {
	att := &database.Attendance{
		ThreadID:       req.GetThreadId(),
		UserID:         req.GetUserId(),
		AttendanceDate: req.GetAttendanceDate().AsTime(),
		Status:         mapEnumToString(req.GetStatus()),
		Reason:         req.GetReason(),
	}

	v := validator.New()
	database.ValidateAttendance(v, att, true)
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "%v", v.Errors)
	}

	if err := s.repo.CreateAttendance(ctx, att); err != nil {
		return nil, status.Errorf(codes.Internal, "create: %v", err)
	}
	return toPB(att), nil
}

// ListAttendance returns attendance records for a thread on a specific date
func (s *Service) ListAttendance(ctx context.Context, req *attendancepb.ListAttendanceRequest) (*attendancepb.ListAttendanceResponse, error) {
	if req.GetThreadId() <= 0 {
		return nil, status.Errorf(codes.InvalidArgument, "thread_id must be > 0")
	}
	date := req.GetAttendanceDate().AsTime()
	recs, err := s.repo.ListByThreadAndDate(ctx, req.GetThreadId(), date)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "list: %v", err)
	}

	resp := &attendancepb.ListAttendanceResponse{}
	for _, a := range recs {
		resp.Records = append(resp.Records, toPB(a))
	}
	return resp, nil
}

// UpdateAttendance updates status or reason of an attendance record
func (s *Service) UpdateAttendance(ctx context.Context, req *attendancepb.UpdateAttendanceRequest) (*attendancepb.AttendanceResponse, error) {
	att, err := s.repo.GetAttendance(ctx, req.GetId())
	if err != nil {
		if errors.Is(err, database.ErrAttendanceNotFound) {
			return nil, status.Errorf(codes.NotFound, "attendance not found")
		}
		return nil, status.Errorf(codes.Internal, "fetch: %v", err)
	}
	att.Status = mapEnumToString(req.GetStatus())
	att.Reason = req.GetReason()

	v := validator.New()
	database.ValidateAttendance(v, att, false)
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "%v", v.Errors)
	}

	if err := s.repo.UpdateAttendance(ctx, att); err != nil {
		if errors.Is(err, database.ErrAttendanceNotFound) {
			return nil, status.Errorf(codes.NotFound, "attendance not found")
		}
		return nil, status.Errorf(codes.Internal, "update: %v", err)
	}
	return toPB(att), nil
}

// DeleteAttendanceByID deletes an attendance record
func (s *Service) DeleteAttendanceByID(ctx context.Context, req *attendancepb.AttendanceIDRequest) (*attendancepb.AttendanceEmptyResponse, error) {
	if err := s.repo.DeleteAttendance(ctx, req.GetId()); err != nil {
		if errors.Is(err, database.ErrAttendanceNotFound) {
			return nil, status.Errorf(codes.NotFound, "attendance not found")
		}
		return nil, status.Errorf(codes.Internal, "delete: %v", err)
	}
	return &attendancepb.AttendanceEmptyResponse{}, nil
}

func (s *Service) DeleteAttendancesByThreadUser(ctx context.Context, req *attendancepb.AttendancesByThreadUser) (*attendancepb.AttendanceEmptyResponse, error) {
	if req.GetThreadId() <= 0 || req.GetUserId() <= 0 {
		return nil, status.Errorf(codes.InvalidArgument, "thread_id and user_id must be > 0")
	}
	if err := s.repo.DeleteByThreadAndUser(ctx, req.GetThreadId(), req.GetUserId()); err != nil {
		return nil, status.Errorf(codes.Internal, "delete: %v", err)
	}
	return &attendancepb.AttendanceEmptyResponse{}, nil
}

// TODO
// SeedAttendance — вспомогательный метод (не декларирован в protobuf).
// Может вызываться из Thread‑service при массовой регистрации студентов.
func (s *Service) SeedAttendance(ctx context.Context, list []*database.Attendance) error {
	// простая валидация
	for _, a := range list {
		v := validator.New()
		database.ValidateAttendance(v, a, true)
		if !v.Valid() {
			return fmt.Errorf("invalid attendance seed record: %v", v.Errors)
		}
	}
	return s.repo.BulkCreateAttendance(ctx, list)
}

// -------------------- helpers --------------------------------

func mapEnumToString(e attendancepb.AttendanceStatus) string {
	switch e {
	case attendancepb.AttendanceStatus_ATTENDANCE_STATUS_UNMARKED:
		return database.StatusUnmarked
	case attendancepb.AttendanceStatus_ATTENDANCE_STATUS_PRESENT:
		return database.StatusPresent
	case attendancepb.AttendanceStatus_ATTENDANCE_STATUS_ABSENT:
		return database.StatusAbsent
	case attendancepb.AttendanceStatus_ATTENDANCE_STATUS_EXCUSED:
		return database.StatusExcused
	default:
		return database.StatusUnmarked
	}
}

func mapStringToEnum(s string) attendancepb.AttendanceStatus {
	switch s {
	case database.StatusUnmarked:
		return attendancepb.AttendanceStatus_ATTENDANCE_STATUS_UNMARKED // This is 1
	case database.StatusPresent:
		return attendancepb.AttendanceStatus_ATTENDANCE_STATUS_PRESENT // This is 2
	case database.StatusAbsent:
		return attendancepb.AttendanceStatus_ATTENDANCE_STATUS_ABSENT // This is 3
	case database.StatusExcused:
		return attendancepb.AttendanceStatus_ATTENDANCE_STATUS_EXCUSED // This is 4
	default:
		return attendancepb.AttendanceStatus_ATTENDANCE_STATUS_UNSPECIFIED // This is 0
	}
}

func toPB(a *database.Attendance) *attendancepb.AttendanceResponse {
	return &attendancepb.AttendanceResponse{
		Id:             a.ID,
		ThreadId:       a.ThreadID,
		UserId:         a.UserID,
		AttendanceDate: timestamppb.New(a.AttendanceDate),
		Status:         mapStringToEnum(a.Status),
		Reason:         a.Reason,
		CreatedAt:      timestamppb.New(a.CreatedAt),
		UpdatedAt:      timestamppb.New(a.UpdatedAt),
	}
}
