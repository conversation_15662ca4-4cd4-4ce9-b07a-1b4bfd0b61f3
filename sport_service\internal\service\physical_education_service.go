package service

import (
	"context"
	"time"

	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
	"github.com/olzzhas/edunite-server/sport_service/internal/repository"
)

// PhysicalEducationService is the main service for the Physical Education course
type PhysicalEducationService struct {
	bookingService            *BookingService
	scheduleService           *ScheduleService
	medicalCertificateService *MedicalCertificateService
	semesterSportLimitService *SemesterSportLimitService
	dailyBookingLimitService  *DailyBookingLimitService
	sportTypeRepo             repository.SportTypeRepository
	facilityRepo              repository.FacilityRepository
}

// NewPhysicalEducationService creates a new Physical Education service
func NewPhysicalEducationService(
	bookingService *BookingService,
	scheduleService *ScheduleService,
	medicalCertificateService *MedicalCertificateService,
	semesterSportLimitService *SemesterSportLimitService,
	dailyBookingLimitService *DailyBookingLimitService,
	sportTypeRepo repository.SportTypeRepository,
	facilityRepo repository.FacilityRepository,
) *PhysicalEducationService {
	return &PhysicalEducationService{
		bookingService:            bookingService,
		scheduleService:           scheduleService,
		medicalCertificateService: medicalCertificateService,
		semesterSportLimitService: semesterSportLimitService,
		dailyBookingLimitService:  dailyBookingLimitService,
		sportTypeRepo:             sportTypeRepo,
		facilityRepo:              facilityRepo,
	}
}

// GetAvailableSportTypes retrieves all available sport types
func (s *PhysicalEducationService) GetAvailableSportTypes(ctx context.Context) ([]*domain.SportType, error) {
	filter := domain.SportTypeFilter{
		Page:     1,
		PageSize: 100, // Assuming there won't be more than 100 sport types
	}
	return s.sportTypeRepo.List(ctx, filter)
}

// GetAvailableFacilities retrieves all available facilities
func (s *PhysicalEducationService) GetAvailableFacilities(ctx context.Context) ([]*domain.Facility, error) {
	filter := domain.FacilityFilter{
		Page:     1,
		PageSize: 100, // Assuming there won't be more than 100 facilities
	}
	return s.facilityRepo.List(ctx, filter)
}

// GetAvailableSchedules retrieves available schedules for a facility
func (s *PhysicalEducationService) GetAvailableSchedules(ctx context.Context, facilityID int64, startDate, endDate time.Time) ([]*domain.Schedule, error) {
	filter := domain.ScheduleFilter{
		FacilityID: facilityID,
		StartDate:  startDate,
		EndDate:    endDate,
		Page:       1,
		PageSize:   1000, // Assuming there won't be more than 1000 schedules in the date range
	}
	schedules, _, err := s.scheduleService.ListSchedules(ctx, filter)
	return schedules, err
}

// BookSession books a session for a user
func (s *PhysicalEducationService) BookSession(ctx context.Context, userID, scheduleID int64) (*domain.Booking, error) {
	return s.bookingService.CreateBooking(ctx, userID, scheduleID)
}

// CancelBooking cancels a booking
func (s *PhysicalEducationService) CancelBooking(ctx context.Context, bookingID, userID int64) error {
	return s.bookingService.CancelBooking(ctx, bookingID, userID)
}

// UploadMedicalCertificate uploads a medical certificate for a user
func (s *PhysicalEducationService) UploadMedicalCertificate(ctx context.Context, userID int64, fileURL string, validFrom, validUntil time.Time) (*domain.MedicalCertificate, error) {
	return s.medicalCertificateService.UploadCertificate(ctx, userID, fileURL, validFrom, validUntil)
}

// GetUserBookings retrieves a user's bookings
func (s *PhysicalEducationService) GetUserBookings(ctx context.Context, userID int64, startDate, endDate time.Time) ([]*domain.Booking, error) {
	filter := domain.BookingFilter{
		UserID:    userID,
		StartDate: startDate,
		EndDate:   endDate,
		Page:      1,
		PageSize:  1000, // Assuming there won't be more than 1000 bookings in the date range
	}
	bookings, _, err := s.bookingService.GetUserBookings(ctx, userID, filter)
	return bookings, err
}

// GetUserSemesterStats retrieves a user's statistics for a semester
func (s *PhysicalEducationService) GetUserSemesterStats(ctx context.Context, userID, semesterID int64) (*domain.UserSemesterStats, error) {
	return s.bookingService.GetUserSemesterStats(ctx, userID, semesterID)
}

// CheckUserCanBookLFK checks if a user can book an LFK session
func (s *PhysicalEducationService) CheckUserCanBookLFK(ctx context.Context, userID int64) (bool, error) {
	// Check if the user has a valid medical certificate
	return s.medicalCertificateService.HasValidCertificate(ctx, userID)
}

// GetSemesterSportLimit retrieves the sport limit for a semester
func (s *PhysicalEducationService) GetSemesterSportLimit(ctx context.Context, semesterID int64) (*domain.SemesterSportLimit, error) {
	return s.semesterSportLimitService.GetSemesterSportLimitBySemesterID(ctx, semesterID)
}

// GetDailyBookingLimit retrieves the daily booking limit for a semester
func (s *PhysicalEducationService) GetDailyBookingLimit(ctx context.Context, semesterID int64) (*domain.DailyBookingLimit, error) {
	return s.dailyBookingLimitService.GetDailyBookingLimitBySemesterID(ctx, semesterID)
}

func (s *PhysicalEducationService) GetFacility(ctx context.Context, facilityID int64) (*domain.Facility, error) {
	return s.facilityRepo.GetByID(ctx, facilityID)
}

func (s *PhysicalEducationService) UpdateFacility(ctx context.Context, facility *domain.Facility) error {
	return s.facilityRepo.Update(ctx, facility)
}

func (s *PhysicalEducationService) CreateFacility(ctx context.Context, facility *domain.Facility) error {
	return s.facilityRepo.Create(ctx, facility)
}

func (s *PhysicalEducationService) DeleteFacility(ctx context.Context, facilityID int64) error {
	return s.facilityRepo.Delete(ctx, facilityID)
}

func (s *PhysicalEducationService) ListFacilities(ctx context.Context, filter domain.FacilityFilter) ([]*domain.Facility, error) {
	return s.facilityRepo.List(ctx, filter)
}

func (s *PhysicalEducationService) GetSportType(ctx context.Context, sportTypeID int64) (*domain.SportType, error) {
	return s.sportTypeRepo.GetByID(ctx, sportTypeID)
}

func (s *PhysicalEducationService) UpdateSportType(ctx context.Context, sportType *domain.SportType) error {
	return s.sportTypeRepo.Update(ctx, sportType)
}

func (s *PhysicalEducationService) CreateSportType(ctx context.Context, sportType *domain.SportType) error {
	return s.sportTypeRepo.Create(ctx, sportType)
}

func (s *PhysicalEducationService) DeleteSportType(ctx context.Context, sportTypeID int64) error {
	return s.sportTypeRepo.Delete(ctx, sportTypeID)
}

func (s *PhysicalEducationService) ListSportTypes(ctx context.Context, filter domain.SportTypeFilter) ([]*domain.SportType, error) {
	return s.sportTypeRepo.List(ctx, filter)
}

func (s *PhysicalEducationService) AssignTeacherToSportType(ctx context.Context, teacherSportType *domain.TeacherSportType) error {
	return s.sportTypeRepo.AssignTeacherToSportType(ctx, teacherSportType)
}

func (s *PhysicalEducationService) RemoveTeacherFromSportType(ctx context.Context, teacherID, sportTypeID int64) error {
	return s.sportTypeRepo.RemoveTeacherFromSportType(ctx, teacherID, sportTypeID)
}

func (s *PhysicalEducationService) ListTeachersForSportType(ctx context.Context, sportTypeID int64) ([]int64, error) {
	return s.sportTypeRepo.ListTeachersForSportType(ctx, sportTypeID)
}

func (s *PhysicalEducationService) ListSportTypesForTeacher(ctx context.Context, teacherID int64) ([]*domain.SportType, error) {
	return s.sportTypeRepo.ListSportTypesForTeacher(ctx, teacherID)
}
