-- Create daily booking limits table
CREATE TABLE IF NOT EXISTS daily_booking_limits (
    id BIGSERIAL PRIMARY KEY,
    semester_id BIGINT NOT NULL REFERENCES semesters(id) ON DELETE CASCADE,
    max_bookings_per_day INT NOT NULL DEFAULT 1,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(semester_id)
);

-- Create trigger for updated_at
CREATE TRIGGER trg_daily_booking_limits_updated
    BEFORE UPDATE ON daily_booking_limits
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();
