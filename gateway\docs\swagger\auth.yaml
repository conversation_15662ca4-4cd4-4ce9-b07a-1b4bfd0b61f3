openapi: 3.0.0
info:
  title: Edunite Auth API
  description: API documentation for the Authentication endpoints of the Edunite platform
  version: 1.0.0
  contact:
    name: Edunite Support
    email: <EMAIL>

servers:
  - url: http://localhost:8081
    description: Local development server
  - url: https://api.edunite.com
    description: Production server

tags:
  - name: auth
    description: Authentication operations

paths:
  /auth/register:
    post:
      tags:
        - auth
      summary: Register a new user
      description: Creates a new user account in the system
      operationId: registerUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - password
                - email
                - name
                - surname
              properties:
                username:
                  type: string
                  description: Username for the new account
                  example: "johndoe"
                password:
                  type: string
                  format: password
                  description: Password for the new account
                  example: "securePassword123"
                email:
                  type: string
                  format: email
                  description: Email address for the new account
                  example: "<EMAIL>"
                name:
                  type: string
                  description: First name of the user
                  example: "<PERSON>"
                surname:
                  type: string
                  description: Last name of the user
                  example: "<PERSON><PERSON>"
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                    description: JWT authentication token
                    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  user:
                    $ref: '#/components/schemas/User'
        '400':
          description: Invalid request body
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '409':
          description: Username or email already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/login:
    post:
      tags:
        - auth
      summary: User login
      description: Authenticates a user and returns a token
      operationId: loginUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - password
              properties:
                username:
                  type: string
                  description: Username or email address
                  example: "johndoe"
                password:
                  type: string
                  format: password
                  description: User password
                  example: "securePassword123"
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                    description: JWT authentication token
                    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  user:
                    $ref: '#/components/schemas/User'
        '400':
          description: Invalid request body
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        keycloak_id:
          type: string
          example: "f47ac10b-58cc-4372-a567-0e02b2c3d479"
        name:
          type: string
          example: "John"
        surname:
          type: string
          example: "Doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        role:
          type: string
          example: "student"
        created_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"

    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
          example: "Invalid credentials"
