package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

// SetupSwaggerRoutes sets up routes for the Swagger UI
func SetupSwaggerRoutes(r *gin.Engine) {
	swaggerHandler := handlers.NewSwaggerHandler()

	// Redirect /swagger to the Swagger UI
	r.GET("/swagger", swaggerHandler.ServeSwaggerUI)
	
	// Serve Swagger UI static files
	r.Static("/docs", "./docs")
}
