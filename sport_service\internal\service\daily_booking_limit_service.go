package service

import (
	"context"
	"errors"

	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
	"github.com/olzzhas/edunite-server/sport_service/internal/repository"
)

// DailyBookingLimitService handles operations related to daily booking limits
type DailyBookingLimitService struct {
	dailyBookingLimitRepo repository.DailyBookingLimitRepository
}

// NewDailyBookingLimitService creates a new daily booking limit service
func NewDailyBookingLimitService(
	dailyBookingLimitRepo repository.DailyBookingLimitRepository,
) *DailyBookingLimitService {
	return &DailyBookingLimitService{
		dailyBookingLimitRepo: dailyBookingLimitRepo,
	}
}

// CreateDailyBookingLimit creates a new daily booking limit
func (s *DailyBookingLimitService) CreateDailyBookingLimit(ctx context.Context, limit *domain.DailyBookingLimit) error {
	// Validate the limit
	if err := s.validateDailyBookingLimit(limit); err != nil {
		return err
	}

	// Create the limit
	return s.dailyBookingLimitRepo.Create(ctx, limit)
}

// UpdateDailyBookingLimit updates an existing daily booking limit
func (s *DailyBookingLimitService) UpdateDailyBookingLimit(ctx context.Context, limit *domain.DailyBookingLimit) error {
	// Validate the limit
	if err := s.validateDailyBookingLimit(limit); err != nil {
		return err
	}

	// Update the limit
	return s.dailyBookingLimitRepo.Update(ctx, limit)
}

// GetDailyBookingLimit retrieves a daily booking limit by ID
func (s *DailyBookingLimitService) GetDailyBookingLimit(ctx context.Context, limitID int64) (*domain.DailyBookingLimit, error) {
	return s.dailyBookingLimitRepo.GetByID(ctx, limitID)
}

// GetDailyBookingLimitBySemesterID retrieves a daily booking limit by semester ID
func (s *DailyBookingLimitService) GetDailyBookingLimitBySemesterID(ctx context.Context, semesterID int64) (*domain.DailyBookingLimit, error) {
	return s.dailyBookingLimitRepo.GetBySemesterID(ctx, semesterID)
}

// ListDailyBookingLimits retrieves all daily booking limits
func (s *DailyBookingLimitService) ListDailyBookingLimits(ctx context.Context, page, pageSize int) ([]*domain.DailyBookingLimit, int, error) {
	limits, err := s.dailyBookingLimitRepo.List(ctx, page, pageSize)
	if err != nil {
		return nil, 0, err
	}

	count, err := s.dailyBookingLimitRepo.Count(ctx)
	if err != nil {
		return nil, 0, err
	}

	return limits, count, nil
}

// Helper methods

func (s *DailyBookingLimitService) validateDailyBookingLimit(limit *domain.DailyBookingLimit) error {
	// Check if the maximum bookings per day is positive
	if limit.MaxBookingsPerDay <= 0 {
		return errors.New("maximum bookings per day must be positive")
	}

	return nil
}

func (s *DailyBookingLimitService) DeleteDailyBookingLimit(ctx context.Context, limitID int64) error {
	return s.dailyBookingLimitRepo.Delete(ctx, limitID)
}
