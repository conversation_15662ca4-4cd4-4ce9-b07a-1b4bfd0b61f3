# Final test to verify the OID 17100 fix

Write-Host "🔍 Final verification of the OID 17100 database scanning fix" -ForegroundColor Green

# Test the transcript endpoint that was originally failing
Write-Host "`nTesting the problematic endpoint: GET /transcripts/user/4" -ForegroundColor Yellow

try {
    # Try to get the response, capturing both success and error cases
    $response = Invoke-WebRequest -Uri "http://localhost:8081/transcripts/user/4" -Method GET -ErrorAction Stop
    Write-Host "✅ Unexpected success (should require auth): $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    $errorResponse = $_.Exception.Response
    $statusCode = $errorResponse.StatusCode
    $errorMessage = $_.Exception.Message
    
    Write-Host "Status Code: $statusCode" -ForegroundColor Cyan
    Write-Host "Error Message: $errorMessage" -ForegroundColor Cyan
    
    # Check if we get the specific OID error
    if ($errorMessage -like "*OID 17100*" -or $errorMessage -like "*unknown oid 17100*") {
        Write-Host "❌ FAILED: OID 17100 error still exists!" -ForegroundColor Red
        Write-Host "The database scanning fix did not work." -ForegroundColor Red
    } elseif ($statusCode -eq 401 -or $errorMessage -like "*401*" -or $errorMessage -like "*Unauthorized*" -or $errorMessage -like "*Authorization*") {
        Write-Host "✅ SUCCESS: Got expected authentication error instead of OID error!" -ForegroundColor Green
        Write-Host "The database scanning fix is working correctly." -ForegroundColor Green
    } else {
        Write-Host "⚠️  Got different error than expected" -ForegroundColor Yellow
        Write-Host "This might indicate a different issue, but no OID 17100 error detected." -ForegroundColor Yellow
    }
}

# Also test the degrees endpoint to make sure basic functionality works
Write-Host "`nTesting public endpoint: GET /degrees" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/degrees" -Method GET
    Write-Host "✅ Degrees endpoint working: Found $($response.degrees.Count) degrees" -ForegroundColor Green
} catch {
    Write-Host "❌ Degrees endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎯 Summary:" -ForegroundColor Green
Write-Host "If you see Authorization or 401 errors instead of OID 17100 errors," -ForegroundColor White
Write-Host "then the database scanning fix has been successfully applied!" -ForegroundColor White
