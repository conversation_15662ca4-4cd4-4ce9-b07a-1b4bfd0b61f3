syntax = "proto3";

package logger;

import "google/protobuf/timestamp.proto";

option go_package = "/pb";

service LoggerService {
  // Existing method for logging events
  rpc LogEvent(LogEventRequest) returns (LogEventResponse) {}

  // New methods for fetching logs
  rpc GetLogs(GetLogsRequest) returns (GetLogsResponse) {}
  rpc GetLogByID(GetLogByIDRequest) returns (LogEntry) {}
  rpc CountLogs(GetLogsRequest) returns (CountLogsResponse) {}
  rpc GetLogLevels(GetLogLevelsRequest) returns (GetLogLevelsResponse) {}
  rpc GetLogServices(GetLogServicesRequest) returns (GetLogServicesResponse) {}
}

// Existing messages
message LogEventRequest {
  string level = 1;
  string message = 2;
  string service_name = 3;
  map<string, string> data = 4;
}

message LogEventResponse {
  bool success = 1;
  string message = 2;
}

// New messages for log fetching
message LogEntry {
  string id = 1;
  string level = 2;
  string message = 3;
  string service = 4;
  map<string, string> data = 5;
  google.protobuf.Timestamp datetime = 6;
}

message GetLogsRequest {
  string level = 1;
  string service = 2;
  google.protobuf.Timestamp start_date = 3;
  google.protobuf.Timestamp end_date = 4;
  int64 limit = 5;
  int64 skip = 6;
}

message GetLogsResponse {
  repeated LogEntry logs = 1;
}

message GetLogByIDRequest {
  string id = 1;
}

message CountLogsResponse {
  int64 count = 1;
}

message GetLogLevelsRequest {
  // Empty request
}

message GetLogLevelsResponse {
  repeated string levels = 1;
}

message GetLogServicesRequest {
  // Empty request
}

message GetLogServicesResponse {
  repeated string services = 1;
}
