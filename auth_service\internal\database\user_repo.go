package database

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v4/pgxpool"
	"golang.org/x/crypto/bcrypt"
)

var (
	ErrUserNotFound       = errors.New("user not found")
	ErrDuplicateEmail     = errors.New("email already exists")
	ErrDuplicateUsername  = errors.New("username already exists")
	ErrInvalidCredentials = errors.New("invalid credentials")
)

// UserRole represents possible user roles
type UserRole string

const (
	RoleStudent   UserRole = "student"
	RoleTeacher   UserRole = "teacher"
	RoleModerator UserRole = "moderator"
	RoleAdmin     UserRole = "admin"
)

// ValidateRole checks if the role is valid
func ValidateRole(role UserRole) bool {
	switch role {
	case RoleStudent, RoleTeacher, RoleModerator, RoleAdmin:
		return true
	default:
		return false
	}
}

// User represents a user in the database
type User struct {
	ID           int64     `json:"id"`
	Username     string    `json:"username"`
	PasswordHash string    `json:"-"` // Never expose password hash
	Email        string    `json:"email"`
	Name         string    `json:"name"`
	Surname      string    `json:"surname"`
	Role         UserRole  `json:"role"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	Version      int       `json:"version"`
}

// UserRepository defines the interface for user database operations
type UserRepository interface {
	CreateUser(ctx context.Context, user *User, password string) error
	GetUserByID(ctx context.Context, id int64) (*User, error)
	GetUserByEmail(ctx context.Context, email string) (*User, error)
	GetUserByUsername(ctx context.Context, username string) (*User, error)
	Authenticate(ctx context.Context, usernameOrEmail, password string) (*User, error)
}

// userRepository implements UserRepository
type userRepository struct {
	db *pgxpool.Pool
}

// NewUserRepository creates a new UserRepository
func NewUserRepository(db *pgxpool.Pool) UserRepository {
	return &userRepository{db: db}
}

// CreateUser creates a new user in the database
func (r *userRepository) CreateUser(ctx context.Context, user *User, password string) error {
	// Check if email already exists
	var exists bool
	err := r.db.QueryRow(ctx, "SELECT EXISTS(SELECT 1 FROM users WHERE email = $1)", user.Email).Scan(&exists)
	if err != nil {
		return fmt.Errorf("error checking email existence: %w", err)
	}
	if exists {
		return ErrDuplicateEmail
	}

	// Check if username already exists
	err = r.db.QueryRow(ctx, "SELECT EXISTS(SELECT 1 FROM users WHERE username = $1)", user.Username).Scan(&exists)
	if err != nil {
		return fmt.Errorf("error checking username existence: %w", err)
	}
	if exists {
		return ErrDuplicateUsername
	}

	// Hash the password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("error hashing password: %w", err)
	}
	user.PasswordHash = string(hashedPassword)

	// Set default role if not provided
	if user.Role == "" {
		user.Role = RoleStudent
	}

	// Validate role
	if !ValidateRole(user.Role) {
		return fmt.Errorf("invalid role: %s", user.Role)
	}

	// Set timestamps
	now := time.Now()
	user.CreatedAt = now
	user.UpdatedAt = now
	user.Version = 1

	// Insert user into database
	err = r.db.QueryRow(ctx,
		`INSERT INTO users (username, password_hash, email, name, surname, role, created_at, updated_at, version)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		RETURNING id`,
		user.Username, user.PasswordHash, user.Email, user.Name, user.Surname, user.Role,
		user.CreatedAt, user.UpdatedAt, user.Version,
	).Scan(&user.ID)

	if err != nil {
		return fmt.Errorf("error creating user: %w", err)
	}

	return nil
}

// GetUserByID retrieves a user by ID
func (r *userRepository) GetUserByID(ctx context.Context, id int64) (*User, error) {
	user := &User{}
	err := r.db.QueryRow(ctx,
		`SELECT id, username, password_hash, email, name, surname, role, created_at, updated_at, version
		FROM users WHERE id = $1`,
		id,
	).Scan(
		&user.ID, &user.Username, &user.PasswordHash, &user.Email, &user.Name, &user.Surname,
		&user.Role, &user.CreatedAt, &user.UpdatedAt, &user.Version,
	)

	if err != nil {
		if err.Error() == "no rows in result set" {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("error getting user by ID: %w", err)
	}

	return user, nil
}

// GetUserByEmail retrieves a user by email
func (r *userRepository) GetUserByEmail(ctx context.Context, email string) (*User, error) {
	user := &User{}
	err := r.db.QueryRow(ctx,
		`SELECT id, username, password_hash, email, name, surname, role, created_at, updated_at, version
		FROM users WHERE email = $1`,
		email,
	).Scan(
		&user.ID, &user.Username, &user.PasswordHash, &user.Email, &user.Name, &user.Surname,
		&user.Role, &user.CreatedAt, &user.UpdatedAt, &user.Version,
	)

	if err != nil {
		if err.Error() == "no rows in result set" {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("error getting user by email: %w", err)
	}

	return user, nil
}

// GetUserByUsername retrieves a user by username
func (r *userRepository) GetUserByUsername(ctx context.Context, username string) (*User, error) {
	user := &User{}
	err := r.db.QueryRow(ctx,
		`SELECT id, username, password_hash, email, name, surname, role, created_at, updated_at, version
		FROM users WHERE username = $1`,
		username,
	).Scan(
		&user.ID, &user.Username, &user.PasswordHash, &user.Email, &user.Name, &user.Surname,
		&user.Role, &user.CreatedAt, &user.UpdatedAt, &user.Version,
	)

	if err != nil {
		if err.Error() == "no rows in result set" {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("error getting user by username: %w", err)
	}

	return user, nil
}

// Authenticate verifies user credentials and returns the user if valid
func (r *userRepository) Authenticate(ctx context.Context, usernameOrEmail, password string) (*User, error) {
	// Try to find user by username or email
	user := &User{}
	err := r.db.QueryRow(ctx,
		`SELECT id, username, password_hash, email, name, surname, role, created_at, updated_at, version
		FROM users WHERE username = $1 OR email = $1`,
		usernameOrEmail,
	).Scan(
		&user.ID, &user.Username, &user.PasswordHash, &user.Email, &user.Name, &user.Surname,
		&user.Role, &user.CreatedAt, &user.UpdatedAt, &user.Version,
	)

	if err != nil {
		if err.Error() == "no rows in result set" {
			return nil, ErrInvalidCredentials
		}
		return nil, fmt.Errorf("error authenticating user: %w", err)
	}

	// Verify password
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password))
	if err != nil {
		return nil, ErrInvalidCredentials
	}

	return user, nil
}
