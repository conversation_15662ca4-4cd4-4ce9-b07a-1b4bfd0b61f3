package repository

import (
	"context"
	
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

// AttendanceRepository defines the interface for attendance data access
type AttendanceRepository interface {
	// Create creates a new attendance record
	Create(ctx context.Context, attendance *domain.Attendance) error
	
	// GetByBookingID retrieves an attendance record by booking ID
	GetByBookingID(ctx context.Context, bookingID int64) (*domain.Attendance, error)
	
	// Update updates an existing attendance record
	Update(ctx context.Context, attendance *domain.Attendance) error
	
	// Delete deletes an attendance record by booking ID
	Delete(ctx context.Context, bookingID int64) error
	
	// List retrieves attendance records based on filters
	List(ctx context.Context, filter domain.AttendanceFilter) ([]*domain.Attendance, error)
	
	// Count counts attendance records based on filters
	Count(ctx context.Context, filter domain.AttendanceFilter) (int, error)
	
	// GetByUserAndSchedule retrieves an attendance record by user ID and schedule ID
	GetByUserAndSchedule(ctx context.Context, userID, scheduleID int64) (*domain.Attendance, error)
	
	// GetStats retrieves attendance statistics for a user
	GetUserStats(ctx context.Context, userID int64, filter domain.AttendanceFilter) (*domain.AttendanceStats, error)
	
	// GetStatsBySportType retrieves attendance statistics for a user by sport type
	GetUserStatsBySportType(ctx context.Context, userID, sportTypeID int64) (*domain.AttendanceStats, error)
	
	// GetStatsBySemester retrieves attendance statistics for a user by semester
	GetUserStatsBySemester(ctx context.Context, userID, semesterID int64) (*domain.AttendanceStats, error)
}
