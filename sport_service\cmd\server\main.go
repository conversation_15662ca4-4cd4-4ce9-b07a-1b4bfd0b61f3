package main

import (
	"context"

	"fmt"
	"github.com/olzzhas/edunite-server/sport_service/internal/repository"
	sportpb "github.com/olzzhas/edunite-server/sport_service/proto"
	"google.golang.org/grpc"

	"log"
	"net"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/sport_service/internal/config"
	sportgrpc "github.com/olzzhas/edunite-server/sport_service/internal/grpc"
	"github.com/olzzhas/edunite-server/sport_service/internal/repository/postgres"
	"github.com/olzzhas/edunite-server/sport_service/internal/service"

	"google.golang.org/grpc/reflection"
)

func main() {
	// Load configuration
	cfg := config.LoadConfig()

	// Connect to database (CockroachDB without certificates)
	dbPool := ConnectDB(cfg)

	defer dbPool.Close()

	// Create repositories
	repos := postgres.NewRepositories(dbPool)

	// Create services
	services := service.NewServices((*repository.Repositories)(repos))

	// Create gRPC server
	grpcServer := grpc.NewServer()

	// Register services
	sportTypeServer := sportgrpc.NewSportTypeServer(services)
	facilityServer := sportgrpc.NewFacilityServer(services)
	scheduleServer := sportgrpc.NewScheduleServer(services)
	bookingServer := sportgrpc.NewBookingServer(services)
	medicalCertificateServer := sportgrpc.NewMedicalCertificateServer(services)
	semesterLimitServer := sportgrpc.NewSemesterLimitServer(services)
	physicalEducationServer := sportgrpc.NewPhysicalEducationServer(services)

	sportpb.RegisterSportTypeServiceServer(grpcServer, sportTypeServer)
	sportpb.RegisterFacilityServiceServer(grpcServer, facilityServer)
	sportpb.RegisterScheduleServiceServer(grpcServer, scheduleServer)
	sportpb.RegisterBookingServiceServer(grpcServer, bookingServer)
	sportpb.RegisterMedicalCertificateServiceServer(grpcServer, medicalCertificateServer)
	sportpb.RegisterSemesterLimitServiceServer(grpcServer, semesterLimitServer)
	sportpb.RegisterPhysicalEducationServiceServer(grpcServer, physicalEducationServer)

	// Enable reflection for tools like grpcurl
	reflection.Register(grpcServer)

	// Start listener on port 50054
	listener, err := net.Listen("tcp", ":50054")
	if err != nil {
		log.Fatalf("Failed to listen on port 50054: %v", err)
	}
	log.Printf("gRPC server listening on :50054")

	// Start gRPC server in a separate goroutine
	go func() {
		if serveErr := grpcServer.Serve(listener); serveErr != nil {
			log.Fatalf("Failed to serve gRPC: %v", serveErr)
		}
	}()

	// Wait for shutdown signal
	waitForShutdown(grpcServer)
	log.Println("Server gracefully stopped")
}

// waitForShutdown blocks until a system signal is received and gracefully stops the gRPC server
func waitForShutdown(server *grpc.Server) {
	// Create channel for OS signals
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM)
	<-quit

	log.Println("Received shutdown signal, gracefully stopping gRPC server...")
	server.GracefulStop()
}

func ConnectDB(cfg *config.Config) *pgxpool.Pool {
	// Формирование строки подключения для CockroachDB без сертификатов
	connstring := fmt.Sprintf(
		"host=%s port=%s dbname=%s user=%s password=%s sslmode=require",
		cfg.Database.Host, cfg.Database.Port, cfg.Database.Name, cfg.Database.User, cfg.Database.Password)

	// Используем pgxpool.ParseConfig для получения конфигурации пула
	poolConfig, err := pgxpool.ParseConfig(connstring)
	if err != nil {
		log.Fatalf("Unable to parse pool config: %v", err)
	}

	// Реализация повторных попыток подключения
	var pool *pgxpool.Pool
	for i := 0; i < 5; i++ {
		pool, err = pgxpool.ConnectConfig(context.Background(), poolConfig)
		if err == nil {
			log.Println("Connected to CockroachDB successfully.")
			break
		}
		log.Printf("Failed to connect to CockroachDB: %v. Retrying in 2 seconds...\n", err)
		time.Sleep(2 * time.Second)
	}

	if err != nil {
		log.Fatalf("Unable to connect to CockroachDB after retries: %v\n", err)
	}

	db := pool
	return db
}
