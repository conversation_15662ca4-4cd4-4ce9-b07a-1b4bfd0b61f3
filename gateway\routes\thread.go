package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

// SetupThreadRoutes настраивает маршруты для потоков
func SetupThreadRoutes(r *gin.Engine, threadHandler *handlers.ThreadHandler) {
	threadsGroup := r.Group("/thread")
	{
		// Combined endpoint for creating thread with schedule
		threadsGroup.POST("/with-schedule", threadHandler.CreateThreadWithScheduleHandler) // POST /thread/with-schedule
		threadsGroup.POST("", threadHandler.CreateThreadHandler)                           // POST /threads
		threadsGroup.GET("", threadHandler.ListThreadsHandler)                             // GET /threads
		threadsGroup.GET("/:id", threadHandler.GetThreadByIDHandler)                       // GET /threads/:id
		threadsGroup.DELETE("/:id", threadHandler.DeleteThreadByIDHandler)                 // DELETE /threads/:id
		threadsGroup.GET("/course/:course_id", threadHandler.ListThreadsByCourseHandler)   // GET /threads/course/:course_id
		// Обновление потока обычно делается через PUT или PATCH
		threadsGroup.PUT("/:id", threadHandler.UpdateThreadByIDHandler) // PUT /threads/:id

		threadsGroup.POST("/register", threadHandler.RegisterUserToThreadHandler)               // POST /thread_registrations/register
		threadsGroup.POST("/register_many", threadHandler.RegisterManyUsersToThreadHandler)     // POST /thread_registrations/register_many
		threadsGroup.POST("/remove", threadHandler.RemoveRegistrationToThreadHandler)           // POST /thread_registrations/remove
		threadsGroup.POST("/remove_many", threadHandler.RemoveManyRegistrationsToThreadHandler) // POST /thread_registrations/remove_many
		threadsGroup.GET("/register/list", threadHandler.ListThreadRegistrationsHandler)        // GET /register/list

		// Week
		threadsGroup.POST("/week", threadHandler.CreateWeekHandler)       // POST /thread/week
		threadsGroup.GET("/week", threadHandler.ListWeeksByThreadHandler) // GET /thread/week?thread_id=123
		threadsGroup.GET("/week/:id", threadHandler.GetWeekHandler)       // GET /thread/week/:id
		threadsGroup.PUT("/week/:id", threadHandler.UpdateWeekHandler)    // PUT /thread/week/:id
		threadsGroup.DELETE("/week/:id", threadHandler.DeleteWeekHandler) // DELETE /thread/week/:id

		// Schedule
		threadsGroup.POST("/:id/schedules", threadHandler.CreateThreadScheduleHandler)                // POST /thread/:id/schedules
		threadsGroup.GET("/:id/schedules", threadHandler.ListThreadSchedulesHandler)                  // GET /thread/:id/schedules
		threadsGroup.GET("/schedules/:schedule_id", threadHandler.GetThreadScheduleByIDHandler)       // GET /thread/schedules/:schedule_id
		threadsGroup.PUT("/schedules/:schedule_id", threadHandler.UpdateThreadScheduleHandler)        // PUT /thread/schedules/:schedule_id
		threadsGroup.DELETE("/schedules/:schedule_id", threadHandler.DeleteThreadScheduleByIDHandler) // DELETE /thread/schedules/:schedule_id

		// Schedule Location (custom endpoints for location field)
		threadsGroup.PUT("/schedules/:schedule_id/location", threadHandler.UpdateScheduleLocationHandler) // PUT /thread/schedules/:schedule_id/location
		threadsGroup.GET("/schedules/:schedule_id/location", threadHandler.GetScheduleLocationHandler)    // GET /thread/schedules/:schedule_id/location

		threadsGroup.GET("/user/:user_id", threadHandler.ListThreadsForUserHandler)
		threadsGroup.GET("/user/:user_id/with-schedules", threadHandler.ListThreadsForUserWithSchedulesHandler)
		threadsGroup.GET("/:id/weeks-hw", threadHandler.ListWeeksWithHomeworkHandler)

		threadsGroup.GET("/members/:thread_id", threadHandler.ListThreadGroupmatesHandler)

		// Thread availability
		threadsGroup.POST("/availability", threadHandler.GetThreadAvailabilityHandler) // POST /thread/availability
	}
}
