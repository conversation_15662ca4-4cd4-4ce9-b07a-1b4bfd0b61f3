package config

import (
	"log"
	"os"

	"github.com/joho/godotenv"
)

type AuthServiceConfig struct {
	Target string
}

type UserServiceConfig struct {
	Target string
}

type LoggerServiceConfig struct {
	Target         string
	RabbitMQTarget string
}

type CourseServiceConfig struct {
	Target string
}

type StorageServiceConfig struct {
	Target string
}

type SportServiceConfig struct {
	Target string
}

type Config struct {
	Services struct {
		AuthService    AuthServiceConfig
		UserService    UserServiceConfig
		LoggerService  LoggerServiceConfig
		CourseService  CourseServiceConfig
		StorageService StorageServiceConfig
		SportService   SportServiceConfig
	}
}

// LoadConfig загружает переменные окружения из .env файла и возвращает конфигурацию
func LoadConfig() *Config {
	// Загружаем переменные из .env файла
	err := godotenv.Load(".env")
	if err != nil {
		log.Fatalf("Error loading .env file: %v", err)
	}

	return &Config{
		Services: struct {
			AuthService    AuthServiceConfig
			UserService    UserServiceConfig
			LoggerService  LoggerServiceConfig
			CourseService  CourseServiceConfig
			StorageService StorageServiceConfig
			SportService   SportServiceConfig
		}{
			AuthService: AuthServiceConfig{
				Target: os.Getenv("AUTH_SERVICE_URL"),
			},
			UserService: UserServiceConfig{
				Target: os.Getenv("USER_SERVICE_URL"),
			},
			LoggerService: LoggerServiceConfig{
				Target:         os.Getenv("LOGGER_SERVICE_URL"),
				RabbitMQTarget: os.Getenv("RABBIT_MQ_SERVICE_URL"),
			},
			CourseService: CourseServiceConfig{
				Target: os.Getenv("COURSE_SERVICE_URL"),
			},
			StorageService: StorageServiceConfig{
				Target: os.Getenv("STORAGE_SERVICE_URL"),
			},
			SportService: SportServiceConfig{
				Target: os.Getenv("SPORT_SERVICE_URL"),
			},
		},
	}
}
