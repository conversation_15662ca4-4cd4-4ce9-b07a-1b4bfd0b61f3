package postgres

import (
	"context"
	"errors"
	"fmt"

	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

type sportTypeRepository struct {
	db *pgxpool.Pool
}

// NewSportTypeRepository creates a new sport type repository
func NewSportTypeRepository(db *pgxpool.Pool) *sportTypeRepository {
	return &sportTypeRepository{db: db}
}

// Create creates a new sport type
func (r *sportTypeRepository) Create(ctx context.Context, sportType *domain.SportType) error {
	query := `
		INSERT INTO sport_types (title, description, category, requires_certificate)
		VALUES ($1, $2, $3, $4)
		RETURNING id, created_at, updated_at, version
	`

	err := r.db.QueryRow(ctx, query,
		sportType.Title,
		sportType.Description,
		sportType.Category,
		sportType.RequiresCertificate,
	).Scan(
		&sportType.ID,
		&sportType.CreatedAt,
		&sportType.UpdatedAt,
		&sportType.Version,
	)

	if err != nil {
		return fmt.Errorf("failed to create sport type: %w", err)
	}

	return nil
}

// GetByID retrieves a sport type by ID
func (r *sportTypeRepository) GetByID(ctx context.Context, id int64) (*domain.SportType, error) {
	query := `
		SELECT id, title, description, category, requires_certificate, created_at, updated_at, version
		FROM sport_types
		WHERE id = $1
	`

	var sportType domain.SportType
	err := r.db.QueryRow(ctx, query, id).Scan(
		&sportType.ID,
		&sportType.Title,
		&sportType.Description,
		&sportType.Category,
		&sportType.RequiresCertificate,
		&sportType.CreatedAt,
		&sportType.UpdatedAt,
		&sportType.Version,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, domain.ErrSportTypeNotFound
		}
		return nil, fmt.Errorf("failed to get sport type: %w", err)
	}

	return &sportType, nil
}

// Update updates an existing sport type
func (r *sportTypeRepository) Update(ctx context.Context, sportType *domain.SportType) error {
	query := `
		UPDATE sport_types
		SET title = $1, description = $2, category = $3, requires_certificate = $4, version = version + 1
		WHERE id = $5 AND version = $6
		RETURNING updated_at, version
	`

	err := r.db.QueryRow(ctx, query,
		sportType.Title,
		sportType.Description,
		sportType.Category,
		sportType.RequiresCertificate,
		sportType.ID,
		sportType.Version,
	).Scan(
		&sportType.UpdatedAt,
		&sportType.Version,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return domain.ErrSportTypeNotFound
		}
		return fmt.Errorf("failed to update sport type: %w", err)
	}

	return nil
}

// Delete deletes a sport type by ID
func (r *sportTypeRepository) Delete(ctx context.Context, id int64) error {
	query := `
		DELETE FROM sport_types
		WHERE id = $1
	`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete sport type: %w", err)
	}

	if result.RowsAffected() == 0 {
		return domain.ErrSportTypeNotFound
	}

	return nil
}

// List retrieves sport types based on filters
func (r *sportTypeRepository) List(ctx context.Context, filter domain.SportTypeFilter) ([]*domain.SportType, error) {
	query := `
		SELECT id, title, description, category, requires_certificate, created_at, updated_at, version
		FROM sport_types
		WHERE 1=1
	`

	var args []interface{}
	var conditions []string

	// Add filter conditions
	if filter.Title != "" {
		args = append(args, "%"+filter.Title+"%")
		conditions = append(conditions, fmt.Sprintf("AND title ILIKE $%d", len(args)))
	}

	if filter.Category != "" {
		category := ""
		switch filter.Category {
		case domain.SportTypeCategoryLFK:
			category = "lfk"
		default:
			category = "normal"
		}
		args = append(args, category)
		conditions = append(conditions, fmt.Sprintf("AND category = $%d", len(args)))
	}

	if filter.RequiresCertificate != nil {
		args = append(args, *filter.RequiresCertificate)
		conditions = append(conditions, fmt.Sprintf("AND requires_certificate = $%d", len(args)))
	}

	// Add pagination
	if filter.Page <= 0 {
		filter.Page = 1
	}

	if filter.PageSize <= 0 {
		filter.PageSize = 10
	}

	offset := (filter.Page - 1) * filter.PageSize

	for _, condition := range conditions {
		query += " " + condition
	}

	query += " ORDER BY id"
	query += fmt.Sprintf(" LIMIT %d OFFSET %d", filter.PageSize, offset)

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to list sport types: %w", err)
	}
	defer rows.Close()

	var sportTypes []*domain.SportType
	for rows.Next() {
		var sportType domain.SportType
		err := rows.Scan(
			&sportType.ID,
			&sportType.Title,
			&sportType.Description,
			&sportType.Category,
			&sportType.RequiresCertificate,
			&sportType.CreatedAt,
			&sportType.UpdatedAt,
			&sportType.Version,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan sport type: %w", err)
		}
		sportTypes = append(sportTypes, &sportType)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating sport types: %w", err)
	}

	return sportTypes, nil
}

// Count counts sport types based on filters
func (r *sportTypeRepository) Count(ctx context.Context, filter domain.SportTypeFilter) (int, error) {
	query := `
		SELECT COUNT(*)
		FROM sport_types
		WHERE 1=1
	`

	var args []interface{}
	var conditions []string

	// Add filter conditions
	if filter.Title != "" {
		args = append(args, "%"+filter.Title+"%")
		conditions = append(conditions, fmt.Sprintf("AND title ILIKE $%d", len(args)))
	}

	if filter.Category != "" {
		args = append(args, filter.Category)
		conditions = append(conditions, fmt.Sprintf("AND category = $%d", len(args)))
	}

	if filter.RequiresCertificate != nil {
		args = append(args, *filter.RequiresCertificate)
		conditions = append(conditions, fmt.Sprintf("AND requires_certificate = $%d", len(args)))
	}

	for _, condition := range conditions {
		query += " " + condition
	}

	var count int
	err := r.db.QueryRow(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count sport types: %w", err)
	}

	return count, nil
}

func (r *sportTypeRepository) AssignTeacherToSportType(ctx context.Context, teacherSportType *domain.TeacherSportType) error {
	query := `
		INSERT INTO teacher_sport_types (teacher_id, sport_type_id, can_review_certificates)
		VALUES ($1, $2, $3)
		RETURNING created_at, updated_at
	`

	err := r.db.QueryRow(ctx, query,
		teacherSportType.TeacherID,
		teacherSportType.SportTypeID,
		teacherSportType.CanReviewCertificates,
	).Scan(
		&teacherSportType.CreatedAt,
		&teacherSportType.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to assign teacher to sport type: %w", err)
	}

	return nil
}

func (r *sportTypeRepository) RemoveTeacherFromSportType(ctx context.Context, teacherID, sportTypeID int64) error {
	query := `
		DELETE FROM teacher_sport_types
		WHERE teacher_id = $1 AND sport_type_id = $2
	`

	result, err := r.db.Exec(ctx, query, teacherID, sportTypeID)
	if err != nil {
		return fmt.Errorf("failed to remove teacher from sport type: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("teacher sport type not found")
	}

	return nil
}

func (r *sportTypeRepository) ListTeachersForSportType(ctx context.Context, id int64) ([]int64, error) {
	query := `
		SELECT teacher_id
		FROM teacher_sport_types
		WHERE sport_type_id = $1
	`

	rows, err := r.db.Query(ctx, query, id)
	if err != nil {
		return nil, fmt.Errorf("failed to list teachers for sport type: %w", err)
	}
	defer rows.Close()

	var teacherIDs []int64
	for rows.Next() {
		var teacherID int64
		err := rows.Scan(&teacherID)
		if err != nil {
			return nil, fmt.Errorf("failed to scan teacher ID: %w", err)
		}
		teacherIDs = append(teacherIDs, teacherID)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating teacher IDs: %w", err)
	}

	return teacherIDs, nil
}

func (r *sportTypeRepository) ListSportTypesForTeacher(ctx context.Context, id int64) ([]*domain.SportType, error) {
	query := `
		SELECT st.id, st.title, st.description, st.category, st.requires_certificate, st.created_at, st.updated_at, st.version
		FROM sport_types st
		JOIN teacher_sport_types tst ON st.id = tst.sport_type_id
		WHERE tst.teacher_id = $1
	`

	rows, err := r.db.Query(ctx, query, id)
	if err != nil {
		return nil, fmt.Errorf("failed to list sport types for teacher: %w", err)
	}
	defer rows.Close()

	var sportTypes []*domain.SportType
	for rows.Next() {
		var sportType domain.SportType
		err := rows.Scan(
			&sportType.ID,
			&sportType.Title,
			&sportType.Description,
			&sportType.Category,
			&sportType.RequiresCertificate,
			&sportType.CreatedAt,
			&sportType.UpdatedAt,
			&sportType.Version,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan sport type: %w", err)
		}
		sportTypes = append(sportTypes, &sportType)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating sport types: %w", err)
	}

	return sportTypes, nil
}
