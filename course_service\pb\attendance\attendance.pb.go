// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.2
// source: pb/attendance/attendance.proto

package attendancepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Attendance status enumeration
type AttendanceStatus int32

const (
	AttendanceStatus_ATTENDANCE_STATUS_UNSPECIFIED AttendanceStatus = 0
	AttendanceStatus_ATTENDANCE_STATUS_UNMARKED    AttendanceStatus = 1
	AttendanceStatus_ATTENDANCE_STATUS_PRESENT     AttendanceStatus = 2
	AttendanceStatus_ATTENDANCE_STATUS_ABSENT      AttendanceStatus = 3
	AttendanceStatus_ATTENDANCE_STATUS_EXCUSED     AttendanceStatus = 4
)

// Enum value maps for AttendanceStatus.
var (
	AttendanceStatus_name = map[int32]string{
		0: "ATTENDANCE_STATUS_UNSPECIFIED",
		1: "ATTENDANCE_STATUS_UNMARKED",
		2: "ATTENDANCE_STATUS_PRESENT",
		3: "ATTENDANCE_STATUS_ABSENT",
		4: "ATTENDANCE_STATUS_EXCUSED",
	}
	AttendanceStatus_value = map[string]int32{
		"ATTENDANCE_STATUS_UNSPECIFIED": 0,
		"ATTENDANCE_STATUS_UNMARKED":    1,
		"ATTENDANCE_STATUS_PRESENT":     2,
		"ATTENDANCE_STATUS_ABSENT":      3,
		"ATTENDANCE_STATUS_EXCUSED":     4,
	}
)

func (x AttendanceStatus) Enum() *AttendanceStatus {
	p := new(AttendanceStatus)
	*p = x
	return p
}

func (x AttendanceStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AttendanceStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_pb_attendance_attendance_proto_enumTypes[0].Descriptor()
}

func (AttendanceStatus) Type() protoreflect.EnumType {
	return &file_pb_attendance_attendance_proto_enumTypes[0]
}

func (x AttendanceStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AttendanceStatus.Descriptor instead.
func (AttendanceStatus) EnumDescriptor() ([]byte, []int) {
	return file_pb_attendance_attendance_proto_rawDescGZIP(), []int{0}
}

// Request to create or update attendance
type AttendanceRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ThreadId       int64                  `protobuf:"varint,1,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	UserId         int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	AttendanceDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=attendance_date,json=attendanceDate,proto3" json:"attendance_date,omitempty"`
	Status         AttendanceStatus       `protobuf:"varint,4,opt,name=status,proto3,enum=attendancepb.AttendanceStatus" json:"status,omitempty"`
	Reason         string                 `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"` // optional if status is EXCUSED
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AttendanceRequest) Reset() {
	*x = AttendanceRequest{}
	mi := &file_pb_attendance_attendance_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttendanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttendanceRequest) ProtoMessage() {}

func (x *AttendanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_attendance_attendance_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttendanceRequest.ProtoReflect.Descriptor instead.
func (*AttendanceRequest) Descriptor() ([]byte, []int) {
	return file_pb_attendance_attendance_proto_rawDescGZIP(), []int{0}
}

func (x *AttendanceRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *AttendanceRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AttendanceRequest) GetAttendanceDate() *timestamppb.Timestamp {
	if x != nil {
		return x.AttendanceDate
	}
	return nil
}

func (x *AttendanceRequest) GetStatus() AttendanceStatus {
	if x != nil {
		return x.Status
	}
	return AttendanceStatus_ATTENDANCE_STATUS_UNSPECIFIED
}

func (x *AttendanceRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// Response with attendance record details
type AttendanceResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ThreadId       int64                  `protobuf:"varint,2,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	UserId         int64                  `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	AttendanceDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=attendance_date,json=attendanceDate,proto3" json:"attendance_date,omitempty"`
	Status         AttendanceStatus       `protobuf:"varint,5,opt,name=status,proto3,enum=attendancepb.AttendanceStatus" json:"status,omitempty"`
	Reason         string                 `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AttendanceResponse) Reset() {
	*x = AttendanceResponse{}
	mi := &file_pb_attendance_attendance_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttendanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttendanceResponse) ProtoMessage() {}

func (x *AttendanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_attendance_attendance_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttendanceResponse.ProtoReflect.Descriptor instead.
func (*AttendanceResponse) Descriptor() ([]byte, []int) {
	return file_pb_attendance_attendance_proto_rawDescGZIP(), []int{1}
}

func (x *AttendanceResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AttendanceResponse) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *AttendanceResponse) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AttendanceResponse) GetAttendanceDate() *timestamppb.Timestamp {
	if x != nil {
		return x.AttendanceDate
	}
	return nil
}

func (x *AttendanceResponse) GetStatus() AttendanceStatus {
	if x != nil {
		return x.Status
	}
	return AttendanceStatus_ATTENDANCE_STATUS_UNSPECIFIED
}

func (x *AttendanceResponse) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *AttendanceResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AttendanceResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Request to list attendance by thread and date
type ListAttendanceRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ThreadId       int64                  `protobuf:"varint,1,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	AttendanceDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=attendance_date,json=attendanceDate,proto3" json:"attendance_date,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ListAttendanceRequest) Reset() {
	*x = ListAttendanceRequest{}
	mi := &file_pb_attendance_attendance_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAttendanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAttendanceRequest) ProtoMessage() {}

func (x *ListAttendanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_attendance_attendance_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAttendanceRequest.ProtoReflect.Descriptor instead.
func (*ListAttendanceRequest) Descriptor() ([]byte, []int) {
	return file_pb_attendance_attendance_proto_rawDescGZIP(), []int{2}
}

func (x *ListAttendanceRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *ListAttendanceRequest) GetAttendanceDate() *timestamppb.Timestamp {
	if x != nil {
		return x.AttendanceDate
	}
	return nil
}

// Response containing multiple attendance records
type ListAttendanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Records       []*AttendanceResponse  `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAttendanceResponse) Reset() {
	*x = ListAttendanceResponse{}
	mi := &file_pb_attendance_attendance_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAttendanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAttendanceResponse) ProtoMessage() {}

func (x *ListAttendanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_attendance_attendance_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAttendanceResponse.ProtoReflect.Descriptor instead.
func (*ListAttendanceResponse) Descriptor() ([]byte, []int) {
	return file_pb_attendance_attendance_proto_rawDescGZIP(), []int{3}
}

func (x *ListAttendanceResponse) GetRecords() []*AttendanceResponse {
	if x != nil {
		return x.Records
	}
	return nil
}

// Request to update status or reason
type UpdateAttendanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Status        AttendanceStatus       `protobuf:"varint,2,opt,name=status,proto3,enum=attendancepb.AttendanceStatus" json:"status,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAttendanceRequest) Reset() {
	*x = UpdateAttendanceRequest{}
	mi := &file_pb_attendance_attendance_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAttendanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAttendanceRequest) ProtoMessage() {}

func (x *UpdateAttendanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_attendance_attendance_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAttendanceRequest.ProtoReflect.Descriptor instead.
func (*UpdateAttendanceRequest) Descriptor() ([]byte, []int) {
	return file_pb_attendance_attendance_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateAttendanceRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAttendanceRequest) GetStatus() AttendanceStatus {
	if x != nil {
		return x.Status
	}
	return AttendanceStatus_ATTENDANCE_STATUS_UNSPECIFIED
}

func (x *UpdateAttendanceRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// Request to delete attendance record by ID
type AttendanceIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AttendanceIDRequest) Reset() {
	*x = AttendanceIDRequest{}
	mi := &file_pb_attendance_attendance_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttendanceIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttendanceIDRequest) ProtoMessage() {}

func (x *AttendanceIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_attendance_attendance_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttendanceIDRequest.ProtoReflect.Descriptor instead.
func (*AttendanceIDRequest) Descriptor() ([]byte, []int) {
	return file_pb_attendance_attendance_proto_rawDescGZIP(), []int{5}
}

func (x *AttendanceIDRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type AttendancesByThreadUser struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThreadId      int64                  `protobuf:"varint,1,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	UserId        int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AttendancesByThreadUser) Reset() {
	*x = AttendancesByThreadUser{}
	mi := &file_pb_attendance_attendance_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttendancesByThreadUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttendancesByThreadUser) ProtoMessage() {}

func (x *AttendancesByThreadUser) ProtoReflect() protoreflect.Message {
	mi := &file_pb_attendance_attendance_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttendancesByThreadUser.ProtoReflect.Descriptor instead.
func (*AttendancesByThreadUser) Descriptor() ([]byte, []int) {
	return file_pb_attendance_attendance_proto_rawDescGZIP(), []int{6}
}

func (x *AttendancesByThreadUser) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *AttendancesByThreadUser) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// Empty response for delete operations
type AttendanceEmptyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AttendanceEmptyResponse) Reset() {
	*x = AttendanceEmptyResponse{}
	mi := &file_pb_attendance_attendance_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttendanceEmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttendanceEmptyResponse) ProtoMessage() {}

func (x *AttendanceEmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_attendance_attendance_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttendanceEmptyResponse.ProtoReflect.Descriptor instead.
func (*AttendanceEmptyResponse) Descriptor() ([]byte, []int) {
	return file_pb_attendance_attendance_proto_rawDescGZIP(), []int{7}
}

var File_pb_attendance_attendance_proto protoreflect.FileDescriptor

const file_pb_attendance_attendance_proto_rawDesc = "" +
	"\n" +
	"\x1epb/attendance/attendance.proto\x12\fattendancepb\x1a\x1fgoogle/protobuf/timestamp.proto\"\xde\x01\n" +
	"\x11AttendanceRequest\x12\x1b\n" +
	"\tthread_id\x18\x01 \x01(\x03R\bthreadId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\x12C\n" +
	"\x0fattendance_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x0eattendanceDate\x126\n" +
	"\x06status\x18\x04 \x01(\x0e2\x1e.attendancepb.AttendanceStatusR\x06status\x12\x16\n" +
	"\x06reason\x18\x05 \x01(\tR\x06reason\"\xe5\x02\n" +
	"\x12AttendanceResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tthread_id\x18\x02 \x01(\x03R\bthreadId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\x03R\x06userId\x12C\n" +
	"\x0fattendance_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x0eattendanceDate\x126\n" +
	"\x06status\x18\x05 \x01(\x0e2\x1e.attendancepb.AttendanceStatusR\x06status\x12\x16\n" +
	"\x06reason\x18\x06 \x01(\tR\x06reason\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"y\n" +
	"\x15ListAttendanceRequest\x12\x1b\n" +
	"\tthread_id\x18\x01 \x01(\x03R\bthreadId\x12C\n" +
	"\x0fattendance_date\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\x0eattendanceDate\"T\n" +
	"\x16ListAttendanceResponse\x12:\n" +
	"\arecords\x18\x01 \x03(\v2 .attendancepb.AttendanceResponseR\arecords\"y\n" +
	"\x17UpdateAttendanceRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x126\n" +
	"\x06status\x18\x02 \x01(\x0e2\x1e.attendancepb.AttendanceStatusR\x06status\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"%\n" +
	"\x13AttendanceIDRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"O\n" +
	"\x17AttendancesByThreadUser\x12\x1b\n" +
	"\tthread_id\x18\x01 \x01(\x03R\bthreadId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\"\x19\n" +
	"\x17AttendanceEmptyResponse*\xb1\x01\n" +
	"\x10AttendanceStatus\x12!\n" +
	"\x1dATTENDANCE_STATUS_UNSPECIFIED\x10\x00\x12\x1e\n" +
	"\x1aATTENDANCE_STATUS_UNMARKED\x10\x01\x12\x1d\n" +
	"\x19ATTENDANCE_STATUS_PRESENT\x10\x02\x12\x1c\n" +
	"\x18ATTENDANCE_STATUS_ABSENT\x10\x03\x12\x1d\n" +
	"\x19ATTENDANCE_STATUS_EXCUSED\x10\x042\xff\x03\n" +
	"\x11AttendanceService\x12W\n" +
	"\x10CreateAttendance\x12\x1f.attendancepb.AttendanceRequest\x1a .attendancepb.AttendanceResponse\"\x00\x12]\n" +
	"\x0eListAttendance\x12#.attendancepb.ListAttendanceRequest\x1a$.attendancepb.ListAttendanceResponse\"\x00\x12]\n" +
	"\x10UpdateAttendance\x12%.attendancepb.UpdateAttendanceRequest\x1a .attendancepb.AttendanceResponse\"\x00\x12b\n" +
	"\x14DeleteAttendanceByID\x12!.attendancepb.AttendanceIDRequest\x1a%.attendancepb.AttendanceEmptyResponse\"\x00\x12o\n" +
	"\x1dDeleteAttendancesByThreadUser\x12%.attendancepb.AttendancesByThreadUser\x1a%.attendancepb.AttendanceEmptyResponse\"\x00BMZKgithub.com/olzzhas/edunite-server/course_service/pb/attendance;attendancepbb\x06proto3"

var (
	file_pb_attendance_attendance_proto_rawDescOnce sync.Once
	file_pb_attendance_attendance_proto_rawDescData []byte
)

func file_pb_attendance_attendance_proto_rawDescGZIP() []byte {
	file_pb_attendance_attendance_proto_rawDescOnce.Do(func() {
		file_pb_attendance_attendance_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pb_attendance_attendance_proto_rawDesc), len(file_pb_attendance_attendance_proto_rawDesc)))
	})
	return file_pb_attendance_attendance_proto_rawDescData
}

var file_pb_attendance_attendance_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pb_attendance_attendance_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_pb_attendance_attendance_proto_goTypes = []any{
	(AttendanceStatus)(0),           // 0: attendancepb.AttendanceStatus
	(*AttendanceRequest)(nil),       // 1: attendancepb.AttendanceRequest
	(*AttendanceResponse)(nil),      // 2: attendancepb.AttendanceResponse
	(*ListAttendanceRequest)(nil),   // 3: attendancepb.ListAttendanceRequest
	(*ListAttendanceResponse)(nil),  // 4: attendancepb.ListAttendanceResponse
	(*UpdateAttendanceRequest)(nil), // 5: attendancepb.UpdateAttendanceRequest
	(*AttendanceIDRequest)(nil),     // 6: attendancepb.AttendanceIDRequest
	(*AttendancesByThreadUser)(nil), // 7: attendancepb.AttendancesByThreadUser
	(*AttendanceEmptyResponse)(nil), // 8: attendancepb.AttendanceEmptyResponse
	(*timestamppb.Timestamp)(nil),   // 9: google.protobuf.Timestamp
}
var file_pb_attendance_attendance_proto_depIdxs = []int32{
	9,  // 0: attendancepb.AttendanceRequest.attendance_date:type_name -> google.protobuf.Timestamp
	0,  // 1: attendancepb.AttendanceRequest.status:type_name -> attendancepb.AttendanceStatus
	9,  // 2: attendancepb.AttendanceResponse.attendance_date:type_name -> google.protobuf.Timestamp
	0,  // 3: attendancepb.AttendanceResponse.status:type_name -> attendancepb.AttendanceStatus
	9,  // 4: attendancepb.AttendanceResponse.created_at:type_name -> google.protobuf.Timestamp
	9,  // 5: attendancepb.AttendanceResponse.updated_at:type_name -> google.protobuf.Timestamp
	9,  // 6: attendancepb.ListAttendanceRequest.attendance_date:type_name -> google.protobuf.Timestamp
	2,  // 7: attendancepb.ListAttendanceResponse.records:type_name -> attendancepb.AttendanceResponse
	0,  // 8: attendancepb.UpdateAttendanceRequest.status:type_name -> attendancepb.AttendanceStatus
	1,  // 9: attendancepb.AttendanceService.CreateAttendance:input_type -> attendancepb.AttendanceRequest
	3,  // 10: attendancepb.AttendanceService.ListAttendance:input_type -> attendancepb.ListAttendanceRequest
	5,  // 11: attendancepb.AttendanceService.UpdateAttendance:input_type -> attendancepb.UpdateAttendanceRequest
	6,  // 12: attendancepb.AttendanceService.DeleteAttendanceByID:input_type -> attendancepb.AttendanceIDRequest
	7,  // 13: attendancepb.AttendanceService.DeleteAttendancesByThreadUser:input_type -> attendancepb.AttendancesByThreadUser
	2,  // 14: attendancepb.AttendanceService.CreateAttendance:output_type -> attendancepb.AttendanceResponse
	4,  // 15: attendancepb.AttendanceService.ListAttendance:output_type -> attendancepb.ListAttendanceResponse
	2,  // 16: attendancepb.AttendanceService.UpdateAttendance:output_type -> attendancepb.AttendanceResponse
	8,  // 17: attendancepb.AttendanceService.DeleteAttendanceByID:output_type -> attendancepb.AttendanceEmptyResponse
	8,  // 18: attendancepb.AttendanceService.DeleteAttendancesByThreadUser:output_type -> attendancepb.AttendanceEmptyResponse
	14, // [14:19] is the sub-list for method output_type
	9,  // [9:14] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_pb_attendance_attendance_proto_init() }
func file_pb_attendance_attendance_proto_init() {
	if File_pb_attendance_attendance_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pb_attendance_attendance_proto_rawDesc), len(file_pb_attendance_attendance_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_attendance_attendance_proto_goTypes,
		DependencyIndexes: file_pb_attendance_attendance_proto_depIdxs,
		EnumInfos:         file_pb_attendance_attendance_proto_enumTypes,
		MessageInfos:      file_pb_attendance_attendance_proto_msgTypes,
	}.Build()
	File_pb_attendance_attendance_proto = out.File
	file_pb_attendance_attendance_proto_goTypes = nil
	file_pb_attendance_attendance_proto_depIdxs = nil
}
