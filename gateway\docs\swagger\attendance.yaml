openapi: 3.0.0
info:
  title: Edunite Attendance API
  description: API documentation for the Attendance endpoints of the Edunite platform
  version: 1.0.0
  contact:
    name: Edunite Support
    email: <EMAIL>

servers:
  - url: http://localhost:8081
    description: Local development server
  - url: https://api.edunite.com
    description: Production server

tags:
  - name: attendance
    description: Attendance operations

paths:
  /attendance:
    post:
      tags:
        - attendance
      summary: Create attendance record
      description: Creates a new attendance record for a user in a thread
      operationId: createAttendance
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - thread_id
                - user_id
                - attendance_date
                - status
              properties:
                thread_id:
                  type: integer
                  format: int64
                  description: ID of the thread for the attendance record
                  example: 1
                user_id:
                  type: integer
                  format: int64
                  description: ID of the user for the attendance record
                  example: 1
                attendance_date:
                  type: string
                  format: date
                  description: Date of the attendance record (YYYY-MM-DD)
                  example: "2023-01-15"
                status:
                  type: integer
                  description: Attendance status (0 = Present, 1 = Absent, 2 = Late, 3 = Excused)
                  enum: [0, 1, 2, 3]
                  example: 0
                reason:
                  type: string
                  description: Reason for absence or late arrival (optional)
                  example: "Doctor's appointment"
      responses:
        '201':
          description: Attendance record created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Attendance'
        '400':
          description: Invalid request body
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    get:
      tags:
        - attendance
      summary: List attendance records
      description: Returns attendance records for a thread on a specific date
      operationId: listAttendance
      parameters:
        - name: thread_id
          in: query
          required: true
          description: ID of the thread to list attendance records for
          schema:
            type: integer
            format: int64
        - name: attendance_date
          in: query
          required: true
          description: Date to list attendance records for (YYYY-MM-DD)
          schema:
            type: string
            format: date
      responses:
        '200':
          description: List of attendance records
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Attendance'
        '400':
          description: Invalid query parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /attendance/{id}:
    put:
      tags:
        - attendance
      summary: Update attendance record
      description: Updates an existing attendance record
      operationId: updateAttendance
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the attendance record to update
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - status
              properties:
                status:
                  type: integer
                  description: Updated attendance status (0 = Present, 1 = Absent, 2 = Late, 3 = Excused)
                  enum: [0, 1, 2, 3]
                  example: 2
                reason:
                  type: string
                  description: Updated reason for absence or late arrival
                  example: "Traffic delay"
      responses:
        '200':
          description: Attendance record updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Attendance'
        '400':
          description: Invalid request body or attendance ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Attendance record not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    delete:
      tags:
        - attendance
      summary: Delete attendance record
      description: Deletes an attendance record by its ID
      operationId: deleteAttendance
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the attendance record to delete
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Attendance record deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Attendance record deleted successfully"
        '400':
          description: Invalid attendance ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Attendance record not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Attendance:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        thread_id:
          type: integer
          format: int64
          example: 1
        user_id:
          type: integer
          format: int64
          example: 1
        attendance_date:
          type: string
          format: date
          example: "2023-01-15"
        status:
          type: integer
          description: Attendance status (0 = Present, 1 = Absent, 2 = Late, 3 = Excused)
          enum: [0, 1, 2, 3]
          example: 0
        reason:
          type: string
          example: "Doctor's appointment"
        created_at:
          type: string
          format: date-time
          example: "2023-01-15T12:00:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2023-01-15T12:00:00Z"

    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
          example: "Invalid request body"
