syntax = "proto3";

package transcript;

option go_package = "github.com/edunite/course_service/pb/transcript";

import "google/protobuf/timestamp.proto";

// Enums
enum AcademicStanding {
    GOOD_STANDING = 0;
    ACADEMIC_WARNING = 1;
    ACADEMIC_PROBATION = 2;
    ACADEMIC_SUSPENSION = 3;
    DISMISSED = 4;
}

enum DegreeLevel {
    BACHELOR = 0;
    MASTER = 1;
    PHD = 2;
    CERTIFICATE = 3;
    DIPLOMA = 4;
}

enum DegreeStatus {
    IN_PROGRESS = 0;
    COMPLETED = 1;
    WITHDRAWN = 2;
    TRANSFERRED = 3;
}

enum GradeScale {
    LETTER = 0;
    NUMERIC = 1;
    GPA_4_0 = 2;
    PASS_FAIL = 3;
}

// Messages
message Degree {
    int64 id = 1;
    string name = 2;
    DegreeLevel level = 3;
    string description = 4;
    int32 required_credits = 5;
    double min_gpa = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
}

message AcademicTranscript {
    int64 id = 1;
    int64 user_id = 2;
    int64 degree_id = 3;
    double cumulative_gpa = 4;
    int32 total_credits_attempted = 5;
    int32 total_credits_earned = 6;
    AcademicStanding academic_standing = 7;
    google.protobuf.Timestamp graduation_date = 8;
    google.protobuf.Timestamp created_at = 9;
    google.protobuf.Timestamp updated_at = 10;
    Degree degree = 11; // populated when needed
}

message TranscriptEntry {
    int64 id = 1;
    int64 transcript_id = 2;
    int64 course_id = 3;
    int64 thread_id = 4;
    int64 semester_id = 5;
    string grade_letter = 6;
    double grade_numeric = 7;
    double grade_points = 8;
    int32 credits = 9;
    bool is_transfer = 10;
    bool is_repeated = 11;
    google.protobuf.Timestamp completion_date = 12;
    google.protobuf.Timestamp created_at = 13;
    google.protobuf.Timestamp updated_at = 14;
    // Additional fields for display
    string course_title = 15;
    string semester_name = 16;
}

message StudentDegree {
    int64 id = 1;
    int64 user_id = 2;
    int64 degree_id = 3;
    DegreeStatus status = 4;
    google.protobuf.Timestamp start_date = 5;
    google.protobuf.Timestamp expected_graduation_date = 6;
    google.protobuf.Timestamp actual_graduation_date = 7;
    double final_gpa = 8;
    google.protobuf.Timestamp created_at = 9;
    google.protobuf.Timestamp updated_at = 10;
    Degree degree = 11; // populated when needed
}

// Request/Response messages
message CreateDegreeRequest {
    string name = 1;
    DegreeLevel level = 2;
    string description = 3;
    int32 required_credits = 4;
    double min_gpa = 5;
}

message DegreeResponse {
    Degree degree = 1;
}

message GetDegreeRequest {
    int64 id = 1;
}

message ListDegreesRequest {
    int32 page = 1;
    int32 page_size = 2;
    DegreeLevel level = 3; // optional filter
}

message ListDegreesResponse {
    repeated Degree degrees = 1;
    int32 total_count = 2;
}

message CreateTranscriptRequest {
    int64 user_id = 1;
    int64 degree_id = 2;
}

message TranscriptResponse {
    AcademicTranscript transcript = 1;
}

message GetTranscriptRequest {
    int64 user_id = 1;
    int64 degree_id = 2; // optional, if not provided returns primary transcript
}

message AddTranscriptEntryRequest {
    int64 transcript_id = 1;
    int64 course_id = 2;
    int64 thread_id = 3;
    int64 semester_id = 4;
    string grade_letter = 5;
    double grade_numeric = 6;
    double grade_points = 7;
    int32 credits = 8;
    bool is_transfer = 9;
    bool is_repeated = 10;
    google.protobuf.Timestamp completion_date = 11;
}

message TranscriptEntryResponse {
    TranscriptEntry entry = 1;
}

message GetTranscriptEntriesRequest {
    int64 transcript_id = 1;
    int64 semester_id = 2; // optional filter
}

message GetTranscriptEntriesResponse {
    repeated TranscriptEntry entries = 1;
}

message UpdateGPARequest {
    int64 transcript_id = 1;
}

message CalculateGPARequest {
    int64 user_id = 1;
    int64 degree_id = 2; // optional
}

message GPAResponse {
    double gpa = 1;
    int32 total_credits_attempted = 2;
    int32 total_credits_earned = 3;
}

message CreateStudentDegreeRequest {
    int64 user_id = 1;
    int64 degree_id = 2;
    google.protobuf.Timestamp start_date = 3;
    google.protobuf.Timestamp expected_graduation_date = 4;
}

message StudentDegreeResponse {
    StudentDegree student_degree = 1;
}

message GetStudentDegreesRequest {
    int64 user_id = 1;
}

message GetStudentDegreesResponse {
    repeated StudentDegree student_degrees = 1;
}

message ListStudentDegreesRequest {
    int32 page = 1;
    int32 page_size = 2;
    int64 user_id = 3; // optional filter by user
    int64 degree_id = 4; // optional filter by degree
    string status = 5; // optional filter by status
}

message ListStudentDegreesResponse {
    repeated StudentDegree student_degrees = 1;
    int32 total_count = 2;
}

message UpdateDegreeStatusRequest {
    int64 student_degree_id = 1;
    DegreeStatus status = 2;
    google.protobuf.Timestamp actual_graduation_date = 3; // required if status is COMPLETED
    double final_gpa = 4; // required if status is COMPLETED
}

message GenerateTranscriptReportRequest {
    int64 user_id = 1;
    int64 degree_id = 2; // optional
    bool include_transfer_credits = 3;
    bool include_repeated_courses = 4;
}

message TranscriptReport {
    AcademicTranscript transcript = 1;
    repeated TranscriptEntry entries = 2;
    StudentDegree student_degree = 3;
    double semester_gpa = 4; // current semester GPA
    string academic_standing_description = 5;
}

// Service definition
service TranscriptService {
    // Degree management
    rpc CreateDegree(CreateDegreeRequest) returns (DegreeResponse);
    rpc GetDegree(GetDegreeRequest) returns (DegreeResponse);
    rpc ListDegrees(ListDegreesRequest) returns (ListDegreesResponse);
    
    // Transcript management
    rpc CreateTranscript(CreateTranscriptRequest) returns (TranscriptResponse);
    rpc GetTranscript(GetTranscriptRequest) returns (TranscriptResponse);
    rpc AddTranscriptEntry(AddTranscriptEntryRequest) returns (TranscriptEntryResponse);
    rpc GetTranscriptEntries(GetTranscriptEntriesRequest) returns (GetTranscriptEntriesResponse);
    rpc UpdateGPA(UpdateGPARequest) returns (TranscriptResponse);
    rpc CalculateGPA(CalculateGPARequest) returns (GPAResponse);
    
    // Student degree management
    rpc CreateStudentDegree(CreateStudentDegreeRequest) returns (StudentDegreeResponse);
    rpc GetStudentDegrees(GetStudentDegreesRequest) returns (GetStudentDegreesResponse);
    rpc ListStudentDegrees(ListStudentDegreesRequest) returns (ListStudentDegreesResponse);
    rpc UpdateDegreeStatus(UpdateDegreeStatusRequest) returns (StudentDegreeResponse);
    
    // Reports
    rpc GenerateTranscriptReport(GenerateTranscriptReportRequest) returns (TranscriptReport);
}
