package postgres

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

type bookingRepository struct {
	db *pgxpool.Pool
}

func (r *bookingRepository) CancelBooking(ctx context.Context, id int64) error {
	//TODO implement me
	panic("implement me")
}

// NewBookingRepository creates a new booking repository
func NewBookingRepository(db *pgxpool.Pool) *bookingRepository {
	return &bookingRepository{db: db}
}

// Create creates a new booking
func (r *bookingRepository) Create(ctx context.Context, booking *domain.Booking) error {
	query := `
		INSERT INTO bookings (schedule_id, user_id, status)
		VALUES ($1, $2, $3)
		RETURNING id, created_at, updated_at
	`

	err := r.db.QueryRow(ctx, query,
		booking.ScheduleID,
		booking.UserID,
		booking.Status,
	).Scan(
		&booking.ID,
		&booking.CreatedAt,
		&booking.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to create booking: %w", err)
	}

	return nil
}

// GetByID retrieves a booking by ID
func (r *bookingRepository) GetByID(ctx context.Context, id int64) (*domain.Booking, error) {
	query := `
		SELECT id, schedule_id, user_id, status, created_at, updated_at
		FROM bookings
		WHERE id = $1
	`

	var booking domain.Booking
	err := r.db.QueryRow(ctx, query, id).Scan(
		&booking.ID,
		&booking.ScheduleID,
		&booking.UserID,
		&booking.Status,
		&booking.CreatedAt,
		&booking.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, domain.ErrBookingNotFound
		}
		return nil, fmt.Errorf("failed to get booking: %w", err)
	}

	return &booking, nil
}

// Update updates an existing booking
func (r *bookingRepository) Update(ctx context.Context, booking *domain.Booking) error {
	query := `
		UPDATE bookings
		SET status = $1
		WHERE id = $2
		RETURNING updated_at
	`

	err := r.db.QueryRow(ctx, query,
		booking.Status,
		booking.ID,
	).Scan(
		&booking.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return domain.ErrBookingNotFound
		}
		return fmt.Errorf("failed to update booking: %w", err)
	}

	return nil
}

// Delete deletes a booking by ID
func (r *bookingRepository) Delete(ctx context.Context, id int64) error {
	query := `
		DELETE FROM bookings
		WHERE id = $1
	`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete booking: %w", err)
	}

	if result.RowsAffected() == 0 {
		return domain.ErrBookingNotFound
	}

	return nil
}

// List retrieves bookings based on filters
func (r *bookingRepository) List(ctx context.Context, filter domain.BookingFilter) ([]*domain.Booking, error) {
	query := `
		SELECT b.id, b.schedule_id, b.user_id, b.status, b.created_at, b.updated_at
		FROM bookings b
		JOIN sport_schedules s ON b.schedule_id = s.id
		WHERE 1=1
	`

	var args []interface{}
	var conditions []string

	// Add filter conditions
	if filter.ScheduleID != 0 {
		args = append(args, filter.ScheduleID)
		conditions = append(conditions, fmt.Sprintf("AND b.schedule_id = $%d", len(args)))
	}

	if filter.UserID != 0 {
		args = append(args, filter.UserID)
		conditions = append(conditions, fmt.Sprintf("AND b.user_id = $%d", len(args)))
	}

	if filter.Status != "" {
		args = append(args, filter.Status)
		conditions = append(conditions, fmt.Sprintf("AND b.status = $%d", len(args)))
	}

	if !filter.StartDate.IsZero() {
		args = append(args, filter.StartDate)
		conditions = append(conditions, fmt.Sprintf("AND s.start_time >= $%d", len(args)))
	}

	if !filter.EndDate.IsZero() {
		args = append(args, filter.EndDate)
		conditions = append(conditions, fmt.Sprintf("AND s.start_time <= $%d", len(args)))
	}

	// Add pagination
	if filter.Page <= 0 {
		filter.Page = 1
	}

	if filter.PageSize <= 0 {
		filter.PageSize = 10
	}

	offset := (filter.Page - 1) * filter.PageSize

	for _, condition := range conditions {
		query += " " + condition
	}

	query += " ORDER BY s.start_time"
	query += fmt.Sprintf(" LIMIT %d OFFSET %d", filter.PageSize, offset)

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to list bookings: %w", err)
	}
	defer rows.Close()

	var bookings []*domain.Booking
	for rows.Next() {
		var booking domain.Booking
		err := rows.Scan(
			&booking.ID,
			&booking.ScheduleID,
			&booking.UserID,
			&booking.Status,
			&booking.CreatedAt,
			&booking.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan booking: %w", err)
		}
		bookings = append(bookings, &booking)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating bookings: %w", err)
	}

	return bookings, nil
}

// Count counts bookings based on filters
func (r *bookingRepository) Count(ctx context.Context, filter domain.BookingFilter) (int, error) {
	query := `
		SELECT COUNT(*)
		FROM bookings b
		JOIN sport_schedules s ON b.schedule_id = s.id
		WHERE 1=1
	`

	var args []interface{}
	var conditions []string

	// Add filter conditions
	if filter.ScheduleID != 0 {
		args = append(args, filter.ScheduleID)
		conditions = append(conditions, fmt.Sprintf("AND b.schedule_id = $%d", len(args)))
	}

	if filter.UserID != 0 {
		args = append(args, filter.UserID)
		conditions = append(conditions, fmt.Sprintf("AND b.user_id = $%d", len(args)))
	}

	if filter.Status != "" {
		args = append(args, filter.Status)
		conditions = append(conditions, fmt.Sprintf("AND b.status = $%d", len(args)))
	}

	if !filter.StartDate.IsZero() {
		args = append(args, filter.StartDate)
		conditions = append(conditions, fmt.Sprintf("AND s.start_time >= $%d", len(args)))
	}

	if !filter.EndDate.IsZero() {
		args = append(args, filter.EndDate)
		conditions = append(conditions, fmt.Sprintf("AND s.start_time <= $%d", len(args)))
	}

	for _, condition := range conditions {
		query += " " + condition
	}

	var count int
	err := r.db.QueryRow(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count bookings: %w", err)
	}

	return count, nil
}

// GetByUserAndSchedule retrieves a booking by user ID and schedule ID
func (r *bookingRepository) GetByUserAndSchedule(ctx context.Context, userID, scheduleID int64) (*domain.Booking, error) {
	query := `
		SELECT id, schedule_id, user_id, status, created_at, updated_at
		FROM bookings
		WHERE user_id = $1 AND schedule_id = $2
	`

	var booking domain.Booking
	err := r.db.QueryRow(ctx, query, userID, scheduleID).Scan(
		&booking.ID,
		&booking.ScheduleID,
		&booking.UserID,
		&booking.Status,
		&booking.CreatedAt,
		&booking.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, domain.ErrBookingNotFound
		}
		return nil, fmt.Errorf("failed to get booking by user and schedule: %w", err)
	}

	return &booking, nil
}

// CountBySchedule counts bookings for a schedule
func (r *bookingRepository) CountBySchedule(ctx context.Context, scheduleID int64) (int, error) {
	query := `
		SELECT COUNT(*)
		FROM bookings
		WHERE schedule_id = $1 AND status IN ('pending', 'confirmed')
	`

	var count int
	err := r.db.QueryRow(ctx, query, scheduleID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count bookings by schedule: %w", err)
	}

	return count, nil
}

// GetUserBookingsForDate retrieves a user's bookings for a specific date
func (r *bookingRepository) GetUserBookingsForDate(ctx context.Context, userID int64, date time.Time) ([]*domain.Booking, error) {
	// Convert date to start and end of day
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)

	query := `
		SELECT b.id, b.schedule_id, b.user_id, b.status, b.created_at, b.updated_at
		FROM bookings b
		JOIN sport_schedules s ON b.schedule_id = s.id
		WHERE b.user_id = $1
		AND s.start_time >= $2
		AND s.start_time < $3
		AND b.status IN ('pending', 'confirmed')
		ORDER BY s.start_time
	`

	rows, err := r.db.Query(ctx, query, userID, startOfDay, endOfDay)
	if err != nil {
		return nil, fmt.Errorf("failed to get user bookings for date: %w", err)
	}
	defer rows.Close()

	var bookings []*domain.Booking
	for rows.Next() {
		var booking domain.Booking
		err := rows.Scan(
			&booking.ID,
			&booking.ScheduleID,
			&booking.UserID,
			&booking.Status,
			&booking.CreatedAt,
			&booking.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan booking: %w", err)
		}
		bookings = append(bookings, &booking)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating bookings: %w", err)
	}

	return bookings, nil
}

// CountUserBookingsInSemester counts a user's bookings in a semester (sportTypeID=0 means all sport types)
func (r *bookingRepository) CountUserBookingsInSemester(ctx context.Context, userID, semesterID, sportTypeID int64) (int, error) {
	var query string
	var args []interface{}

	if sportTypeID == 0 {
		// Count bookings for all sport types
		query = `
			SELECT COUNT(*)
			FROM bookings b
			JOIN sport_schedules s ON b.schedule_id = s.id
			WHERE b.user_id = $1
			AND s.semester_id = $2
			AND b.status IN ('confirmed', 'completed')
		`
		args = []interface{}{userID, semesterID}
	} else {
		// Count bookings for a specific sport type
		query = `
			SELECT COUNT(*)
			FROM bookings b
			JOIN sport_schedules s ON b.schedule_id = s.id
			JOIN facilities f ON s.facility_id = f.id
			WHERE b.user_id = $1
			AND s.semester_id = $2
			AND f.sport_type_id = $3
			AND b.status IN ('confirmed', 'completed')
		`
		args = []interface{}{userID, semesterID, sportTypeID}
	}

	var count int
	err := r.db.QueryRow(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count user bookings in semester: %w", err)
	}

	return count, nil
}

// GetStats retrieves booking statistics
func (r *bookingRepository) GetStats(ctx context.Context, filter domain.BookingFilter) (*domain.BookingStats, error) {
	query := `
		SELECT
			COUNT(*) AS total_bookings,
			COUNT(CASE WHEN status = 'pending' THEN 1 END) AS pending_bookings,
			COUNT(CASE WHEN status = 'confirmed' THEN 1 END) AS confirmed_bookings,
			COUNT(CASE WHEN status = 'cancelled' THEN 1 END) AS cancelled_bookings,
			COUNT(CASE WHEN status = 'completed' THEN 1 END) AS completed_bookings
		FROM bookings b
		JOIN sport_schedules s ON b.schedule_id = s.id
		WHERE 1=1
	`

	var args []interface{}
	var conditions []string

	// Add filter conditions
	if filter.ScheduleID != 0 {
		args = append(args, filter.ScheduleID)
		conditions = append(conditions, fmt.Sprintf("AND b.schedule_id = $%d", len(args)))
	}

	if filter.UserID != 0 {
		args = append(args, filter.UserID)
		conditions = append(conditions, fmt.Sprintf("AND b.user_id = $%d", len(args)))
	}

	if !filter.StartDate.IsZero() {
		args = append(args, filter.StartDate)
		conditions = append(conditions, fmt.Sprintf("AND s.start_time >= $%d", len(args)))
	}

	if !filter.EndDate.IsZero() {
		args = append(args, filter.EndDate)
		conditions = append(conditions, fmt.Sprintf("AND s.start_time <= $%d", len(args)))
	}

	for _, condition := range conditions {
		query += " " + condition
	}

	var stats domain.BookingStats
	err := r.db.QueryRow(ctx, query, args...).Scan(
		&stats.TotalBookings,
		&stats.PendingBookings,
		&stats.ConfirmedBookings,
		&stats.CancelledBookings,
		&stats.CompletedBookings,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get booking stats: %w", err)
	}

	return &stats, nil
}
