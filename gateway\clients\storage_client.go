package clients

import (
	"context"

	storagepb "github.com/olzzhas/edunite-server/storage_service/pb"
	"google.golang.org/grpc"
)

// StorageClient представляет клиент для взаимодействия со Storage Service через gRPC
type StorageClient struct {
	client storagepb.StorageServiceClient
}

// NewStorageClient создает новый экземпляр StorageClient с подключением к gRPC
func NewStorageClient(conn *grpc.ClientConn) *StorageClient {
	return &StorageClient{
		client: storagepb.NewStorageServiceClient(conn),
	}
}

// UploadFile загружает файл в MinIO (через StorageService)
func (c *StorageClient) UploadFile(ctx context.Context, bucketName, objectName, contentType string, fileData []byte) (storagepb.UploadFileResponse, error) {
	req := &storagepb.UploadFileRequest{
		BucketName:  bucketName,
		ObjectName:  objectName,
		ContentType: contentType,
		FileData:    fileData,
	}
	resp, err := c.client.UploadFile(ctx, req)
	if err != nil {
		return storagepb.UploadFileResponse{}, err
	}
	return *resp, nil
}

// DownloadFile скачивает файл из MinIO (через StorageService)
func (c *StorageClient) DownloadFile(ctx context.Context, bucketName, objectName string) (storagepb.DownloadFileResponse, error) {
	req := &storagepb.DownloadFileRequest{
		BucketName: bucketName,
		ObjectName: objectName,
	}
	resp, err := c.client.DownloadFile(ctx, req)
	if err != nil {
		return storagepb.DownloadFileResponse{}, err
	}
	return *resp, nil
}

// DeleteFile удаляет файл в MinIO (через StorageService)
func (c *StorageClient) DeleteFile(ctx context.Context, bucketName, objectName string) error {
	req := &storagepb.DeleteFileRequest{
		BucketName: bucketName,
		ObjectName: objectName,
	}
	_, err := c.client.DeleteFile(ctx, req)
	return err
}
