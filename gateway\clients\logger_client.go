package clients

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/olzzhas/edunite-server/gateway/loggerpb"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// LoggerClient представляет клиент для взаимодействия с Logger Service через gRPC
type LoggerClient struct {
	conn   *grpc.ClientConn
	client loggerpb.LoggerServiceClient
}

// LogEntry представляет запись лога, полученную из Logger Service
type LogEntry struct {
	ID       string            `json:"id"`
	Level    string            `json:"level"`
	Message  string            `json:"message"`
	Service  string            `json:"service"`
	Data     map[string]string `json:"data,omitempty"`
	Datetime time.Time         `json:"datetime"`
}

// LogFilter представляет фильтр для получения логов
type LogFilter struct {
	Level     string
	Service   string
	StartDate time.Time
	EndDate   time.Time
	Limit     int64
	Skip      int64
}

// NewLoggerClient создает новый экземпляр LoggerClient с подключением к gRPC
func NewLoggerClient(conn *grpc.ClientConn) *LoggerClient {
	return &LoggerClient{
		conn:   conn,
		client: loggerpb.NewLoggerServiceClient(conn),
	}
}

// WriteLog записывает лог через gRPC
func (lc *LoggerClient) WriteLog(level, message, serviceName string, data map[string]string) error {
	// Create gRPC request
	req := &loggerpb.WriteLogRequest{
		Level:    level,
		Message:  message,
		Service:  serviceName,
		Data:     data,
		Datetime: timestamppb.New(time.Now().UTC()),
	}

	// Call gRPC method
	_, err := lc.client.WriteLog(context.Background(), req)
	if err != nil {
		// Fallback to stdout if gRPC call fails
		logData := map[string]interface{}{
			"level":    level,
			"message":  message,
			"service":  serviceName,
			"data":     data,
			"datetime": time.Now().UTC().Format(time.RFC3339),
		}

		jsonData, _ := json.Marshal(logData)
		log.Printf("LOG (fallback): %s\n", string(jsonData))
	}

	return err
}

// GetLogs получает логи из MongoDB по заданным критериям
func (lc *LoggerClient) GetLogs(ctx context.Context, filter LogFilter) ([]LogEntry, error) {
	// Create gRPC request
	req := &loggerpb.GetLogsRequest{
		Level:   filter.Level,
		Service: filter.Service,
		Limit:   filter.Limit,
		Skip:    filter.Skip,
	}

	// Add start date if provided
	if !filter.StartDate.IsZero() {
		req.StartDate = timestamppb.New(filter.StartDate)
	}

	// Add end date if provided
	if !filter.EndDate.IsZero() {
		req.EndDate = timestamppb.New(filter.EndDate)
	}

	// Call gRPC method
	resp, err := lc.client.GetLogs(ctx, req)
	if err != nil {
		return nil, err
	}

	// Convert response to LogEntry slice
	logs := make([]LogEntry, len(resp.Logs))
	for i, log := range resp.Logs {
		logs[i] = convertPbLogToLogEntry(log)
	}

	return logs, nil
}

// GetLogByID получает лог по его ID
func (lc *LoggerClient) GetLogByID(ctx context.Context, id string) (*LogEntry, error) {
	// Create gRPC request
	req := &loggerpb.GetLogByIDRequest{
		Id: id,
	}

	// Call gRPC method
	resp, err := lc.client.GetLogByID(ctx, req)
	if err != nil {
		return nil, err
	}

	// Convert response to LogEntry
	log := convertPbLogToLogEntry(resp)
	return &log, nil
}

// CountLogs подсчитывает количество логов по заданным критериям
func (lc *LoggerClient) CountLogs(ctx context.Context, filter LogFilter) (int64, error) {
	// Create gRPC request
	req := &loggerpb.GetLogsRequest{
		Level:   filter.Level,
		Service: filter.Service,
	}

	// Add start date if provided
	if !filter.StartDate.IsZero() {
		req.StartDate = timestamppb.New(filter.StartDate)
	}

	// Add end date if provided
	if !filter.EndDate.IsZero() {
		req.EndDate = timestamppb.New(filter.EndDate)
	}

	// Call gRPC method
	resp, err := lc.client.CountLogs(ctx, req)
	if err != nil {
		return 0, err
	}

	return resp.Count, nil
}

// GetLogLevels получает все уникальные уровни логов
func (lc *LoggerClient) GetLogLevels(ctx context.Context) ([]string, error) {
	// Create gRPC request
	req := &loggerpb.GetLogLevelsRequest{}

	// Call gRPC method
	resp, err := lc.client.GetLogLevels(ctx, req)
	if err != nil {
		return nil, err
	}

	return resp.Levels, nil
}

// GetLogServices получает все уникальные имена сервисов
func (lc *LoggerClient) GetLogServices(ctx context.Context) ([]string, error) {
	// Create gRPC request
	req := &loggerpb.GetLogServicesRequest{}

	// Call gRPC method
	resp, err := lc.client.GetLogServices(ctx, req)
	if err != nil {
		return nil, err
	}

	return resp.Services, nil
}

// Helper function to convert protobuf log to LogEntry
func convertPbLogToLogEntry(pbLog *loggerpb.LogEntry) LogEntry {
	// Convert data map
	data := make(map[string]string)
	for k, v := range pbLog.Data {
		data[k] = v
	}

	// Convert timestamp to time.Time
	var datetime time.Time
	if pbLog.Datetime != nil {
		datetime = pbLog.Datetime.AsTime()
	}

	return LogEntry{
		ID:       pbLog.Id,
		Level:    pbLog.Level,
		Message:  pbLog.Message,
		Service:  pbLog.Service,
		Datetime: datetime,
		Data:     data,
	}
}
