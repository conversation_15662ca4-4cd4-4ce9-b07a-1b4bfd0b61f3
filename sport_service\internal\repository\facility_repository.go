package repository

import (
	"context"
	
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

// FacilityRepository defines the interface for facility data access
type FacilityRepository interface {
	// Create creates a new facility
	Create(ctx context.Context, facility *domain.Facility) error
	
	// GetByID retrieves a facility by ID
	GetByID(ctx context.Context, id int64) (*domain.Facility, error)
	
	// Update updates an existing facility
	Update(ctx context.Context, facility *domain.Facility) error
	
	// Delete deletes a facility by ID
	Delete(ctx context.Context, id int64) error
	
	// List retrieves facilities based on filters
	List(ctx context.Context, filter domain.FacilityFilter) ([]*domain.Facility, error)
	
	// Count counts facilities based on filters
	Count(ctx context.Context, filter domain.FacilityFilter) (int, error)
	
	// GetBySportType retrieves facilities by sport type ID
	GetBySportType(ctx context.Context, sportTypeID int64) ([]*domain.Facility, error)
}
