package database

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/olzzhas/edunite-server/storage_service/internal/config"

	"github.com/minio/minio-go/v7/pkg/credentials"
)

// MinioRepository - интерфейс хранения в MinIO
type MinioRepository interface {
	Upload(ctx context.Context, bucketName, objectName string, data []byte, contentType string) (string, error)
	Download(ctx context.Context, bucketName, objectName string) ([]byte, string, error)
	Delete(ctx context.Context, bucketName, objectName string) error
}

type minioRepository struct {
	client *minio.Client
	config *config.Config
}

// DefaultBuckets defines the default buckets to create on startup
var DefaultBuckets = []string{
	"assignments",
	"profiles",
	"courses",
	"submissions",
	"attachments",
}

// NewMinioRepository конструктор репозитория
func NewMinioRepository(cfg *config.Config) (MinioRepository, error) {
	minioClient, err := minio.New(cfg.MinioEndpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(cfg.MinioAccessKey, cfg.MinioSecretKey, ""),
		Secure: cfg.MinioUseSSL,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create minio client: %w", err)
	}

	repo := &minioRepository{
		client: minioClient,
		config: cfg,
	}

	// Initialize default buckets with retries
	if err := repo.initializeDefaultBuckets(); err != nil {
		return nil, fmt.Errorf("failed to initialize default buckets: %w", err)
	}

	return repo, nil
}

// initializeDefaultBuckets creates default buckets and sets policies
func (r *minioRepository) initializeDefaultBuckets() error {
	ctx := context.Background()

	// Define retry parameters
	maxRetries := 5
	retryDelay := time.Second * 3

	// Try to initialize buckets with retries
	var lastErr error
	for attempt := 1; attempt <= maxRetries; attempt++ {
		if err := r.createDefaultBuckets(ctx); err != nil {
			lastErr = err
			fmt.Printf("Attempt %d/%d to initialize MinIO buckets failed: %v. Retrying in %v...\n",
				attempt, maxRetries, err, retryDelay)
			time.Sleep(retryDelay)
			continue
		}
		// If successful, return nil
		fmt.Println("Successfully initialized MinIO buckets")
		return nil
	}

	// If we get here, all retries failed
	return fmt.Errorf("failed to initialize MinIO buckets after %d attempts: %w", maxRetries, lastErr)
}

// createDefaultBuckets creates the default buckets and sets their policies
func (r *minioRepository) createDefaultBuckets(ctx context.Context) error {
	// Create each default bucket if it doesn't exist
	for _, bucketName := range DefaultBuckets {
		exists, err := r.client.BucketExists(ctx, bucketName)
		if err != nil {
			return fmt.Errorf("error checking bucket %s: %w", bucketName, err)
		}

		if !exists {
			fmt.Printf("Creating bucket: %s\n", bucketName)
			if err := r.client.MakeBucket(ctx, bucketName, minio.MakeBucketOptions{}); err != nil {
				return fmt.Errorf("cannot create bucket %s: %w", bucketName, err)
			}

			// Set bucket policy for public read access
			policy := `{
				"Version": "2012-10-17",
				"Statement": [
					{
						"Effect": "Allow",
						"Principal": {"AWS": ["*"]},
						"Action": ["s3:GetObject"],
						"Resource": ["arn:aws:s3:::%s/*"]
					}
				]
			}`

			policy = fmt.Sprintf(policy, bucketName)

			fmt.Printf("Setting public read policy for bucket: %s\n", bucketName)
			if err := r.client.SetBucketPolicy(ctx, bucketName, policy); err != nil {
				return fmt.Errorf("cannot set bucket policy for %s: %w", bucketName, err)
			}
		} else {
			fmt.Printf("Bucket already exists: %s\n", bucketName)
		}
	}

	return nil
}

// Upload загружает файл в bucket
func (r *minioRepository) Upload(ctx context.Context, bucketName, objectName string, data []byte, contentType string) (string, error) {
	reader := bytes.NewReader(data)
	fileSize := int64(len(data))

	// Убедимся, что bucket существует (или создадим)
	exists, errBucketExists := r.client.BucketExists(ctx, bucketName)
	if errBucketExists != nil {
		return "", fmt.Errorf("error checking bucket: %w", errBucketExists)
	}
	if !exists {
		if err := r.client.MakeBucket(ctx, bucketName, minio.MakeBucketOptions{}); err != nil {
			return "", fmt.Errorf("cannot create bucket: %w", err)
		}
	}

	uploadInfo, err := r.client.PutObject(ctx, bucketName, objectName, reader, fileSize, minio.PutObjectOptions{
		ContentType: contentType,
	})
	if err != nil {
		return "", fmt.Errorf("failed to upload object: %w", err)
	}

	// Сформируем URL (если MinIO настроен на публичный доступ) или вернём путь
	// Use the appropriate endpoint based on environment
	var fileURL string

	// If we're in production, use the public endpoint
	if r.config.Environment == "production" {
		protocol := "http"
		if r.config.MinioUseSSL {
			protocol = "https"
		}
		fileURL = fmt.Sprintf("%s://%s/%s/%s", protocol, r.config.PublicMinioEndpoint, bucketName, uploadInfo.Key)
	} else {
		// In development, use localhost with the exposed port
		fileURL = fmt.Sprintf("http://localhost:9010/%s/%s", bucketName, uploadInfo.Key)
	}

	return fileURL, nil
}

// Download скачивает файл из bucket
func (r *minioRepository) Download(ctx context.Context, bucketName, objectName string) ([]byte, string, error) {
	obj, err := r.client.GetObject(ctx, bucketName, objectName, minio.GetObjectOptions{})
	if err != nil {
		return nil, "", fmt.Errorf("failed to get object: %w", err)
	}
	defer obj.Close()

	stat, err := obj.Stat()
	if err != nil {
		return nil, "", fmt.Errorf("object stat error: %w", err)
	}

	data, err := io.ReadAll(obj)
	if err != nil {
		return nil, "", fmt.Errorf("read object error: %w", err)
	}

	// ContentType можно взять из stat.ContentType()
	return data, stat.ContentType, nil
}

// Delete удаляет объект из bucket
func (r *minioRepository) Delete(ctx context.Context, bucketName, objectName string) error {
	err := r.client.RemoveObject(ctx, bucketName, objectName, minio.RemoveObjectOptions{})
	if err != nil {
		return fmt.Errorf("failed to remove object: %w", err)
	}
	return nil
}
