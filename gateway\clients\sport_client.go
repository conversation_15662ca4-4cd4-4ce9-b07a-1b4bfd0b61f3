package clients

import (
	"context"

	sportpb "github.com/olzzhas/edunite-server/sport_service/proto"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// SportTypeClient represents a client for interacting with the SportType service
type SportTypeClient struct {
	client sportpb.SportTypeServiceClient
}

// NewSportTypeClient creates a new SportTypeClient
func NewSportTypeClient(conn *grpc.ClientConn) *SportTypeClient {
	return &SportTypeClient{
		client: sportpb.NewSportTypeServiceClient(conn),
	}
}

// CreateSportType creates a new sport type
func (c *SportTypeClient) CreateSportType(ctx context.Context, title, description string, category sportpb.SportTypeCategory, requiresCertificate bool) (*sportpb.SportTypeResponse, error) {
	req := &sportpb.CreateSportTypeRequest{
		Title:               title,
		Description:         description,
		Category:            category,
		RequiresCertificate: requiresCertificate,
	}
	return c.client.CreateSportType(ctx, req)
}

// GetSportType gets a sport type by ID
func (c *SportTypeClient) GetSportType(ctx context.Context, id int64) (*sportpb.SportTypeResponse, error) {
	req := &sportpb.GetSportTypeRequest{
		Id: id,
	}
	return c.client.GetSportType(ctx, req)
}

// UpdateSportType updates a sport type
func (c *SportTypeClient) UpdateSportType(ctx context.Context, id int64, title, description string, category sportpb.SportTypeCategory, requiresCertificate bool) (*sportpb.SportTypeResponse, error) {
	req := &sportpb.UpdateSportTypeRequest{
		Id:                  id,
		Title:               title,
		Description:         description,
		Category:            category,
		RequiresCertificate: requiresCertificate,
	}
	return c.client.UpdateSportType(ctx, req)
}

// DeleteSportType deletes a sport type
func (c *SportTypeClient) DeleteSportType(ctx context.Context, id int64) error {
	req := &sportpb.DeleteSportTypeRequest{
		Id: id,
	}
	_, err := c.client.DeleteSportType(ctx, req)
	return err
}

// ListSportTypes lists all sport types
func (c *SportTypeClient) ListSportTypes(ctx context.Context, page, pageSize int32) (*sportpb.ListSportTypesResponse, error) {
	req := &sportpb.ListSportTypesRequest{
		Page:     page,
		PageSize: pageSize,
	}
	return c.client.ListSportTypes(ctx, req)
}

// FacilityClient represents a client for interacting with the Facility service
type FacilityClient struct {
	client sportpb.FacilityServiceClient
}

// NewFacilityClient creates a new FacilityClient
func NewFacilityClient(conn *grpc.ClientConn) *FacilityClient {
	return &FacilityClient{
		client: sportpb.NewFacilityServiceClient(conn),
	}
}

// CreateFacility creates a new facility
func (c *FacilityClient) CreateFacility(ctx context.Context, title, description string, capacity int32) (*sportpb.FacilityResponse, error) {
	req := &sportpb.CreateFacilityRequest{
		Title:       title,
		Description: description,
		MaxCapacity: capacity,
	}
	return c.client.CreateFacility(ctx, req)
}

// GetFacility gets a facility by ID
func (c *FacilityClient) GetFacility(ctx context.Context, id int64) (*sportpb.FacilityResponse, error) {
	req := &sportpb.GetFacilityRequest{
		Id: id,
	}
	return c.client.GetFacility(ctx, req)
}

// UpdateFacility updates a facility
func (c *FacilityClient) UpdateFacility(ctx context.Context, id int64, title, description string, capacity int32) (*sportpb.FacilityResponse, error) {
	req := &sportpb.UpdateFacilityRequest{
		Id:          id,
		Title:       title,
		Description: description,
		MaxCapacity: capacity,
	}
	return c.client.UpdateFacility(ctx, req)
}

// DeleteFacility deletes a facility
func (c *FacilityClient) DeleteFacility(ctx context.Context, id int64) error {
	req := &sportpb.DeleteFacilityRequest{
		Id: id,
	}
	_, err := c.client.DeleteFacility(ctx, req)
	return err
}

// ListFacilities lists all facilities
func (c *FacilityClient) ListFacilities(ctx context.Context, page, pageSize int32) (*sportpb.ListFacilitiesResponse, error) {
	req := &sportpb.ListFacilitiesRequest{
		Page:     page,
		PageSize: pageSize,
	}
	return c.client.ListFacilities(ctx, req)
}

// ScheduleClient represents a client for interacting with the Schedule service
type ScheduleClient struct {
	client sportpb.ScheduleServiceClient
}

// NewScheduleClient creates a new ScheduleClient
func NewScheduleClient(conn *grpc.ClientConn) *ScheduleClient {
	return &ScheduleClient{
		client: sportpb.NewScheduleServiceClient(conn),
	}
}

// CreateWeeklySchedules creates schedules from a weekly template
func (c *ScheduleClient) CreateWeeklySchedules(
	ctx context.Context,
	facilityID, teacherID, semesterID, sportTypeID int64, // Added sportTypeID parameter
	dayOfWeek int32,
	startTime, endTime string,
	location string,
	startDate, endDate *timestamppb.Timestamp,
) (*sportpb.CreateWeeklySchedulesResponse, error) {
	req := &sportpb.CreateWeeklySchedulesRequest{
		FacilityId:  facilityID,
		TeacherId:   teacherID,
		SemesterId:  semesterID,
		DayOfWeek:   dayOfWeek,
		SportTypeId: sportTypeID,
		StartTime:   startTime,
		EndTime:     endTime,
		Location:    location,
		StartDate:   startDate,
		EndDate:     endDate,
	}

	return c.client.CreateWeeklySchedules(ctx, req)
}

// GetSchedule gets a schedule by ID
func (c *ScheduleClient) GetSchedule(ctx context.Context, id int64) (*sportpb.ScheduleResponse, error) {
	req := &sportpb.GetScheduleRequest{
		Id: id,
	}
	return c.client.GetSchedule(ctx, req)
}

// ListSchedules lists schedules with filtering
func (c *ScheduleClient) ListSchedules(
	ctx context.Context,
	facilityID, teacherID, semesterID int64,
	startDate, endDate *timestamppb.Timestamp,
	location string,
	page, pageSize int32,
) (*sportpb.ListSchedulesResponse, error) {
	req := &sportpb.ListSchedulesRequest{
		FacilityId: facilityID,
		TeacherId:  teacherID,
		SemesterId: semesterID,
		StartDate:  startDate,
		EndDate:    endDate,
		Location:   location,
		Page:       page,
		PageSize:   pageSize,
	}
	return c.client.ListSchedules(ctx, req)
}

// GetSchedulesForTeacher gets schedules for a teacher within a time range
func (c *ScheduleClient) GetSchedulesForTeacher(
	ctx context.Context,
	teacherID int64,
	startDate, endDate *timestamppb.Timestamp,
) (*sportpb.ListSchedulesResponse, error) {
	req := &sportpb.GetSchedulesForTeacherRequest{
		TeacherId: teacherID,
		StartDate: startDate,
		EndDate:   endDate,
	}
	return c.client.GetSchedulesForTeacher(ctx, req)
}

// GetSchedulesForFacility gets schedules for a facility within a time range
func (c *ScheduleClient) GetSchedulesForFacility(
	ctx context.Context,
	facilityID int64,
	startDate, endDate *timestamppb.Timestamp,
) (*sportpb.ListSchedulesResponse, error) {
	req := &sportpb.GetSchedulesForFacilityRequest{
		FacilityId: facilityID,
		StartDate:  startDate,
		EndDate:    endDate,
	}
	return c.client.GetSchedulesForFacility(ctx, req)
}

// GetSchedulesForSemester gets schedules for a semester
func (c *ScheduleClient) GetSchedulesForSemester(
	ctx context.Context,
	semesterID int64,
) (*sportpb.ListSchedulesResponse, error) {
	req := &sportpb.GetSchedulesForSemesterRequest{
		SemesterId: semesterID,
	}
	return c.client.GetSchedulesForSemester(ctx, req)
}

// BookingClient represents a client for interacting with the Booking service
type BookingClient struct {
	client sportpb.BookingServiceClient
}

// NewBookingClient creates a new BookingClient
func NewBookingClient(conn *grpc.ClientConn) *BookingClient {
	return &BookingClient{
		client: sportpb.NewBookingServiceClient(conn),
	}
}

// ListBookingsByUser lists all bookings for a user within a date range
func (c *BookingClient) ListBookingsByUser(ctx context.Context, userID int64, startDate, endDate *timestamppb.Timestamp) (*sportpb.ListBookingsResponse, error) {
	req := &sportpb.ListUserBookingsRequest{
		UserId:    userID,
		StartDate: startDate,
		EndDate:   endDate,
		Page:      1,
		PageSize:  100, // Reasonable limit for schedule display
	}
	return c.client.ListUserBookings(ctx, req)
}

// MedicalCertificateClient represents a client for interacting with the MedicalCertificate service
type MedicalCertificateClient struct {
	client sportpb.MedicalCertificateServiceClient
}

// NewMedicalCertificateClient creates a new MedicalCertificateClient
func NewMedicalCertificateClient(conn *grpc.ClientConn) *MedicalCertificateClient {
	return &MedicalCertificateClient{
		client: sportpb.NewMedicalCertificateServiceClient(conn),
	}
}

// SemesterLimitClient represents a client for interacting with the SemesterLimit service
type SemesterLimitClient struct {
	client sportpb.SemesterLimitServiceClient
}

// NewSemesterLimitClient creates a new SemesterLimitClient
func NewSemesterLimitClient(conn *grpc.ClientConn) *SemesterLimitClient {
	return &SemesterLimitClient{
		client: sportpb.NewSemesterLimitServiceClient(conn),
	}
}

// PhysicalEducationClient represents a client for interacting with the PhysicalEducation service
type PhysicalEducationClient struct {
	client sportpb.PhysicalEducationServiceClient
}

// NewPhysicalEducationClient creates a new PhysicalEducationClient
func NewPhysicalEducationClient(conn *grpc.ClientConn) *PhysicalEducationClient {
	return &PhysicalEducationClient{
		client: sportpb.NewPhysicalEducationServiceClient(conn),
	}
}

// GetAvailableSportTypes gets available sport types
func (c *PhysicalEducationClient) GetAvailableSportTypes(ctx context.Context, page, pageSize int32) (*sportpb.ListSportTypesResponse, error) {
	req := &sportpb.GetAvailableSportTypesRequest{
		Page:     page,
		PageSize: pageSize,
	}
	return c.client.GetAvailableSportTypes(ctx, req)
}

// GetAvailableFacilities gets available facilities
func (c *PhysicalEducationClient) GetAvailableFacilities(ctx context.Context, page, pageSize int32) (*sportpb.ListFacilitiesResponse, error) {
	req := &sportpb.GetAvailableFacilitiesRequest{
		Page:     page,
		PageSize: pageSize,
	}
	return c.client.GetAvailableFacilities(ctx, req)
}

// BookSession books a session
func (c *PhysicalEducationClient) BookSession(ctx context.Context, userId, scheduleId int64) (*sportpb.BookingResponse, error) {
	req := &sportpb.BookSessionRequest{
		UserId:     userId,
		ScheduleId: scheduleId,
	}
	return c.client.BookSession(ctx, req)
}

// CancelBooking cancels a booking
func (c *PhysicalEducationClient) CancelBooking(ctx context.Context, bookingId, userId int64) (*sportpb.BookingResponse, error) {
	req := &sportpb.CancelBookingRequest{
		Id:     bookingId,
		UserId: userId,
	}
	return c.client.CancelBooking(ctx, req)
}
