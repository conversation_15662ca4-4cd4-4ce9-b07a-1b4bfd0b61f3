syntax = "proto3";

package coursepb;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/olzzhas/edunite-server/course_service/pb/course;coursepb";

service CourseService {
  // course entity
  rpc CreateCourse(CreateCourseRequest) returns (CourseResponse){}
  rpc GetCourseByID(GetCourseByIDRequest) returns (CourseResponse) {}
  rpc GetAllCourses(EmptyRequest) returns(CoursesResponse){}
  rpc UpdateCourse(UpdateCourseByIDRequest) returns (CourseResponse){}
  rpc DeleteCourse(DeleteCourseByIDRequest) returns (EmptyResponse){}
}

//-------------------------------//
//        Course Messages        //
//-------------------------------//
message CreateCourseRequest {
  string title = 1;
  string description = 2;
  string banner_image_url = 3;
  repeated int64 prerequisite_course_ids = 4;
}

message GetCourseByIDRequest {
  int64 id = 1;
}

message UpdateCourseByIDRequest {
  int64 id = 1;
  string title = 2;
  string description = 3;
  string banner_image_url = 4;
}

message DeleteCourseByIDRequest {
  int64 id = 1;
}

message CourseResponse {
  int64 id = 1;
  string title = 2;
  string description = 3;
  string banner_image_url = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
}

message CoursesResponse {
  repeated CourseResponse courses = 1;
}

//-------------------------------//
//       Empty Messages          //
//-------------------------------//
message EmptyRequest {}

message EmptyResponse {}