package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

func SetupUserRoutes(r *gin.Engine, authClient *clients.AuthClient, userHandler *handlers.UserHandler) {
	userGroup := r.Group("/user")
	//userGroup.Use(AuthMiddleware(authClient))
	{
		userGroup.GET("/users", userHandler.GetAllUsers)
		userGroup.GET("/users/:id", userHandler.GetUserByID)
		userGroup.PUT("/users/:id", userHandler.UpdateUser)
	}
}
