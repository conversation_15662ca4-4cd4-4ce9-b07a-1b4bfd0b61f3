package domain

import (
	"time"
)

// DailyBookingLimit represents the maximum number of bookings a user can have per day
type DailyBookingLimit struct {
	ID               int64     `json:"id"`
	SemesterID       int64     `json:"semester_id"`
	MaxBookingsPerDay int      `json:"max_bookings_per_day"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// Error definitions for daily booking limit operations
var (
	ErrDailyBookingLimitNotFound = Error{"daily booking limit not found"}
	ErrDuplicateDailyBookingLimit = Error{"daily booking limit already exists for this semester"}
)
