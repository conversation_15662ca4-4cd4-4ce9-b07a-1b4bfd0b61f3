package grpc

import (
	"context"
	"errors"
	sportpb "github.com/olzzhas/edunite-server/sport_service/proto"

	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
	"github.com/olzzhas/edunite-server/sport_service/internal/service"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type bookingServer struct {
	sportpb.UnimplementedBookingServiceServer
	services *service.Services
}

// NewBookingServer creates a new booking gRPC server
func NewBookingServer(services *service.Services) *bookingServer {
	return &bookingServer{
		services: services,
	}
}

// CreateBooking creates a new booking
func (s *bookingServer) CreateBooking(ctx context.Context, req *sportpb.CreateBookingRequest) (*sportpb.BookingResponse, error) {
	booking, err := s.services.Booking.CreateBooking(ctx, req.UserId, req.ScheduleId)
	if err != nil {
		if err == domain.ErrBookingClosed {
			return nil, status.Errorf(codes.FailedPrecondition, "booking is closed")
		}
		if err == domain.ErrScheduleFull {
			return nil, status.Errorf(codes.FailedPrecondition, "schedule is full")
		}
		if err == domain.ErrDuplicateBooking {
			return nil, status.Errorf(codes.AlreadyExists, "booking already exists")
		}
		if err == domain.ErrInvalidMedicalCertificate {
			return nil, status.Errorf(codes.FailedPrecondition, "invalid medical certificate")
		}
		if err == domain.ErrDailyBookingLimit {
			return nil, status.Errorf(codes.FailedPrecondition, "daily booking limit reached")
		}
		if err == domain.ErrSemesterBookingLimit {
			return nil, status.Errorf(codes.FailedPrecondition, "semester booking limit reached")
		}
		return nil, status.Errorf(codes.Internal, "failed to create booking: %v", err)
	}

	return s.convertBookingToProto(ctx, booking)
}

// GetBooking retrieves a booking by ID
func (s *bookingServer) GetBooking(ctx context.Context, req *sportpb.GetBookingRequest) (*sportpb.BookingResponse, error) {
	booking, err := s.services.Booking.GetBooking(ctx, req.Id)
	if err != nil {
		if errors.Is(err, domain.ErrBookingNotFound) {
			return nil, status.Errorf(codes.NotFound, "booking not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to get booking: %v", err)
	}

	return s.convertBookingToProto(ctx, booking)
}

// CancelBooking cancels a booking
func (s *bookingServer) CancelBooking(ctx context.Context, req *sportpb.CancelBookingRequest) (*sportpb.BookingResponse, error) {
	err := s.services.Booking.CancelBooking(ctx, req.Id, req.UserId)
	if err != nil {
		if errors.Is(err, domain.ErrBookingNotFound) {
			return nil, status.Errorf(codes.NotFound, "booking not found")
		}
		if errors.Is(err, domain.ErrCancellationDeadline) {
			return nil, status.Errorf(codes.FailedPrecondition, "cancellation deadline has passed")
		}
		return nil, status.Errorf(codes.Internal, "failed to cancel booking: %v", err)
	}

	booking, err := s.services.Booking.GetBooking(ctx, req.Id)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get booking after cancellation: %v", err)
	}

	return s.convertBookingToProto(ctx, booking)
}

// ListUserBookings lists bookings for a user
func (s *bookingServer) ListUserBookings(ctx context.Context, req *sportpb.ListUserBookingsRequest) (*sportpb.ListBookingsResponse, error) {
	filter := domain.BookingFilter{
		UserID:   req.UserId,
		Status:   domain.BookingStatus(req.Status.String()),
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
	}

	if req.StartDate != nil {
		filter.StartDate = req.StartDate.AsTime()
	}

	if req.EndDate != nil {
		filter.EndDate = req.EndDate.AsTime()
	}

	bookings, total, err := s.services.Booking.GetUserBookings(ctx, req.UserId, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list user bookings: %v", err)
	}

	response := &sportpb.ListBookingsResponse{
		Total:    int32(total),
		Page:     int32(filter.Page),
		PageSize: int32(filter.PageSize),
	}

	for _, booking := range bookings {
		bookingProto, err := s.convertBookingToProto(ctx, booking)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to convert booking: %v", err)
		}
		response.Bookings = append(response.Bookings, bookingProto)
	}

	return response, nil
}

// ListScheduleBookings lists bookings for a schedule
func (s *bookingServer) ListScheduleBookings(ctx context.Context, req *sportpb.ListScheduleBookingsRequest) (*sportpb.ListBookingsResponse, error) {
	filter := domain.BookingFilter{
		ScheduleID: req.ScheduleId,
		Status:     domain.BookingStatus(req.Status.String()),
		Page:       int(req.Page),
		PageSize:   int(req.PageSize),
	}

	bookings, total, err := s.services.Booking.GetScheduleBookings(ctx, req.ScheduleId, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list schedule bookings: %v", err)
	}

	response := &sportpb.ListBookingsResponse{
		Total:    int32(total),
		Page:     int32(filter.Page),
		PageSize: int32(filter.PageSize),
	}

	for _, booking := range bookings {
		bookingProto, err := s.convertBookingToProto(ctx, booking)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to convert booking: %v", err)
		}
		response.Bookings = append(response.Bookings, bookingProto)
	}

	return response, nil
}

// GetBookingStats retrieves booking statistics
func (s *bookingServer) GetBookingStats(ctx context.Context, req *sportpb.GetBookingStatsRequest) (*sportpb.BookingStatsResponse, error) {
	filter := domain.BookingFilter{
		UserID:     req.UserId,
		ScheduleID: req.ScheduleId,
	}

	if req.StartDate != nil {
		filter.StartDate = req.StartDate.AsTime()
	}

	if req.EndDate != nil {
		filter.EndDate = req.EndDate.AsTime()
	}

	stats, err := s.services.Booking.GetBookingStats(ctx, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get booking stats: %v", err)
	}

	return &sportpb.BookingStatsResponse{
		TotalBookings:     int32(stats.TotalBookings),
		PendingBookings:   int32(stats.PendingBookings),
		ConfirmedBookings: int32(stats.ConfirmedBookings),
		CancelledBookings: int32(stats.CancelledBookings),
		CompletedBookings: int32(stats.CompletedBookings),
	}, nil
}

// GetUserSemesterStats retrieves user semester statistics
func (s *bookingServer) GetUserSemesterStats(ctx context.Context, req *sportpb.GetUserSemesterStatsRequest) (*sportpb.UserSemesterStatsResponse, error) {
	stats, err := s.services.Booking.GetUserSemesterStats(ctx, req.UserId, req.SemesterId)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get user semester stats: %v", err)
	}

	// Get semester limit
	semesterLimit, err := s.services.SemesterSportLimit.GetSemesterSportLimitBySemesterID(ctx, req.SemesterId)
	if err != nil && !errors.Is(err, domain.ErrSemesterSportLimitNotFound) {
		return nil, status.Errorf(codes.Internal, "failed to get semester limit: %v", err)
	}

	minLessons := 0
	maxLessons := 20
	if semesterLimit != nil {
		minLessons = semesterLimit.MinLessons
		maxLessons = semesterLimit.MaxLessons
	}

	// Check if the user has passed the minimum requirement
	passed := stats.TotalAttendance >= minLessons

	return &sportpb.UserSemesterStatsResponse{
		UserId:          req.UserId,
		SemesterId:      req.SemesterId,
		TotalBookings:   int32(stats.TotalBookings),
		TotalAttendance: int32(stats.TotalAttendance),
		AttendanceRate:  stats.AttendanceRate,
		SemesterLimit:   int32(maxLessons),
		SemesterMinimum: int32(minLessons),
		Passed:          passed,
	}, nil
}

// Helper function to convert domain.Booking to sportpb.BookingResponse
func (s *bookingServer) convertBookingToProto(ctx context.Context, booking *domain.Booking) (*sportpb.BookingResponse, error) {
	// Get the schedule
	schedule, err := s.services.Schedule.GetSchedule(ctx, booking.ScheduleID)
	if err != nil {
		return nil, err
	}

	// Convert booking status
	var status sportpb.BookingStatus
	switch booking.Status {
	case domain.BookingStatusPending:
		status = sportpb.BookingStatus_PENDING
	case domain.BookingStatusConfirmed:
		status = sportpb.BookingStatus_CONFIRMED
	case domain.BookingStatusCancelled:
		status = sportpb.BookingStatus_CANCELLED
	case domain.BookingStatusCompleted:
		status = sportpb.BookingStatus_COMPLETED
	default:
		status = sportpb.BookingStatus_PENDING
	}

	// Get the facility
	facility, err := s.services.PhysicalEducation.GetFacility(ctx, schedule.FacilityID)
	if err != nil {
		return nil, err
	}

	return &sportpb.BookingResponse{
		Id:         booking.ID,
		ScheduleId: booking.ScheduleID,
		UserId:     booking.UserID,
		Status:     status,
		CreatedAt:  timestamppb.New(booking.CreatedAt),
		UpdatedAt:  timestamppb.New(booking.UpdatedAt),
		Schedule: &sportpb.ScheduleInfo{
			Id:            schedule.ID,
			StartTime:     timestamppb.New(schedule.StartTime),
			EndTime:       timestamppb.New(schedule.EndTime),
			Location:      schedule.Location,
			FacilityTitle: facility.Title,
		},
		User: &sportpb.UserInfo{
			Id:   booking.UserID,
			Name: "", // This would need to be fetched from the user service
		},
	}, nil
}
