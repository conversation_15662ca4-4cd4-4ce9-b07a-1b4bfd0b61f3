# Указываем путь к proto-файлам (все *.proto) и целевые директории
PROTO_SRC := $(shell git ls-files -- '*.proto')
PROTO_OUT := ./

# Пути к зависимостям Google APIs (путь для include)
# Если нужны дополнительные пути, добавьте их через -I
PROTO_INCLUDE := -I . -I C:/protoc-28.2-win64/include/googleapis

# Команды для генерации Go и gRPC файлов
.PHONY: all clean proto

all: proto

proto:
	protoc $(PROTO_INCLUDE) \
		--go_out=$(PROTO_OUT) --go_opt=paths=source_relative \
		--go-grpc_out=$(PROTO_OUT) --go-grpc_opt=paths=source_relative \
		--grpc-gateway_out=$(PROTO_OUT) --grpc-gateway_opt=paths=source_relative \
		$(PROTO_SRC)

clean:
	rm -f $(PROTO_OUT)/*.go

# evans -r -p 50051 --host localhost