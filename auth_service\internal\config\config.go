package config

import (
	"os"
	"strconv"
	"time"
)

// Config holds all configuration for the auth service
type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
	JWT      JWTConfig
}

// ServerConfig holds server-specific configuration
type ServerConfig struct {
	Port string
}

// DatabaseConfig holds database connection configuration
type DatabaseConfig struct {
	URL      string
	Host     string
	Port     string
	User     string
	Password string
	Name     string
}

// JWTConfig holds JWT-specific configuration
type JWTConfig struct {
	Secret            string
	AccessExpiration  time.Duration
	RefreshExpiration time.Duration
}

// LoadConfig loads configuration from environment variables
func LoadConfig() *Config {
	accessExpStr := getEnv("JWT_ACCESS_EXPIRATION", "15")
	refreshExpStr := getEnv("JWT_REFRESH_EXPIRATION", "10080") // 7 days in minutes

	accessExp, _ := strconv.Atoi(accessExpStr)
	refreshExp, _ := strconv.Atoi(refreshExpStr)

	return &Config{
		Server: ServerConfig{
			Port: getEnv("SERVER_PORT", "50052"),
		},
		Database: DatabaseConfig{
			URL:      getEnv("DATABASE_URL", ""),
			Host:     getEnv("DATABASE_HOST", "localhost"),
			Port:     getEnv("DATABASE_PORT", "5432"),
			User:     getEnv("DATABASE_USER", "postgres"),
			Password: getEnv("DATABASE_PASSWORD", "postgres"),
			Name:     getEnv("DATABASE_NAME", "edunite"),
		},
		JWT: JWTConfig{
			Secret:            getEnv("JWT_SECRET", "your-secret-key-change-in-production"),
			AccessExpiration:  time.Duration(accessExp) * time.Minute,
			RefreshExpiration: time.Duration(refreshExp) * time.Minute,
		},
	}
}

// getEnv gets an environment variable or returns a default value
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}
