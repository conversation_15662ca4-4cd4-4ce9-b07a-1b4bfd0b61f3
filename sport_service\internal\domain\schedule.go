package domain

import (
	"time"
)

// Schedule represents a time slot when a facility is available for booking
type Schedule struct {
	ID                   int64     `json:"id"`
	FacilityID           int64     `json:"facility_id"`
	TeacherID            int64     `json:"teacher_id"`
	SemesterID           int64     `json:"semester_id"`
	SportTypeID          int64     `json:"sport_type_id"`
	StartTime            time.Time `json:"start_time"`
	EndTime              time.Time `json:"end_time"`
	CancellationDeadline time.Time `json:"cancellation_deadline"` // Deadline for cancellation (8 PM day before)
	Location             string    `json:"location"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
	Version              int32     `json:"version"`
}

// WeeklyScheduleTemplate represents a template for creating recurring schedules
type WeeklyScheduleTemplate struct {
	FacilityID  int64     `json:"facility_id"`
	TeacherID   int64     `json:"teacher_id"`
	SemesterID  int64     `json:"semester_id"`
	SportTypeID int64     `json:"sport_type_id"` // Added SportTypeID field
	DayOfWeek   int       `json:"day_of_week"`   // 1=Monday, 7=Sunday
	StartTime   time.Time `json:"start_time"`
	EndTime     time.Time `json:"end_time"`
	Location    string    `json:"location"`
	StartDate   time.Time `json:"start_date"` // First occurrence
	EndDate     time.Time `json:"end_date"`   // Last occurrence
}

// ScheduleFilter represents filters for querying schedules
type ScheduleFilter struct {
	FacilityID int64
	TeacherID  int64
	SemesterID int64
	StartDate  time.Time
	EndDate    time.Time
	Location   string
	Page       int
	PageSize   int
}

// Error definitions for schedule operations
var (
	ErrScheduleNotFound = Error{"schedule not found"}
	ErrScheduleConflict = Error{"schedule conflicts with existing schedule"}
)
