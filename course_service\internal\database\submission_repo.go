package database

import (
	"context"
	"errors"
	"time"

	"github.com/jackc/pgx/v4/pgxpool"
)

var (
	ErrSubmissionNotFound = errors.New("submission not found")
)

// AssignmentSubmission модель для таблицы assignment_submissions
type AssignmentSubmission struct {
	ID           int64     `json:"id"`
	AssignmentID int64     `json:"assignment_id"`
	UserID       int64     `json:"user_id"`
	SubmittedAt  time.Time `json:"submitted_at"`
	FileURLs     []string  `json:"file_urls"`
	Comment      string    `json:"comment"`
	Score        *int32    `json:"score"`    // Using pointer to handle NULL values
	Feedback     *string   `json:"feedback"` // Using pointer to handle NULL values
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// AssignmentSubmissionRepository интерфейс для работы с submissions
type AssignmentSubmissionRepository interface {
	CreateSubmission(ctx context.Context, s *AssignmentSubmission) error
	GetSubmission(ctx context.Context, submissionID int64) (*AssignmentSubmission, error)
	ListSubmissionsByAssignmentID(ctx context.Context, assignmentID int64) ([]*AssignmentSubmission, error)
	GetSubmissionsByAssignmentAndUser(ctx context.Context, assignmentID, userID int64) ([]*AssignmentSubmission, error)
	UpdateSubmissionScore(ctx context.Context, submissionID int64, score int32, feedback string) (*AssignmentSubmission, error)
	UpdateSubmission(ctx context.Context, s *AssignmentSubmission) error
	DeleteSubmission(ctx context.Context, submissionID int64) error
}

type assignmentSubmissionRepo struct {
	db *pgxpool.Pool
}

// NewAssignmentSubmissionRepository - конструктор
func NewAssignmentSubmissionRepository(db *pgxpool.Pool) AssignmentSubmissionRepository {
	return &assignmentSubmissionRepo{db: db}
}

func (r *assignmentSubmissionRepo) CreateSubmission(ctx context.Context, s *AssignmentSubmission) error {
	query := `
		INSERT INTO assignment_submissions (assignment_id, user_id, file_urls, comment)
		VALUES ($1, $2, $3, $4)
		RETURNING id, submitted_at, created_at, updated_at
	`
	row := r.db.QueryRow(ctx, query, s.AssignmentID, s.UserID, s.FileURLs, s.Comment)
	return row.Scan(&s.ID, &s.SubmittedAt, &s.CreatedAt, &s.UpdatedAt)
}

func (r *assignmentSubmissionRepo) GetSubmission(ctx context.Context, id int64) (*AssignmentSubmission, error) {
	query := `
		SELECT id, assignment_id, user_id, submitted_at, file_urls, comment, score, feedback, created_at, updated_at
		FROM assignment_submissions
		WHERE id = $1
	`
	row := r.db.QueryRow(ctx, query, id)
	var s AssignmentSubmission
	// Create variables for nullable fields
	var score *int32
	var feedback *string
	if err := row.Scan(
		&s.ID,
		&s.AssignmentID,
		&s.UserID,
		&s.SubmittedAt,
		&s.FileURLs,
		&s.Comment,
		&score,
		&feedback,
		&s.CreatedAt,
		&s.UpdatedAt,
	); err != nil {
		return nil, ErrSubmissionNotFound
	}
	s.Score = score
	s.Feedback = feedback
	return &s, nil
}

func (r *assignmentSubmissionRepo) ListSubmissionsByAssignmentID(ctx context.Context, assignmentID int64) ([]*AssignmentSubmission, error) {
	query := `
		SELECT id, assignment_id, user_id, submitted_at, file_urls, comment, score, feedback, created_at, updated_at
		FROM assignment_submissions
		WHERE assignment_id = $1
		ORDER BY id
	`
	rows, err := r.db.Query(ctx, query, assignmentID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var list []*AssignmentSubmission
	for rows.Next() {
		var s AssignmentSubmission
		// Create variables for nullable fields
		var score *int32
		var feedback *string
		if err := rows.Scan(
			&s.ID,
			&s.AssignmentID,
			&s.UserID,
			&s.SubmittedAt,
			&s.FileURLs,
			&s.Comment,
			&score,
			&feedback,
			&s.CreatedAt,
			&s.UpdatedAt,
		); err != nil {
			return nil, err
		}
		s.Score = score
		s.Feedback = feedback
		list = append(list, &s)
	}
	return list, rows.Err()
}

func (r *assignmentSubmissionRepo) UpdateSubmissionScore(ctx context.Context, id int64, score int32, feedback string) (*AssignmentSubmission, error) {
	query := `
		UPDATE assignment_submissions
		SET score = $1, feedback = $2, updated_at = NOW()
		WHERE id = $3
		RETURNING id, assignment_id, user_id, submitted_at, file_urls, comment, score, feedback, created_at, updated_at
	`
	row := r.db.QueryRow(ctx, query, score, feedback, id)
	var s AssignmentSubmission
	// Convert non-pointer values to pointers for scan
	scorePtr := score
	feedbackPtr := feedback
	if err := row.Scan(
		&s.ID,
		&s.AssignmentID,
		&s.UserID,
		&s.SubmittedAt,
		&s.FileURLs,
		&s.Comment,
		&scorePtr,
		&feedbackPtr,
		&s.CreatedAt,
		&s.UpdatedAt,
	); err != nil {
		return nil, ErrSubmissionNotFound
	}
	s.Score = &scorePtr
	s.Feedback = &feedbackPtr
	return &s, nil
}

func (r *assignmentSubmissionRepo) UpdateSubmission(ctx context.Context, s *AssignmentSubmission) error {
	query := `
		UPDATE assignment_submissions
		SET file_urls = $1, comment = $2, submitted_at = NOW(), updated_at = NOW()
		WHERE id = $3
		RETURNING submitted_at, updated_at
	`
	row := r.db.QueryRow(ctx, query, s.FileURLs, s.Comment, s.ID)
	if err := row.Scan(&s.SubmittedAt, &s.UpdatedAt); err != nil {
		return ErrSubmissionNotFound
	}
	return nil
}

func (r *assignmentSubmissionRepo) DeleteSubmission(ctx context.Context, id int64) error {
	cmd, err := r.db.Exec(ctx, `DELETE FROM assignment_submissions WHERE id=$1`, id)
	if err != nil {
		return err
	}
	if cmd.RowsAffected() == 0 {
		return ErrSubmissionNotFound
	}
	return nil
}

// GetSubmissionsByAssignmentAndUser returns all submissions for a specific assignment and user
func (r *assignmentSubmissionRepo) GetSubmissionsByAssignmentAndUser(ctx context.Context, assignmentID, userID int64) ([]*AssignmentSubmission, error) {
	query := `
		SELECT id, assignment_id, user_id, submitted_at, file_urls, comment, score, feedback, created_at, updated_at
		FROM assignment_submissions
		WHERE assignment_id = $1 AND user_id = $2
		ORDER BY submitted_at DESC
	`
	rows, err := r.db.Query(ctx, query, assignmentID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var list []*AssignmentSubmission
	for rows.Next() {
		var s AssignmentSubmission
		// Create variables for nullable fields
		var score *int32
		var feedback *string
		if err := rows.Scan(
			&s.ID,
			&s.AssignmentID,
			&s.UserID,
			&s.SubmittedAt,
			&s.FileURLs,
			&s.Comment,
			&score,
			&feedback,
			&s.CreatedAt,
			&s.UpdatedAt,
		); err != nil {
			return nil, err
		}
		s.Score = score
		s.Feedback = feedback
		list = append(list, &s)
	}
	return list, rows.Err()
}
