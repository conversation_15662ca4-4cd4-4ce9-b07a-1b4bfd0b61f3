package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/course_service/internal/database"
)

func main() {
	// Load database URL from environment
	databaseURL := os.Getenv("DATABASE_URL")
	if databaseURL == "" {
		databaseURL = "postgresql://olzzhas:<EMAIL>/edunite_xmcu"
	}

	// Connect to database
	db, err := pgxpool.Connect(context.Background(), databaseURL)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Create repository
	repo := database.NewTranscriptRepository(db)

	fmt.Println("🔍 Testing database scanning operations...")

	// Test 1: List degrees (should work)
	fmt.Println("\n1. Testing ListDegrees...")
	degrees, count, err := repo.ListDegrees(context.Background(), "", 0, 10)
	if err != nil {
		fmt.Printf("❌ ListDegrees failed: %v\n", err)
		if err.Error() == "can't scan into dest[12]: unknown oid 17100 cannot be scanned into *interface {}" {
			fmt.Println("🚨 OID 17100 error detected!")
		}
	} else {
		fmt.Printf("✅ ListDegrees successful: Found %d degrees (total: %d)\n", len(degrees), count)
	}

	// Test 2: Try to get a transcript (this was the main failing operation)
	fmt.Println("\n2. Testing GetTranscript...")
	transcript, err := repo.GetTranscript(context.Background(), 4, nil)
	if err != nil {
		if err == database.ErrTranscriptNotFound {
			fmt.Println("✅ GetTranscript returned expected 'not found' error (no OID scanning error)")
		} else {
			fmt.Printf("❌ GetTranscript failed: %v\n", err)
			if err.Error() == "can't scan into dest[12]: unknown oid 17100 cannot be scanned into *interface {}" {
				fmt.Println("🚨 OID 17100 error detected!")
			}
		}
	} else {
		fmt.Printf("✅ GetTranscript successful: Found transcript for user %d\n", transcript.UserID)
	}

	// Test 3: Test GetStudentDegrees (this was also failing)
	fmt.Println("\n3. Testing GetStudentDegrees...")
	studentDegrees, err := repo.GetStudentDegrees(context.Background(), 4)
	if err != nil {
		fmt.Printf("❌ GetStudentDegrees failed: %v\n", err)
		if err.Error() == "can't scan into dest[12]: unknown oid 17100 cannot be scanned into *interface {}" {
			fmt.Println("🚨 OID 17100 error detected!")
		}
	} else {
		fmt.Printf("✅ GetStudentDegrees successful: Found %d student degrees\n", len(studentDegrees))
	}

	// Test 4: Test ListStudentDegrees
	fmt.Println("\n4. Testing ListStudentDegrees...")
	userID := int64(4)
	studentDegreesList, totalCount, err := repo.ListStudentDegrees(context.Background(), &userID, nil, "", 0, 10)
	if err != nil {
		fmt.Printf("❌ ListStudentDegrees failed: %v\n", err)
		if err.Error() == "can't scan into dest[12]: unknown oid 17100 cannot be scanned into *interface {}" {
			fmt.Println("🚨 OID 17100 error detected!")
		}
	} else {
		fmt.Printf("✅ ListStudentDegrees successful: Found %d student degrees (total: %d)\n", len(studentDegreesList), totalCount)
	}

	// Test 5: Create a test transcript to verify end-to-end functionality
	fmt.Println("\n5. Testing CreateTranscript...")
	testTranscript := &database.AcademicTranscript{
		UserID:   999, // Use a test user ID
		DegreeID: &degrees[0].ID, // Use the first degree
	}
	err = repo.CreateTranscript(context.Background(), testTranscript)
	if err != nil {
		fmt.Printf("CreateTranscript failed (may be expected): %v\n", err)
	} else {
		fmt.Printf("✅ CreateTranscript successful: Created transcript ID %d\n", testTranscript.ID)
		
		// Now test getting this transcript
		fmt.Println("6. Testing GetTranscript for newly created transcript...")
		retrievedTranscript, err := repo.GetTranscript(context.Background(), 999, &degrees[0].ID)
		if err != nil {
			fmt.Printf("❌ GetTranscript failed for newly created transcript: %v\n", err)
		} else {
			fmt.Printf("✅ GetTranscript successful for newly created transcript!\n")
			fmt.Printf("  - Transcript ID: %d\n", retrievedTranscript.ID)
			fmt.Printf("  - User ID: %d\n", retrievedTranscript.UserID)
			if retrievedTranscript.Degree != nil {
				fmt.Printf("  - Degree: %s\n", retrievedTranscript.Degree.Name)
			}
		}
		
		// Clean up
		repo.DeleteTranscript(context.Background(), testTranscript.ID)
	}

	fmt.Println("\n🎉 Database scanning fix test completed!")
	fmt.Println("If no OID 17100 errors were reported, the fix is working correctly!")
}
