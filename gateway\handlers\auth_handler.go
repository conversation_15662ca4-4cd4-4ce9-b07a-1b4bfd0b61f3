package handlers

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"github.com/olzzhas/edunite-server/gateway/models"
)

type AuthHandler struct {
	RabbitLogPublisher clients.LogPublisher
	AuthClient         *clients.AuthClient
	UserService        *clients.UserClient
}

// RegisterHandler handles user registration
func (h *AuthHandler) RegisterHandler(c *gin.Context) {
	var req models.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	// Register user with Auth Service
	authResp, err := h.AuthClient.Register(req.Username, req.Password, req.Email, req.Name, req.Surname)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Error while registering user: %s", err)})
		return
	}

	// If the user has a role specified, update the user's role
	if req.Role != "" {
		// Get the user ID from the auth response
		userId := authResp.User.Id

		// Update the user's role
		_, err = h.UserService.UpdateUser(c, userId, authResp.User.Name, authResp.User.Surname, authResp.User.Email, req.Role)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Error while updating user role: %s", err)})
			return
		}
	}

	logData := map[string]any{
		"user_id":      authResp.User.Id,
		"user_name":    req.Name,
		"user_surname": req.Surname,
		"user_email":   req.Email,
	}

	_ = h.RabbitLogPublisher.PublishLog("INFO", "user registered successfully", "auth", logData)

	c.JSON(http.StatusCreated, gin.H{
		"message":            "User registered successfully",
		"access_token":       authResp.AccessToken,
		"refresh_token":      authResp.RefreshToken,
		"expires_in":         authResp.ExpiresIn,
		"refresh_expires_in": authResp.RefreshExpiresIn,
		"user":               authResp.User,
	})
}

// LoginHandler handles user authentication
func (h *AuthHandler) LoginHandler(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	// Login with Auth Service
	authResp, err := h.AuthClient.Login(req.Username, req.Password)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"access_token":       authResp.AccessToken,
		"refresh_token":      authResp.RefreshToken,
		"expires_in":         authResp.ExpiresIn,
		"refresh_expires_in": authResp.RefreshExpiresIn,
		"user":               authResp.User,
	})
}

// RefreshTokenHandler handles token refresh
func (h *AuthHandler) RefreshTokenHandler(c *gin.Context) {
	var req models.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	// Refresh token with Auth Service
	authResp, err := h.AuthClient.RefreshToken(req.RefreshToken)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid refresh token"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"access_token":       authResp.AccessToken,
		"refresh_token":      authResp.RefreshToken,
		"expires_in":         authResp.ExpiresIn,
		"refresh_expires_in": authResp.RefreshExpiresIn,
		"user":               authResp.User,
	})
}

// ValidateTokenHandler handles token validation
func (h *AuthHandler) ValidateTokenHandler(c *gin.Context) {
	// Get token from Authorization header
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header is required"})
		return
	}

	// Extract token from "Bearer <token>"
	token := ""
	if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
		token = authHeader[7:]
	} else {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization format"})
		return
	}

	// Validate token with Auth Service
	valid, userId, err := h.AuthClient.ValidateToken(token)
	if err != nil || !valid {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid":   valid,
		"user_id": userId,
	})
}
