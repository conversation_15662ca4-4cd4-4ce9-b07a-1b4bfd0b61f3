// internal/query/threadquery/weeks_with_hw.go
package threadquery

import (
	"context"

	"github.com/olzzhas/edunite-server/course_service/internal/dto"
)

func (r *Repo) ListWeeksWithAssignmentsAndSubmission(ctx context.Context, threadID, userID int64) ([]*dto.WeekAssignmentSubmission, error) {
	const q = `
		SELECT
			w.id                   AS week_id,
			w.thread_id,
			w.week_number,
			w.type,
			w.title,
			w.description,
			w.created_at,
			w.updated_at,

			a.id                   AS ass_id,
			a.title                AS ass_title,
			a.description          AS ass_descr,
			a.due_date,
			a.max_points,
			a.type                 AS ass_type,

			s.id                   AS sub_id,
			s.score,
			s.comment,
			s.feedback,
			s.file_urls,
			s.submitted_at,
			s.created_at           AS sub_created,
			s.updated_at           AS sub_updated
		FROM            weeks           AS w
		LEFT JOIN       assignments     AS a  ON a.week_id = w.id
		LEFT JOIN       assignment_submissions AS s
					  ON s.assignment_id = a.id
					 AND s.user_id      = $2
		WHERE           w.thread_id = $1
		ORDER BY        w.week_number, a.id;
	`

	rows, err := r.db.Query(ctx, q, threadID, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var out []*dto.WeekAssignmentSubmission
	for rows.Next() {
		var rec dto.WeekAssignmentSubmission
		err := rows.Scan(
			&rec.WeekID,
			&rec.ThreadID,
			&rec.WeekNumber,
			&rec.Type,
			&rec.WeekTitle,
			&rec.WeekDescr,
			&rec.WeekCreated,
			&rec.WeekUpdated,

			&rec.AssID,
			&rec.AssTitle,
			&rec.AssDescr,
			&rec.AssDueDate,
			&rec.AssMaxPoints,
			&rec.AssType,

			&rec.SubID,
			&rec.SubScore,
			&rec.SubComment,
			&rec.SubFeedback,
			&rec.SubFileURLs,
			&rec.SubSubmitted,
			&rec.SubCreated,
			&rec.SubUpdated,
		)
		if err != nil {
			return nil, err
		}
		out = append(out, &rec)
	}

	return out, rows.Err()
}
