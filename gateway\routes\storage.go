package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

// SetupStorageRoutes registers routes for file operations (Upload, Download, Delete),
// protected by authentication
func SetupStorageRoutes(r *gin.Engine, authClient *clients.AuthClient, storageHandler *handlers.StorageHandler) {
	storageGroup := r.Group("/storage")
	//storageGroup.Use(AuthMiddleware(authClient))
	{
		// Upload file (multipart form-data)
		storageGroup.POST("/upload", storageHandler.UploadFileHandler)

		// Download file
		storageGroup.GET("/download/:bucket/:object", storageHandler.DownloadFileHandler)

		// Delete file
		storageGroup.DELETE("/:bucket/:object", storageHandler.DeleteFileHandler)

		// теперь метод для «просмотра» (GET файла в виде фото)
		storageGroup.GET("/photo/:bucket/:object", storageHandler.GetPhotoHandler)
	}
}
