# Edunite Server

A modern, microservices-based educational platform backend built with Go and gRPC.

## 🎯 Key Features

- **Microservices Architecture**: Modular design with specialized services for users, courses, storage, and logging
- **Modern Tech Stack**: Go 1.23+, gRPC, Protocol Buffers, Docker
- **Authentication**: Secure authentication via Keycloak integration
- **API Gateway**: Centralized REST API gateway with Swagger documentation
- **Storage**: MinIO integration for file management
- **Logging**: Centralized logging system with MongoDB and RabbitMQ
- **Course Management**: Comprehensive course, assignment, and attendance tracking
- **Documentation**: Interactive API documentation via Swagger UI

## 🛠 Technology Stack

- **Backend**: Go 1.23+
- **API**: gRPC, REST (via gateway)
- **Documentation**: OpenAPI/Swagger
- **Databases**: PostgreSQL, MongoDB
- **Storage**: MinIO
- **Message Queue**: RabbitMQ
- **Authentication**: Keycloak
- **Containerization**: Docker & Docker Compose

## 🚀 Quick Start

1. Clone the repository:
```bash
git clone https://github.com/olzzhas/edunite-server.git
cd edunite-server
```

2. Set up environment variables:
```bash
# Copy example env files
cp .env.example .env
```

3. Start services:
```bash
docker-compose up -d
```

4. Initialize database:
```bash
docker-compose up flyway
```

## 📚 Documentation

API documentation is available at:
- Swagger UI: `http://localhost:8081/docs/swagger-ui/`
- API Documentation: `http://localhost:8081/docs/`

## 🔧 Development

Generate Protocol Buffers code:
```bash
# In respective service directories
make proto
```

## 📦 Services

- Gateway Service (8081): Main API Gateway
- User Service (50051): User management
- Logger Service (50052): Centralized logging
- Course Service (50053): Course management
- Storage Service (50059): File operations
- Keycloak (8085): Authentication
- MinIO (9010/9011): Object storage

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit changes
4. Push to the branch
5. Open a Pull Request

## 📄 License

[Add your license information here]
