package repository

import (
	"context"
	"time"
	
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

// ScheduleRepository defines the interface for schedule data access
type ScheduleRepository interface {
	// Create creates a new schedule
	Create(ctx context.Context, schedule *domain.Schedule) error
	
	// GetByID retrieves a schedule by ID
	GetByID(ctx context.Context, id int64) (*domain.Schedule, error)
	
	// Update updates an existing schedule
	Update(ctx context.Context, schedule *domain.Schedule) error
	
	// Delete deletes a schedule by ID
	Delete(ctx context.Context, id int64) error
	
	// List retrieves schedules based on filters
	List(ctx context.Context, filter domain.ScheduleFilter) ([]*domain.Schedule, error)
	
	// Count counts schedules based on filters
	Count(ctx context.Context, filter domain.ScheduleFilter) (int, error)
	
	// GetByTeacherIDAndTimeRange retrieves schedules for a teacher within a time range
	GetByTeacherIDAndTimeRange(ctx context.Context, teacherID int64, start, end time.Time) ([]*domain.Schedule, error)
	
	// GetByFacilityIDAndTimeRange retrieves schedules for a facility within a time range
	GetByFacilityIDAndTimeRange(ctx context.Context, facilityID int64, start, end time.Time) ([]*domain.Schedule, error)
	
	// GetBySemesterID retrieves schedules for a semester
	GetBySemesterID(ctx context.Context, semesterID int64) ([]*domain.Schedule, error)
	
	// CreateBatch creates multiple schedules at once (for weekly schedules)
	CreateBatch(ctx context.Context, schedules []*domain.Schedule) error
	
	// CheckConflict checks if a schedule conflicts with existing schedules
	CheckConflict(ctx context.Context, schedule *domain.Schedule) (bool, error)
}
