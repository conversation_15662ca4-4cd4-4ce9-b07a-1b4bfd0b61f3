// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/facility.proto

package sportpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Create facility request
type CreateFacilityRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Title         string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	MaxCapacity   int32                  `protobuf:"varint,3,opt,name=max_capacity,json=maxCapacity,proto3" json:"max_capacity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateFacilityRequest) Reset() {
	*x = CreateFacilityRequest{}
	mi := &file_proto_facility_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateFacilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFacilityRequest) ProtoMessage() {}

func (x *CreateFacilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_facility_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFacilityRequest.ProtoReflect.Descriptor instead.
func (*CreateFacilityRequest) Descriptor() ([]byte, []int) {
	return file_proto_facility_proto_rawDescGZIP(), []int{0}
}

func (x *CreateFacilityRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CreateFacilityRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateFacilityRequest) GetMaxCapacity() int32 {
	if x != nil {
		return x.MaxCapacity
	}
	return 0
}

// Get facility request
type GetFacilityRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFacilityRequest) Reset() {
	*x = GetFacilityRequest{}
	mi := &file_proto_facility_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFacilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFacilityRequest) ProtoMessage() {}

func (x *GetFacilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_facility_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFacilityRequest.ProtoReflect.Descriptor instead.
func (*GetFacilityRequest) Descriptor() ([]byte, []int) {
	return file_proto_facility_proto_rawDescGZIP(), []int{1}
}

func (x *GetFacilityRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Update facility request
type UpdateFacilityRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	MaxCapacity   int32                  `protobuf:"varint,4,opt,name=max_capacity,json=maxCapacity,proto3" json:"max_capacity,omitempty"`
	Version       int32                  `protobuf:"varint,5,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateFacilityRequest) Reset() {
	*x = UpdateFacilityRequest{}
	mi := &file_proto_facility_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateFacilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFacilityRequest) ProtoMessage() {}

func (x *UpdateFacilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_facility_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFacilityRequest.ProtoReflect.Descriptor instead.
func (*UpdateFacilityRequest) Descriptor() ([]byte, []int) {
	return file_proto_facility_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateFacilityRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateFacilityRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdateFacilityRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateFacilityRequest) GetMaxCapacity() int32 {
	if x != nil {
		return x.MaxCapacity
	}
	return 0
}

func (x *UpdateFacilityRequest) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

// Delete facility request
type DeleteFacilityRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteFacilityRequest) Reset() {
	*x = DeleteFacilityRequest{}
	mi := &file_proto_facility_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFacilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFacilityRequest) ProtoMessage() {}

func (x *DeleteFacilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_facility_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFacilityRequest.ProtoReflect.Descriptor instead.
func (*DeleteFacilityRequest) Descriptor() ([]byte, []int) {
	return file_proto_facility_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteFacilityRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// List facilities request
type ListFacilitiesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Title         string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFacilitiesRequest) Reset() {
	*x = ListFacilitiesRequest{}
	mi := &file_proto_facility_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFacilitiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFacilitiesRequest) ProtoMessage() {}

func (x *ListFacilitiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_facility_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFacilitiesRequest.ProtoReflect.Descriptor instead.
func (*ListFacilitiesRequest) Descriptor() ([]byte, []int) {
	return file_proto_facility_proto_rawDescGZIP(), []int{4}
}

func (x *ListFacilitiesRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ListFacilitiesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListFacilitiesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Facility response
type FacilityResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	MaxCapacity   int32                  `protobuf:"varint,4,opt,name=max_capacity,json=maxCapacity,proto3" json:"max_capacity,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Version       int32                  `protobuf:"varint,7,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FacilityResponse) Reset() {
	*x = FacilityResponse{}
	mi := &file_proto_facility_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FacilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FacilityResponse) ProtoMessage() {}

func (x *FacilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_facility_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FacilityResponse.ProtoReflect.Descriptor instead.
func (*FacilityResponse) Descriptor() ([]byte, []int) {
	return file_proto_facility_proto_rawDescGZIP(), []int{5}
}

func (x *FacilityResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FacilityResponse) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FacilityResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *FacilityResponse) GetMaxCapacity() int32 {
	if x != nil {
		return x.MaxCapacity
	}
	return 0
}

func (x *FacilityResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *FacilityResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *FacilityResponse) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

// List facilities response
type ListFacilitiesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Facilities    []*FacilityResponse    `protobuf:"bytes,1,rep,name=facilities,proto3" json:"facilities,omitempty"`
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFacilitiesResponse) Reset() {
	*x = ListFacilitiesResponse{}
	mi := &file_proto_facility_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFacilitiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFacilitiesResponse) ProtoMessage() {}

func (x *ListFacilitiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_facility_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFacilitiesResponse.ProtoReflect.Descriptor instead.
func (*ListFacilitiesResponse) Descriptor() ([]byte, []int) {
	return file_proto_facility_proto_rawDescGZIP(), []int{6}
}

func (x *ListFacilitiesResponse) GetFacilities() []*FacilityResponse {
	if x != nil {
		return x.Facilities
	}
	return nil
}

func (x *ListFacilitiesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListFacilitiesResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListFacilitiesResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

var File_proto_facility_proto protoreflect.FileDescriptor

const file_proto_facility_proto_rawDesc = "" +
	"\n" +
	"\x14proto/facility.proto\x12\asportpb\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\"r\n" +
	"\x15CreateFacilityRequest\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12!\n" +
	"\fmax_capacity\x18\x03 \x01(\x05R\vmaxCapacity\"$\n" +
	"\x12GetFacilityRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\x9c\x01\n" +
	"\x15UpdateFacilityRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12!\n" +
	"\fmax_capacity\x18\x04 \x01(\x05R\vmaxCapacity\x12\x18\n" +
	"\aversion\x18\x05 \x01(\x05R\aversion\"'\n" +
	"\x15DeleteFacilityRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"^\n" +
	"\x15ListFacilitiesRequest\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\"\x8d\x02\n" +
	"\x10FacilityResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12!\n" +
	"\fmax_capacity\x18\x04 \x01(\x05R\vmaxCapacity\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12\x18\n" +
	"\aversion\x18\a \x01(\x05R\aversion\"\x9a\x01\n" +
	"\x16ListFacilitiesResponse\x129\n" +
	"\n" +
	"facilities\x18\x01 \x03(\v2\x19.sportpb.FacilityResponseR\n" +
	"facilities\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize2\x99\x03\n" +
	"\x0fFacilityService\x12M\n" +
	"\x0eCreateFacility\x12\x1e.sportpb.CreateFacilityRequest\x1a\x19.sportpb.FacilityResponse\"\x00\x12G\n" +
	"\vGetFacility\x12\x1b.sportpb.GetFacilityRequest\x1a\x19.sportpb.FacilityResponse\"\x00\x12M\n" +
	"\x0eUpdateFacility\x12\x1e.sportpb.UpdateFacilityRequest\x1a\x19.sportpb.FacilityResponse\"\x00\x12J\n" +
	"\x0eDeleteFacility\x12\x1e.sportpb.DeleteFacilityRequest\x1a\x16.google.protobuf.Empty\"\x00\x12S\n" +
	"\x0eListFacilities\x12\x1e.sportpb.ListFacilitiesRequest\x1a\x1f.sportpb.ListFacilitiesResponse\"\x00B<Z:github.com/olzzhas/edunite-server/sport_service/pb/sportpbb\x06proto3"

var (
	file_proto_facility_proto_rawDescOnce sync.Once
	file_proto_facility_proto_rawDescData []byte
)

func file_proto_facility_proto_rawDescGZIP() []byte {
	file_proto_facility_proto_rawDescOnce.Do(func() {
		file_proto_facility_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_facility_proto_rawDesc), len(file_proto_facility_proto_rawDesc)))
	})
	return file_proto_facility_proto_rawDescData
}

var file_proto_facility_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_proto_facility_proto_goTypes = []any{
	(*CreateFacilityRequest)(nil),  // 0: sportpb.CreateFacilityRequest
	(*GetFacilityRequest)(nil),     // 1: sportpb.GetFacilityRequest
	(*UpdateFacilityRequest)(nil),  // 2: sportpb.UpdateFacilityRequest
	(*DeleteFacilityRequest)(nil),  // 3: sportpb.DeleteFacilityRequest
	(*ListFacilitiesRequest)(nil),  // 4: sportpb.ListFacilitiesRequest
	(*FacilityResponse)(nil),       // 5: sportpb.FacilityResponse
	(*ListFacilitiesResponse)(nil), // 6: sportpb.ListFacilitiesResponse
	(*timestamppb.Timestamp)(nil),  // 7: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),          // 8: google.protobuf.Empty
}
var file_proto_facility_proto_depIdxs = []int32{
	7, // 0: sportpb.FacilityResponse.created_at:type_name -> google.protobuf.Timestamp
	7, // 1: sportpb.FacilityResponse.updated_at:type_name -> google.protobuf.Timestamp
	5, // 2: sportpb.ListFacilitiesResponse.facilities:type_name -> sportpb.FacilityResponse
	0, // 3: sportpb.FacilityService.CreateFacility:input_type -> sportpb.CreateFacilityRequest
	1, // 4: sportpb.FacilityService.GetFacility:input_type -> sportpb.GetFacilityRequest
	2, // 5: sportpb.FacilityService.UpdateFacility:input_type -> sportpb.UpdateFacilityRequest
	3, // 6: sportpb.FacilityService.DeleteFacility:input_type -> sportpb.DeleteFacilityRequest
	4, // 7: sportpb.FacilityService.ListFacilities:input_type -> sportpb.ListFacilitiesRequest
	5, // 8: sportpb.FacilityService.CreateFacility:output_type -> sportpb.FacilityResponse
	5, // 9: sportpb.FacilityService.GetFacility:output_type -> sportpb.FacilityResponse
	5, // 10: sportpb.FacilityService.UpdateFacility:output_type -> sportpb.FacilityResponse
	8, // 11: sportpb.FacilityService.DeleteFacility:output_type -> google.protobuf.Empty
	6, // 12: sportpb.FacilityService.ListFacilities:output_type -> sportpb.ListFacilitiesResponse
	8, // [8:13] is the sub-list for method output_type
	3, // [3:8] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_proto_facility_proto_init() }
func file_proto_facility_proto_init() {
	if File_proto_facility_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_facility_proto_rawDesc), len(file_proto_facility_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_facility_proto_goTypes,
		DependencyIndexes: file_proto_facility_proto_depIdxs,
		MessageInfos:      file_proto_facility_proto_msgTypes,
	}.Build()
	File_proto_facility_proto = out.File
	file_proto_facility_proto_goTypes = nil
	file_proto_facility_proto_depIdxs = nil
}
