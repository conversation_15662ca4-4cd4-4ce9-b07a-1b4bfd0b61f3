-- Create user role enum type directly (CockroachDB compatible)
CREATE TYPE user_role AS ENUM (
    'student',
    'teacher',
    'moderator',
    'admin'
);

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    keycloak_id UUID UNIQUE NOT NULL,
    name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    surname <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    role user_role NOT NULL DEFAULT 'student',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version INT DEFAULT 1
);

-- Создаем функцию для автоматического обновления поля updated_at при изменении данных
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Создаем триггер, вызывающий функцию перед обновлением записи
CREATE TRIGGER update_users_timestamp
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

-- Создаем индексы для повышения производительности поиска
CREATE INDEX idx_users_email ON users (email);
CREATE INDEX idx_users_keycloak_id ON users (keycloak_id);
