package database

import (
	"context"
	"errors"
	"github.com/jackc/pgconn"
	"github.com/jackc/pgx/v4"
	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
	"time"

	"github.com/jackc/pgx/v4/pgxpool"
)

var (
	ErrSemesterNotFound      = errors.New("semester not found")
	ErrSemesterBreakNotFound = errors.New("semester break not found")
	ErrSemesterBreakConflict = errors.New("semester break already exists for this date")
)

// Semester модель для таблицы semesters
type Semester struct {
	ID        int64     `json:"id"`
	Name      string    `json:"name"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// SemesterBreak модель для таблицы semester_breaks
type SemesterBreak struct {
	ID          int64     `json:"id"`
	SemesterID  int64     `json:"semester_id"`
	BreakDate   time.Time `json:"break_date"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// SemesterRepository интерфейс для работы с семестрами и перерывами
type SemesterRepository interface {
	CreateSemester(ctx context.Context, sem *Semester) error
	GetSemester(ctx context.Context, id int64) (*Semester, error)
	GetAllSemesters(ctx context.Context) ([]*Semester, error)
	UpdateSemester(ctx context.Context, sem *Semester) error
	DeleteSemester(ctx context.Context, id int64) error

	AddSemesterBreak(ctx context.Context, br *SemesterBreak) error
	RemoveSemesterBreak(ctx context.Context, id int64) error
	ListSemesterBreaks(ctx context.Context, semesterID int64) ([]*SemesterBreak, error)
}

type semesterRepository struct {
	db *pgxpool.Pool
}

func ValidateSemester(v *validator.Validator, s *Semester) {
	v.Check(s.Name != "", "name", "must be provided")
	v.Check(len(s.Name) <= 255, "name", "max length is 255 characters")
	v.Check(!s.StartDate.IsZero(), "start_date", "must be provided")
	v.Check(!s.EndDate.IsZero(), "end_date", "must be provided")
	v.Check(s.StartDate.Before(s.EndDate), "date_range", "start_date must be before end_date")
}

func ValidateSemesterBreak(v *validator.Validator, b *SemesterBreak) {
	v.Check(b.SemesterID > 0, "semester_id", "must be provided and > 0")
	v.Check(!b.BreakDate.IsZero(), "break_date", "must be provided")
	v.Check(len(b.Description) <= 500, "description", "max length is 500 characters")
}

// NewSemesterRepository конструктор
func NewSemesterRepository(db *pgxpool.Pool) SemesterRepository {
	return &semesterRepository{db: db}
}

func (r *semesterRepository) CreateSemester(ctx context.Context, sem *Semester) error {
	query := `
		INSERT INTO semesters (name, start_date, end_date)
		VALUES ($1, $2, $3)
		RETURNING id, created_at, updated_at
	`
	row := r.db.QueryRow(ctx, query, sem.Name, sem.StartDate, sem.EndDate)
	if err := row.Scan(&sem.ID, &sem.CreatedAt, &sem.UpdatedAt); err != nil {
		return err
	}
	return nil
}

func (r *semesterRepository) GetSemester(ctx context.Context, id int64) (*Semester, error) {
	query := `
		SELECT id, name, start_date, end_date, created_at, updated_at
		FROM semesters WHERE id = $1
	`
	sem := &Semester{}
	err := r.db.QueryRow(ctx, query, id).Scan(
		&sem.ID, &sem.Name, &sem.StartDate, &sem.EndDate, &sem.CreatedAt, &sem.UpdatedAt,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, ErrSemesterNotFound
		}
		return nil, err
	}
	return sem, nil
}

func (r *semesterRepository) GetAllSemesters(ctx context.Context) ([]*Semester, error) {
	query := `SELECT id, name, start_date, end_date, created_at, updated_at FROM semesters ORDER BY id`
	rows, err := r.db.Query(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var list []*Semester
	for rows.Next() {
		s := &Semester{}
		if err := rows.Scan(&s.ID, &s.Name, &s.StartDate, &s.EndDate, &s.CreatedAt, &s.UpdatedAt); err != nil {
			return nil, err
		}
		list = append(list, s)
	}
	return list, rows.Err()
}

func (r *semesterRepository) UpdateSemester(ctx context.Context, sem *Semester) error {
	query := `
		UPDATE semesters
		SET name=$1, start_date=$2, end_date=$3, updated_at=NOW()
		WHERE id=$4 RETURNING updated_at
	`
	row := r.db.QueryRow(ctx, query, sem.Name, sem.StartDate, sem.EndDate, sem.ID)
	if err := row.Scan(&sem.UpdatedAt); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return ErrSemesterNotFound
		}
		return err
	}
	return nil
}

func (r *semesterRepository) DeleteSemester(ctx context.Context, id int64) error {
	tag, err := r.db.Exec(ctx, `DELETE FROM semesters WHERE id=$1`, id)
	if err != nil {
		return err
	}
	if tag.RowsAffected() == 0 {
		return ErrSemesterNotFound
	}
	return nil
}

func (r *semesterRepository) AddSemesterBreak(ctx context.Context, br *SemesterBreak) error {
	query := `
		INSERT INTO semester_breaks (semester_id, break_date, description)
		VALUES ($1, $2, $3)
		RETURNING id, created_at, updated_at
	`
	row := r.db.QueryRow(ctx, query, br.SemesterID, br.BreakDate, br.Description)
	if err := row.Scan(&br.ID, &br.CreatedAt, &br.UpdatedAt); err != nil {
		var pgErr *pgconn.PgError
		if errors.As(err, &pgErr) && pgErr.Code == "23505" {
			return ErrSemesterBreakConflict
		}
		return err
	}
	return nil
}

func (r *semesterRepository) RemoveSemesterBreak(ctx context.Context, id int64) error {
	tag, err := r.db.Exec(ctx, `DELETE FROM semester_breaks WHERE id=$1`, id)
	if err != nil {
		return err
	}
	if tag.RowsAffected() == 0 {
		return ErrSemesterBreakNotFound
	}
	return nil
}

func (r *semesterRepository) ListSemesterBreaks(ctx context.Context, semesterID int64) ([]*SemesterBreak, error) {
	query := `
		SELECT id, semester_id, break_date, description, created_at, updated_at
		FROM semester_breaks WHERE semester_id=$1 ORDER BY break_date
	`
	rows, err := r.db.Query(ctx, query, semesterID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var list []*SemesterBreak
	for rows.Next() {
		b := &SemesterBreak{}
		if err := rows.Scan(&b.ID, &b.SemesterID, &b.BreakDate, &b.Description, &b.CreatedAt, &b.UpdatedAt); err != nil {
			return nil, err
		}
		list = append(list, b)
	}
	return list, rows.Err()
}
