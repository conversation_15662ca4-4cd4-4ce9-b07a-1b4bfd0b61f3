package grpc

import (
	"context"
	"errors"
	sportpb "github.com/olzzhas/edunite-server/sport_service/proto"

	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
	"github.com/olzzhas/edunite-server/sport_service/internal/service"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type physicalEducationServer struct {
	sportpb.UnimplementedPhysicalEducationServiceServer
	services *service.Services
}

// NewPhysicalEducationServer creates a new physical education gRPC server
func NewPhysicalEducationServer(services *service.Services) *physicalEducationServer {
	return &physicalEducationServer{
		services: services,
	}
}

// GetAvailableSportTypes retrieves available sport types
func (s *physicalEducationServer) GetAvailableSportTypes(ctx context.Context, req *sportpb.GetAvailableSportTypesRequest) (*sportpb.ListSportTypesResponse, error) {
	sportTypes, err := s.services.PhysicalEducation.GetAvailableSportTypes(ctx)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get available sport types: %v", err)
	}

	response := &sportpb.ListSportTypesResponse{
		Total:    int32(len(sportTypes)),
		Page:     1,
		PageSize: int32(len(sportTypes)),
	}

	for _, sportType := range sportTypes {
		response.SportTypes = append(response.SportTypes, convertSportTypeToProto(sportType))
	}

	return response, nil
}

// GetAvailableFacilities retrieves available facilities
func (s *physicalEducationServer) GetAvailableFacilities(ctx context.Context, req *sportpb.GetAvailableFacilitiesRequest) (*sportpb.ListFacilitiesResponse, error) {
	facilities, err := s.services.PhysicalEducation.GetAvailableFacilities(ctx)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get available facilities: %v", err)
	}

	response := &sportpb.ListFacilitiesResponse{
		Total:    int32(len(facilities)),
		Page:     1,
		PageSize: int32(len(facilities)),
	}

	for _, facility := range facilities {
		response.Facilities = append(response.Facilities, convertFacilityToProto(facility))
	}

	return response, nil
}

// GetAvailableSchedules retrieves available schedules
func (s *physicalEducationServer) GetAvailableSchedules(ctx context.Context, req *sportpb.GetAvailableSchedulesRequest) (*sportpb.ListSchedulesResponse, error) {
	schedules, err := s.services.PhysicalEducation.GetAvailableSchedules(
		ctx,
		req.FacilityId,
		req.StartDate.AsTime(),
		req.EndDate.AsTime(),
	)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get available schedules: %v", err)
	}

	response := &sportpb.ListSchedulesResponse{
		Total:    int32(len(schedules)),
		Page:     1,
		PageSize: int32(len(schedules)),
	}

	scheduleServer := NewScheduleServer(s.services)
	for _, schedule := range schedules {
		scheduleProto, err := scheduleServer.convertScheduleToProto(ctx, schedule)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to convert schedule: %v", err)
		}
		response.Schedules = append(response.Schedules, scheduleProto)
	}

	return response, nil
}

// BookSession books a session
func (s *physicalEducationServer) BookSession(ctx context.Context, req *sportpb.BookSessionRequest) (*sportpb.BookingResponse, error) {
	booking, err := s.services.PhysicalEducation.BookSession(ctx, req.UserId, req.ScheduleId)
	if err != nil {
		if err == domain.ErrBookingClosed {
			return nil, status.Errorf(codes.FailedPrecondition, "booking is closed")
		}
		if err == domain.ErrScheduleFull {
			return nil, status.Errorf(codes.FailedPrecondition, "schedule is full")
		}
		if err == domain.ErrDuplicateBooking {
			return nil, status.Errorf(codes.AlreadyExists, "booking already exists")
		}
		if err == domain.ErrInvalidMedicalCertificate {
			return nil, status.Errorf(codes.FailedPrecondition, "invalid medical certificate")
		}
		if err == domain.ErrDailyBookingLimit {
			return nil, status.Errorf(codes.FailedPrecondition, "daily booking limit reached")
		}
		if err == domain.ErrSemesterBookingLimit {
			return nil, status.Errorf(codes.FailedPrecondition, "semester booking limit reached")
		}
		return nil, status.Errorf(codes.Internal, "failed to book session: %v", err)
	}

	bookingServer := NewBookingServer(s.services)
	return bookingServer.convertBookingToProto(ctx, booking)
}

// CancelBooking cancels a booking
func (s *physicalEducationServer) CancelBooking(ctx context.Context, req *sportpb.CancelBookingRequest) (*sportpb.BookingResponse, error) {
	err := s.services.PhysicalEducation.CancelBooking(ctx, req.Id, req.UserId)
	if err != nil {
		if err == domain.ErrBookingNotFound {
			return nil, status.Errorf(codes.NotFound, "booking not found")
		}
		if err == domain.ErrCancellationDeadline {
			return nil, status.Errorf(codes.FailedPrecondition, "cancellation deadline has passed")
		}
		return nil, status.Errorf(codes.Internal, "failed to cancel booking: %v", err)
	}

	booking, err := s.services.Booking.GetBooking(ctx, req.Id)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get booking after cancellation: %v", err)
	}

	bookingServer := NewBookingServer(s.services)
	return bookingServer.convertBookingToProto(ctx, booking)
}

// UploadMedicalCertificate uploads a medical certificate
func (s *physicalEducationServer) UploadMedicalCertificate(ctx context.Context, req *sportpb.UploadCertificateRequest) (*sportpb.MedicalCertificateResponse, error) {
	certificate, err := s.services.PhysicalEducation.UploadMedicalCertificate(
		ctx,
		req.UserId,
		req.FileUrl,
		req.ValidFrom.AsTime(),
		req.ValidUntil.AsTime(),
	)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to upload medical certificate: %v", err)
	}

	certificateServer := NewMedicalCertificateServer(s.services)
	return certificateServer.convertCertificateToProto(certificate), nil
}

// GetUserBookings retrieves a user's bookings
func (s *physicalEducationServer) GetUserBookings(ctx context.Context, req *sportpb.GetUserBookingsRequest) (*sportpb.ListBookingsResponse, error) {
	bookings, err := s.services.PhysicalEducation.GetUserBookings(
		ctx,
		req.UserId,
		req.StartDate.AsTime(),
		req.EndDate.AsTime(),
	)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get user bookings: %v", err)
	}

	response := &sportpb.ListBookingsResponse{
		Total:    int32(len(bookings)),
		Page:     1,
		PageSize: int32(len(bookings)),
	}

	bookingServer := NewBookingServer(s.services)
	for _, booking := range bookings {
		bookingProto, err := bookingServer.convertBookingToProto(ctx, booking)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to convert booking: %v", err)
		}
		response.Bookings = append(response.Bookings, bookingProto)
	}

	return response, nil
}

// GetUserSemesterStats retrieves a user's semester statistics
func (s *physicalEducationServer) GetUserSemesterStats(ctx context.Context, req *sportpb.GetUserSemesterStatsRequest) (*sportpb.UserSemesterStatsResponse, error) {
	stats, err := s.services.PhysicalEducation.GetUserSemesterStats(ctx, req.UserId, req.SemesterId)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get user semester stats: %v", err)
	}

	// Get semester limit
	semesterLimit, err := s.services.PhysicalEducation.GetSemesterSportLimit(ctx, req.SemesterId)
	if err != nil && !errors.Is(err, domain.ErrSemesterSportLimitNotFound) {
		return nil, status.Errorf(codes.Internal, "failed to get semester limit: %v", err)
	}

	minLessons := 0
	maxLessons := 20
	if semesterLimit != nil {
		minLessons = semesterLimit.MinLessons
		maxLessons = semesterLimit.MaxLessons
	}

	// Check if the user has passed the minimum requirement
	passed := stats.TotalAttendance >= minLessons

	// Calculate total bookings and attendance
	totalBookings := stats.TotalBookings
	totalAttendance := stats.TotalAttendance

	// Calculate attendance rate
	attendanceRate := stats.AttendanceRate
	return &sportpb.UserSemesterStatsResponse{
		UserId:          req.UserId,
		SemesterId:      req.SemesterId,
		TotalBookings:   int32(totalBookings),
		TotalAttendance: int32(totalAttendance),
		AttendanceRate:  attendanceRate,
		SemesterLimit:   int32(maxLessons),
		SemesterMinimum: int32(minLessons),
		Passed:          passed,
	}, nil
}

// CheckUserCanBookLFK checks if a user can book LFK
func (s *physicalEducationServer) CheckUserCanBookLFK(ctx context.Context, req *sportpb.CheckUserCanBookLFKRequest) (*sportpb.CheckUserCanBookLFKResponse, error) {
	canBook, err := s.services.PhysicalEducation.CheckUserCanBookLFK(ctx, req.UserId)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to check if user can book LFK: %v", err)
	}

	response := &sportpb.CheckUserCanBookLFKResponse{
		CanBook: canBook,
	}

	if !canBook {
		response.Reason = "User does not have a valid medical certificate"
	}

	return response, nil
}

// GetSemesterSportLimit retrieves the semester sport limit
func (s *physicalEducationServer) GetSemesterSportLimit(ctx context.Context, req *sportpb.GetSemesterLimitBySemesterRequest) (*sportpb.SemesterLimitResponse, error) {
	limit, err := s.services.PhysicalEducation.GetSemesterSportLimit(ctx, req.SemesterId)
	if err != nil {
		if err == domain.ErrSemesterSportLimitNotFound {
			return nil, status.Errorf(codes.NotFound, "semester limit not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to get semester limit: %v", err)
	}

	semesterLimitServer := NewSemesterLimitServer(s.services)
	return semesterLimitServer.convertSemesterLimitToProto(limit), nil
}

// GetDailyBookingLimit retrieves the daily booking limit
func (s *physicalEducationServer) GetDailyBookingLimit(ctx context.Context, req *sportpb.GetDailyBookingLimitBySemesterRequest) (*sportpb.DailyBookingLimitResponse, error) {
	limit, err := s.services.PhysicalEducation.GetDailyBookingLimit(ctx, req.SemesterId)
	if err != nil {
		if err == domain.ErrDailyBookingLimitNotFound {
			return nil, status.Errorf(codes.NotFound, "daily booking limit not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to get daily booking limit: %v", err)
	}

	semesterLimitServer := NewSemesterLimitServer(s.services)
	return semesterLimitServer.convertDailyBookingLimitToProto(limit), nil
}
