// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/semester_limit.proto

package sportpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Create semester limit request
type CreateSemesterLimitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SemesterId    int64                  `protobuf:"varint,1,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	MinLessons    int32                  `protobuf:"varint,2,opt,name=min_lessons,json=minLessons,proto3" json:"min_lessons,omitempty"`
	MaxLessons    int32                  `protobuf:"varint,3,opt,name=max_lessons,json=maxLessons,proto3" json:"max_lessons,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSemesterLimitRequest) Reset() {
	*x = CreateSemesterLimitRequest{}
	mi := &file_proto_semester_limit_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSemesterLimitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSemesterLimitRequest) ProtoMessage() {}

func (x *CreateSemesterLimitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSemesterLimitRequest.ProtoReflect.Descriptor instead.
func (*CreateSemesterLimitRequest) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{0}
}

func (x *CreateSemesterLimitRequest) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *CreateSemesterLimitRequest) GetMinLessons() int32 {
	if x != nil {
		return x.MinLessons
	}
	return 0
}

func (x *CreateSemesterLimitRequest) GetMaxLessons() int32 {
	if x != nil {
		return x.MaxLessons
	}
	return 0
}

// Get semester limit request
type GetSemesterLimitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSemesterLimitRequest) Reset() {
	*x = GetSemesterLimitRequest{}
	mi := &file_proto_semester_limit_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSemesterLimitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSemesterLimitRequest) ProtoMessage() {}

func (x *GetSemesterLimitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSemesterLimitRequest.ProtoReflect.Descriptor instead.
func (*GetSemesterLimitRequest) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{1}
}

func (x *GetSemesterLimitRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Get semester limit by semester request
type GetSemesterLimitBySemesterRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SemesterId    int64                  `protobuf:"varint,1,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSemesterLimitBySemesterRequest) Reset() {
	*x = GetSemesterLimitBySemesterRequest{}
	mi := &file_proto_semester_limit_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSemesterLimitBySemesterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSemesterLimitBySemesterRequest) ProtoMessage() {}

func (x *GetSemesterLimitBySemesterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSemesterLimitBySemesterRequest.ProtoReflect.Descriptor instead.
func (*GetSemesterLimitBySemesterRequest) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{2}
}

func (x *GetSemesterLimitBySemesterRequest) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

// Update semester limit request
type UpdateSemesterLimitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MinLessons    int32                  `protobuf:"varint,2,opt,name=min_lessons,json=minLessons,proto3" json:"min_lessons,omitempty"`
	MaxLessons    int32                  `protobuf:"varint,3,opt,name=max_lessons,json=maxLessons,proto3" json:"max_lessons,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSemesterLimitRequest) Reset() {
	*x = UpdateSemesterLimitRequest{}
	mi := &file_proto_semester_limit_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSemesterLimitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSemesterLimitRequest) ProtoMessage() {}

func (x *UpdateSemesterLimitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSemesterLimitRequest.ProtoReflect.Descriptor instead.
func (*UpdateSemesterLimitRequest) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateSemesterLimitRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateSemesterLimitRequest) GetMinLessons() int32 {
	if x != nil {
		return x.MinLessons
	}
	return 0
}

func (x *UpdateSemesterLimitRequest) GetMaxLessons() int32 {
	if x != nil {
		return x.MaxLessons
	}
	return 0
}

// Delete semester limit request
type DeleteSemesterLimitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSemesterLimitRequest) Reset() {
	*x = DeleteSemesterLimitRequest{}
	mi := &file_proto_semester_limit_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSemesterLimitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSemesterLimitRequest) ProtoMessage() {}

func (x *DeleteSemesterLimitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSemesterLimitRequest.ProtoReflect.Descriptor instead.
func (*DeleteSemesterLimitRequest) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteSemesterLimitRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// List semester limits request
type ListSemesterLimitsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSemesterLimitsRequest) Reset() {
	*x = ListSemesterLimitsRequest{}
	mi := &file_proto_semester_limit_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSemesterLimitsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSemesterLimitsRequest) ProtoMessage() {}

func (x *ListSemesterLimitsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSemesterLimitsRequest.ProtoReflect.Descriptor instead.
func (*ListSemesterLimitsRequest) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{5}
}

func (x *ListSemesterLimitsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSemesterLimitsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Semester limit response
type SemesterLimitResponse struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	Id         int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SemesterId int64                  `protobuf:"varint,2,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	MinLessons int32                  `protobuf:"varint,3,opt,name=min_lessons,json=minLessons,proto3" json:"min_lessons,omitempty"`
	MaxLessons int32                  `protobuf:"varint,4,opt,name=max_lessons,json=maxLessons,proto3" json:"max_lessons,omitempty"`
	CreatedAt  *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt  *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Additional information
	Semester      *SemesterInfo `protobuf:"bytes,7,opt,name=semester,proto3" json:"semester,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SemesterLimitResponse) Reset() {
	*x = SemesterLimitResponse{}
	mi := &file_proto_semester_limit_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SemesterLimitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SemesterLimitResponse) ProtoMessage() {}

func (x *SemesterLimitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SemesterLimitResponse.ProtoReflect.Descriptor instead.
func (*SemesterLimitResponse) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{6}
}

func (x *SemesterLimitResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SemesterLimitResponse) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *SemesterLimitResponse) GetMinLessons() int32 {
	if x != nil {
		return x.MinLessons
	}
	return 0
}

func (x *SemesterLimitResponse) GetMaxLessons() int32 {
	if x != nil {
		return x.MaxLessons
	}
	return 0
}

func (x *SemesterLimitResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SemesterLimitResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *SemesterLimitResponse) GetSemester() *SemesterInfo {
	if x != nil {
		return x.Semester
	}
	return nil
}

// List semester limits response
type ListSemesterLimitsResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Limits        []*SemesterLimitResponse `protobuf:"bytes,1,rep,name=limits,proto3" json:"limits,omitempty"`
	Total         int32                    `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                    `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                    `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSemesterLimitsResponse) Reset() {
	*x = ListSemesterLimitsResponse{}
	mi := &file_proto_semester_limit_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSemesterLimitsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSemesterLimitsResponse) ProtoMessage() {}

func (x *ListSemesterLimitsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSemesterLimitsResponse.ProtoReflect.Descriptor instead.
func (*ListSemesterLimitsResponse) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{7}
}

func (x *ListSemesterLimitsResponse) GetLimits() []*SemesterLimitResponse {
	if x != nil {
		return x.Limits
	}
	return nil
}

func (x *ListSemesterLimitsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListSemesterLimitsResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSemesterLimitsResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Create daily booking limit request
type CreateDailyBookingLimitRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	SemesterId        int64                  `protobuf:"varint,1,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	MaxBookingsPerDay int32                  `protobuf:"varint,2,opt,name=max_bookings_per_day,json=maxBookingsPerDay,proto3" json:"max_bookings_per_day,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateDailyBookingLimitRequest) Reset() {
	*x = CreateDailyBookingLimitRequest{}
	mi := &file_proto_semester_limit_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDailyBookingLimitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDailyBookingLimitRequest) ProtoMessage() {}

func (x *CreateDailyBookingLimitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDailyBookingLimitRequest.ProtoReflect.Descriptor instead.
func (*CreateDailyBookingLimitRequest) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{8}
}

func (x *CreateDailyBookingLimitRequest) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *CreateDailyBookingLimitRequest) GetMaxBookingsPerDay() int32 {
	if x != nil {
		return x.MaxBookingsPerDay
	}
	return 0
}

// Get daily booking limit request
type GetDailyBookingLimitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDailyBookingLimitRequest) Reset() {
	*x = GetDailyBookingLimitRequest{}
	mi := &file_proto_semester_limit_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDailyBookingLimitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyBookingLimitRequest) ProtoMessage() {}

func (x *GetDailyBookingLimitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyBookingLimitRequest.ProtoReflect.Descriptor instead.
func (*GetDailyBookingLimitRequest) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{9}
}

func (x *GetDailyBookingLimitRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// Get daily booking limit by semester request
type GetDailyBookingLimitBySemesterRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SemesterId    int64                  `protobuf:"varint,1,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDailyBookingLimitBySemesterRequest) Reset() {
	*x = GetDailyBookingLimitBySemesterRequest{}
	mi := &file_proto_semester_limit_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDailyBookingLimitBySemesterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyBookingLimitBySemesterRequest) ProtoMessage() {}

func (x *GetDailyBookingLimitBySemesterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyBookingLimitBySemesterRequest.ProtoReflect.Descriptor instead.
func (*GetDailyBookingLimitBySemesterRequest) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{10}
}

func (x *GetDailyBookingLimitBySemesterRequest) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

// Update daily booking limit request
type UpdateDailyBookingLimitRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MaxBookingsPerDay int32                  `protobuf:"varint,2,opt,name=max_bookings_per_day,json=maxBookingsPerDay,proto3" json:"max_bookings_per_day,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateDailyBookingLimitRequest) Reset() {
	*x = UpdateDailyBookingLimitRequest{}
	mi := &file_proto_semester_limit_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDailyBookingLimitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDailyBookingLimitRequest) ProtoMessage() {}

func (x *UpdateDailyBookingLimitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDailyBookingLimitRequest.ProtoReflect.Descriptor instead.
func (*UpdateDailyBookingLimitRequest) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateDailyBookingLimitRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateDailyBookingLimitRequest) GetMaxBookingsPerDay() int32 {
	if x != nil {
		return x.MaxBookingsPerDay
	}
	return 0
}

// Delete daily booking limit request
type DeleteDailyBookingLimitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDailyBookingLimitRequest) Reset() {
	*x = DeleteDailyBookingLimitRequest{}
	mi := &file_proto_semester_limit_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDailyBookingLimitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDailyBookingLimitRequest) ProtoMessage() {}

func (x *DeleteDailyBookingLimitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDailyBookingLimitRequest.ProtoReflect.Descriptor instead.
func (*DeleteDailyBookingLimitRequest) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteDailyBookingLimitRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// List daily booking limits request
type ListDailyBookingLimitsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDailyBookingLimitsRequest) Reset() {
	*x = ListDailyBookingLimitsRequest{}
	mi := &file_proto_semester_limit_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDailyBookingLimitsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyBookingLimitsRequest) ProtoMessage() {}

func (x *ListDailyBookingLimitsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyBookingLimitsRequest.ProtoReflect.Descriptor instead.
func (*ListDailyBookingLimitsRequest) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{13}
}

func (x *ListDailyBookingLimitsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListDailyBookingLimitsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Daily booking limit response
type DailyBookingLimitResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SemesterId        int64                  `protobuf:"varint,2,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	MaxBookingsPerDay int32                  `protobuf:"varint,3,opt,name=max_bookings_per_day,json=maxBookingsPerDay,proto3" json:"max_bookings_per_day,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt         *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Additional information
	Semester      *SemesterInfo `protobuf:"bytes,6,opt,name=semester,proto3" json:"semester,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DailyBookingLimitResponse) Reset() {
	*x = DailyBookingLimitResponse{}
	mi := &file_proto_semester_limit_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DailyBookingLimitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyBookingLimitResponse) ProtoMessage() {}

func (x *DailyBookingLimitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyBookingLimitResponse.ProtoReflect.Descriptor instead.
func (*DailyBookingLimitResponse) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{14}
}

func (x *DailyBookingLimitResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DailyBookingLimitResponse) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *DailyBookingLimitResponse) GetMaxBookingsPerDay() int32 {
	if x != nil {
		return x.MaxBookingsPerDay
	}
	return 0
}

func (x *DailyBookingLimitResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DailyBookingLimitResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *DailyBookingLimitResponse) GetSemester() *SemesterInfo {
	if x != nil {
		return x.Semester
	}
	return nil
}

// List daily booking limits response
type ListDailyBookingLimitsResponse struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Limits        []*DailyBookingLimitResponse `protobuf:"bytes,1,rep,name=limits,proto3" json:"limits,omitempty"`
	Total         int32                        `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                        `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                        `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDailyBookingLimitsResponse) Reset() {
	*x = ListDailyBookingLimitsResponse{}
	mi := &file_proto_semester_limit_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDailyBookingLimitsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDailyBookingLimitsResponse) ProtoMessage() {}

func (x *ListDailyBookingLimitsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_semester_limit_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDailyBookingLimitsResponse.ProtoReflect.Descriptor instead.
func (*ListDailyBookingLimitsResponse) Descriptor() ([]byte, []int) {
	return file_proto_semester_limit_proto_rawDescGZIP(), []int{15}
}

func (x *ListDailyBookingLimitsResponse) GetLimits() []*DailyBookingLimitResponse {
	if x != nil {
		return x.Limits
	}
	return nil
}

func (x *ListDailyBookingLimitsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListDailyBookingLimitsResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListDailyBookingLimitsResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

var File_proto_semester_limit_proto protoreflect.FileDescriptor

const file_proto_semester_limit_proto_rawDesc = "" +
	"\n" +
	"\x1aproto/semester_limit.proto\x12\asportpb\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x14proto/schedule.proto\"\x7f\n" +
	"\x1aCreateSemesterLimitRequest\x12\x1f\n" +
	"\vsemester_id\x18\x01 \x01(\x03R\n" +
	"semesterId\x12\x1f\n" +
	"\vmin_lessons\x18\x02 \x01(\x05R\n" +
	"minLessons\x12\x1f\n" +
	"\vmax_lessons\x18\x03 \x01(\x05R\n" +
	"maxLessons\")\n" +
	"\x17GetSemesterLimitRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"D\n" +
	"!GetSemesterLimitBySemesterRequest\x12\x1f\n" +
	"\vsemester_id\x18\x01 \x01(\x03R\n" +
	"semesterId\"n\n" +
	"\x1aUpdateSemesterLimitRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vmin_lessons\x18\x02 \x01(\x05R\n" +
	"minLessons\x12\x1f\n" +
	"\vmax_lessons\x18\x03 \x01(\x05R\n" +
	"maxLessons\",\n" +
	"\x1aDeleteSemesterLimitRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"L\n" +
	"\x19ListSemesterLimitsRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\"\xb3\x02\n" +
	"\x15SemesterLimitResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vsemester_id\x18\x02 \x01(\x03R\n" +
	"semesterId\x12\x1f\n" +
	"\vmin_lessons\x18\x03 \x01(\x05R\n" +
	"minLessons\x12\x1f\n" +
	"\vmax_lessons\x18\x04 \x01(\x05R\n" +
	"maxLessons\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x121\n" +
	"\bsemester\x18\a \x01(\v2\x15.sportpb.SemesterInfoR\bsemester\"\x9b\x01\n" +
	"\x1aListSemesterLimitsResponse\x126\n" +
	"\x06limits\x18\x01 \x03(\v2\x1e.sportpb.SemesterLimitResponseR\x06limits\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"r\n" +
	"\x1eCreateDailyBookingLimitRequest\x12\x1f\n" +
	"\vsemester_id\x18\x01 \x01(\x03R\n" +
	"semesterId\x12/\n" +
	"\x14max_bookings_per_day\x18\x02 \x01(\x05R\x11maxBookingsPerDay\"-\n" +
	"\x1bGetDailyBookingLimitRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"H\n" +
	"%GetDailyBookingLimitBySemesterRequest\x12\x1f\n" +
	"\vsemester_id\x18\x01 \x01(\x03R\n" +
	"semesterId\"a\n" +
	"\x1eUpdateDailyBookingLimitRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12/\n" +
	"\x14max_bookings_per_day\x18\x02 \x01(\x05R\x11maxBookingsPerDay\"0\n" +
	"\x1eDeleteDailyBookingLimitRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"P\n" +
	"\x1dListDailyBookingLimitsRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\"\xa6\x02\n" +
	"\x19DailyBookingLimitResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vsemester_id\x18\x02 \x01(\x03R\n" +
	"semesterId\x12/\n" +
	"\x14max_bookings_per_day\x18\x03 \x01(\x05R\x11maxBookingsPerDay\x129\n" +
	"\n" +
	"created_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x121\n" +
	"\bsemester\x18\x06 \x01(\v2\x15.sportpb.SemesterInfoR\bsemester\"\xa3\x01\n" +
	"\x1eListDailyBookingLimitsResponse\x12:\n" +
	"\x06limits\x18\x01 \x03(\v2\".sportpb.DailyBookingLimitResponseR\x06limits\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize2\xc8\t\n" +
	"\x14SemesterLimitService\x12\\\n" +
	"\x13CreateSemesterLimit\x12#.sportpb.CreateSemesterLimitRequest\x1a\x1e.sportpb.SemesterLimitResponse\"\x00\x12V\n" +
	"\x10GetSemesterLimit\x12 .sportpb.GetSemesterLimitRequest\x1a\x1e.sportpb.SemesterLimitResponse\"\x00\x12j\n" +
	"\x1aGetSemesterLimitBySemester\x12*.sportpb.GetSemesterLimitBySemesterRequest\x1a\x1e.sportpb.SemesterLimitResponse\"\x00\x12\\\n" +
	"\x13UpdateSemesterLimit\x12#.sportpb.UpdateSemesterLimitRequest\x1a\x1e.sportpb.SemesterLimitResponse\"\x00\x12T\n" +
	"\x13DeleteSemesterLimit\x12#.sportpb.DeleteSemesterLimitRequest\x1a\x16.google.protobuf.Empty\"\x00\x12_\n" +
	"\x12ListSemesterLimits\x12\".sportpb.ListSemesterLimitsRequest\x1a#.sportpb.ListSemesterLimitsResponse\"\x00\x12h\n" +
	"\x17CreateDailyBookingLimit\x12'.sportpb.CreateDailyBookingLimitRequest\x1a\".sportpb.DailyBookingLimitResponse\"\x00\x12b\n" +
	"\x14GetDailyBookingLimit\x12$.sportpb.GetDailyBookingLimitRequest\x1a\".sportpb.DailyBookingLimitResponse\"\x00\x12v\n" +
	"\x1eGetDailyBookingLimitBySemester\x12..sportpb.GetDailyBookingLimitBySemesterRequest\x1a\".sportpb.DailyBookingLimitResponse\"\x00\x12h\n" +
	"\x17UpdateDailyBookingLimit\x12'.sportpb.UpdateDailyBookingLimitRequest\x1a\".sportpb.DailyBookingLimitResponse\"\x00\x12\\\n" +
	"\x17DeleteDailyBookingLimit\x12'.sportpb.DeleteDailyBookingLimitRequest\x1a\x16.google.protobuf.Empty\"\x00\x12k\n" +
	"\x16ListDailyBookingLimits\x12&.sportpb.ListDailyBookingLimitsRequest\x1a'.sportpb.ListDailyBookingLimitsResponse\"\x00B<Z:github.com/olzzhas/edunite-server/sport_service/pb/sportpbb\x06proto3"

var (
	file_proto_semester_limit_proto_rawDescOnce sync.Once
	file_proto_semester_limit_proto_rawDescData []byte
)

func file_proto_semester_limit_proto_rawDescGZIP() []byte {
	file_proto_semester_limit_proto_rawDescOnce.Do(func() {
		file_proto_semester_limit_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_semester_limit_proto_rawDesc), len(file_proto_semester_limit_proto_rawDesc)))
	})
	return file_proto_semester_limit_proto_rawDescData
}

var file_proto_semester_limit_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_proto_semester_limit_proto_goTypes = []any{
	(*CreateSemesterLimitRequest)(nil),            // 0: sportpb.CreateSemesterLimitRequest
	(*GetSemesterLimitRequest)(nil),               // 1: sportpb.GetSemesterLimitRequest
	(*GetSemesterLimitBySemesterRequest)(nil),     // 2: sportpb.GetSemesterLimitBySemesterRequest
	(*UpdateSemesterLimitRequest)(nil),            // 3: sportpb.UpdateSemesterLimitRequest
	(*DeleteSemesterLimitRequest)(nil),            // 4: sportpb.DeleteSemesterLimitRequest
	(*ListSemesterLimitsRequest)(nil),             // 5: sportpb.ListSemesterLimitsRequest
	(*SemesterLimitResponse)(nil),                 // 6: sportpb.SemesterLimitResponse
	(*ListSemesterLimitsResponse)(nil),            // 7: sportpb.ListSemesterLimitsResponse
	(*CreateDailyBookingLimitRequest)(nil),        // 8: sportpb.CreateDailyBookingLimitRequest
	(*GetDailyBookingLimitRequest)(nil),           // 9: sportpb.GetDailyBookingLimitRequest
	(*GetDailyBookingLimitBySemesterRequest)(nil), // 10: sportpb.GetDailyBookingLimitBySemesterRequest
	(*UpdateDailyBookingLimitRequest)(nil),        // 11: sportpb.UpdateDailyBookingLimitRequest
	(*DeleteDailyBookingLimitRequest)(nil),        // 12: sportpb.DeleteDailyBookingLimitRequest
	(*ListDailyBookingLimitsRequest)(nil),         // 13: sportpb.ListDailyBookingLimitsRequest
	(*DailyBookingLimitResponse)(nil),             // 14: sportpb.DailyBookingLimitResponse
	(*ListDailyBookingLimitsResponse)(nil),        // 15: sportpb.ListDailyBookingLimitsResponse
	(*timestamppb.Timestamp)(nil),                 // 16: google.protobuf.Timestamp
	(*SemesterInfo)(nil),                          // 17: sportpb.SemesterInfo
	(*emptypb.Empty)(nil),                         // 18: google.protobuf.Empty
}
var file_proto_semester_limit_proto_depIdxs = []int32{
	16, // 0: sportpb.SemesterLimitResponse.created_at:type_name -> google.protobuf.Timestamp
	16, // 1: sportpb.SemesterLimitResponse.updated_at:type_name -> google.protobuf.Timestamp
	17, // 2: sportpb.SemesterLimitResponse.semester:type_name -> sportpb.SemesterInfo
	6,  // 3: sportpb.ListSemesterLimitsResponse.limits:type_name -> sportpb.SemesterLimitResponse
	16, // 4: sportpb.DailyBookingLimitResponse.created_at:type_name -> google.protobuf.Timestamp
	16, // 5: sportpb.DailyBookingLimitResponse.updated_at:type_name -> google.protobuf.Timestamp
	17, // 6: sportpb.DailyBookingLimitResponse.semester:type_name -> sportpb.SemesterInfo
	14, // 7: sportpb.ListDailyBookingLimitsResponse.limits:type_name -> sportpb.DailyBookingLimitResponse
	0,  // 8: sportpb.SemesterLimitService.CreateSemesterLimit:input_type -> sportpb.CreateSemesterLimitRequest
	1,  // 9: sportpb.SemesterLimitService.GetSemesterLimit:input_type -> sportpb.GetSemesterLimitRequest
	2,  // 10: sportpb.SemesterLimitService.GetSemesterLimitBySemester:input_type -> sportpb.GetSemesterLimitBySemesterRequest
	3,  // 11: sportpb.SemesterLimitService.UpdateSemesterLimit:input_type -> sportpb.UpdateSemesterLimitRequest
	4,  // 12: sportpb.SemesterLimitService.DeleteSemesterLimit:input_type -> sportpb.DeleteSemesterLimitRequest
	5,  // 13: sportpb.SemesterLimitService.ListSemesterLimits:input_type -> sportpb.ListSemesterLimitsRequest
	8,  // 14: sportpb.SemesterLimitService.CreateDailyBookingLimit:input_type -> sportpb.CreateDailyBookingLimitRequest
	9,  // 15: sportpb.SemesterLimitService.GetDailyBookingLimit:input_type -> sportpb.GetDailyBookingLimitRequest
	10, // 16: sportpb.SemesterLimitService.GetDailyBookingLimitBySemester:input_type -> sportpb.GetDailyBookingLimitBySemesterRequest
	11, // 17: sportpb.SemesterLimitService.UpdateDailyBookingLimit:input_type -> sportpb.UpdateDailyBookingLimitRequest
	12, // 18: sportpb.SemesterLimitService.DeleteDailyBookingLimit:input_type -> sportpb.DeleteDailyBookingLimitRequest
	13, // 19: sportpb.SemesterLimitService.ListDailyBookingLimits:input_type -> sportpb.ListDailyBookingLimitsRequest
	6,  // 20: sportpb.SemesterLimitService.CreateSemesterLimit:output_type -> sportpb.SemesterLimitResponse
	6,  // 21: sportpb.SemesterLimitService.GetSemesterLimit:output_type -> sportpb.SemesterLimitResponse
	6,  // 22: sportpb.SemesterLimitService.GetSemesterLimitBySemester:output_type -> sportpb.SemesterLimitResponse
	6,  // 23: sportpb.SemesterLimitService.UpdateSemesterLimit:output_type -> sportpb.SemesterLimitResponse
	18, // 24: sportpb.SemesterLimitService.DeleteSemesterLimit:output_type -> google.protobuf.Empty
	7,  // 25: sportpb.SemesterLimitService.ListSemesterLimits:output_type -> sportpb.ListSemesterLimitsResponse
	14, // 26: sportpb.SemesterLimitService.CreateDailyBookingLimit:output_type -> sportpb.DailyBookingLimitResponse
	14, // 27: sportpb.SemesterLimitService.GetDailyBookingLimit:output_type -> sportpb.DailyBookingLimitResponse
	14, // 28: sportpb.SemesterLimitService.GetDailyBookingLimitBySemester:output_type -> sportpb.DailyBookingLimitResponse
	14, // 29: sportpb.SemesterLimitService.UpdateDailyBookingLimit:output_type -> sportpb.DailyBookingLimitResponse
	18, // 30: sportpb.SemesterLimitService.DeleteDailyBookingLimit:output_type -> google.protobuf.Empty
	15, // 31: sportpb.SemesterLimitService.ListDailyBookingLimits:output_type -> sportpb.ListDailyBookingLimitsResponse
	20, // [20:32] is the sub-list for method output_type
	8,  // [8:20] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_proto_semester_limit_proto_init() }
func file_proto_semester_limit_proto_init() {
	if File_proto_semester_limit_proto != nil {
		return
	}
	file_proto_schedule_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_semester_limit_proto_rawDesc), len(file_proto_semester_limit_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_semester_limit_proto_goTypes,
		DependencyIndexes: file_proto_semester_limit_proto_depIdxs,
		MessageInfos:      file_proto_semester_limit_proto_msgTypes,
	}.Build()
	File_proto_semester_limit_proto = out.File
	file_proto_semester_limit_proto_goTypes = nil
	file_proto_semester_limit_proto_depIdxs = nil
}
