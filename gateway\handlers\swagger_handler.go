package handlers

import (
	"github.com/gin-gonic/gin"
	"net/http"
)

// SwaggerHandler handles requests for the Swagger UI
type SwaggerHandler struct{}

// NewSwaggerHandler creates a new SwaggerHandler
func NewSwaggerHandler() *SwaggerHandler {
	return &SwaggerHandler{}
}

// ServeSwaggerUI serves the Swagger UI
func (h *SwaggerHandler) ServeSwaggerUI(c *gin.Context) {
	// Redirect to the Swagger UI index.html
	c.Redirect(http.StatusMovedPermanently, "/docs/swagger-ui/")
}
