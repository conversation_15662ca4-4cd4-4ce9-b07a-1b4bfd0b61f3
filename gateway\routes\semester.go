package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

// SetupSemesterRoutes настраивает маршруты для семестров
func SetupSemesterRoutes(r *gin.Engine, semesterHandler *handlers.SemesterHandler) {
	semestersGroup := r.Group("/semester")
	{
		semestersGroup.POST("", semesterHandler.CreateSemesterHandler)       // POST /semesters
		semestersGroup.GET("", semesterHandler.ListSemestersHandler)         // GET /semesters
		semestersGroup.GET("/:id", semesterHandler.GetSemesterByIDHandler)   // GET /semesters/:id
		semestersGroup.PUT("/:id", semesterHandler.UpdateSemesterHandler)    // PUT /semesters/:id
		semestersGroup.DELETE("/:id", semesterHandler.DeleteSemesterHandler) // DELETE /semesters/:id

		semestersGroup.GET("/with-breaks", semesterHandler.ListSemestersWithBreaksHandler) // GET /semester/with-breaks
		semestersGroup.POST("/breaks/:id", semesterHandler.AddSemesterBreakHandler)
	}
}
