package service

import (
	"github.com/olzzhas/edunite-server/sport_service/internal/repository"
)

// NewServices creates all services
func NewServices(repos *repository.Repositories) *Services {
	bookingService := NewBookingService(
		repos.Booking,
		repos.Schedule,
		repos.Facility,
		repos.SportType,
		repos.MedicalCertificate,
		repos.SemesterSportLimit,
		repos.DailyBookingLimit,
	)
	
	scheduleService := NewScheduleService(
		repos.Schedule,
		repos.Facility,
	)
	
	medicalCertificateService := NewMedicalCertificateService(
		repos.MedicalCertificate,
		repos.TeacherSportType,
	)
	
	semesterSportLimitService := NewSemesterSportLimitService(
		repos.SemesterSportLimit,
		repos.SportType,
	)
	
	dailyBookingLimitService := NewDailyBookingLimitService(
		repos.DailyBookingLimit,
	)
	
	physicalEducationService := NewPhysicalEducationService(
		bookingService,
		scheduleService,
		medicalCertificateService,
		semesterSportLimitService,
		dailyBookingLimitService,
		repos.SportType,
		repos.Facility,
	)
	
	return &Services{
		Booking:            bookingService,
		Schedule:           scheduleService,
		MedicalCertificate: medicalCertificateService,
		SemesterSportLimit: semesterSportLimitService,
		DailyBookingLimit:  dailyBookingLimitService,
		PhysicalEducation:  physicalEducationService,
	}
}

// Services holds all service implementations
type Services struct {
	Booking            *BookingService
	Schedule           *ScheduleService
	MedicalCertificate *MedicalCertificateService
	SemesterSportLimit *SemesterSportLimitService
	DailyBookingLimit  *DailyBookingLimitService
	PhysicalEducation  *PhysicalEducationService
}
