package main

import (
	"context"
	"fmt"
	"log"
	"time"

	transcriptpb "github.com/olzzhas/edunite-server/course_service/pb/transcript"
	"google.golang.org/grpc"
)

func main() {
	// Connect directly to the course service
	conn, err := grpc.Dial("localhost:50053", grpc.WithInsecure())
	if err != nil {
		log.Fatalf("Failed to connect to course service: %v", err)
	}
	defer conn.Close()

	client := transcriptpb.NewTranscriptServiceClient(conn)

	fmt.Println("🔍 Testing direct gRPC connection to course service...")

	// Test 1: List degrees (should work)
	fmt.Println("\n1. Testing ListDegrees...")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	degreesResp, err := client.ListDegrees(ctx, &transcriptpb.ListDegreesRequest{
		Page:     1,
		PageSize: 10,
	})
	if err != nil {
		fmt.Printf("❌ ListDegrees failed: %v\n", err)
		if err.<PERSON>rror() == "can't scan into dest[12]: unknown oid 17100 cannot be scanned into *interface {}" {
			fmt.Println("🚨 OID 17100 error detected!")
		}
	} else {
		fmt.Printf("✅ ListDegrees successful: Found %d degrees\n", len(degreesResp.Degrees))
		for _, degree := range degreesResp.Degrees {
			fmt.Printf("  - %s (%s)\n", degree.Name, degree.Level.String())
		}
	}

	// Test 2: Try to get a transcript for user ID 4 (this was failing before)
	fmt.Println("\n2. Testing GetTranscript for user ID 4...")
	transcriptResp, err := client.GetTranscript(ctx, &transcriptpb.GetTranscriptRequest{
		UserId: 4,
	})
	if err != nil {
		// Check if it's the expected "not found" error or the OID scanning error
		if err.Error() == "rpc error: code = NotFound desc = transcript not found" {
			fmt.Println("✅ GetTranscript returned expected 'not found' error (no OID scanning error)")
		} else if err.Error() == "can't scan into dest[12]: unknown oid 17100 cannot be scanned into *interface {}" {
			fmt.Printf("❌ OID 17100 error still exists: %v\n", err)
		} else {
			fmt.Printf("ℹ️  GetTranscript returned different error: %v\n", err)
		}
	} else {
		fmt.Printf("✅ GetTranscript successful: Found transcript for user %d\n", transcriptResp.Transcript.UserId)
	}

	// Test 3: Test with degree ID parameter
	fmt.Println("\n3. Testing GetTranscript with degree ID...")
	_, err = client.GetTranscript(ctx, &transcriptpb.GetTranscriptRequest{
		UserId:   4,
		DegreeId: 1,
	})
	if err != nil {
		if err.Error() == "rpc error: code = NotFound desc = transcript not found" {
			fmt.Println("✅ GetTranscript with degree ID returned expected 'not found' error")
		} else if err.Error() == "can't scan into dest[12]: unknown oid 17100 cannot be scanned into *interface {}" {
			fmt.Printf("❌ OID 17100 error still exists: %v\n", err)
		} else {
			fmt.Printf("ℹ️  GetTranscript with degree ID returned: %v\n", err)
		}
	} else {
		fmt.Printf("✅ GetTranscript with degree ID successful!\n")
	}

	fmt.Println("\n🎉 Direct gRPC test completed!")
	fmt.Println("If no OID 17100 errors were reported, the database scanning fix is working!")
}
