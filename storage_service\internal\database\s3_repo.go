package database

import (
	"bytes"
	"context"
	"fmt"
	"io"

	"github.com/aws/aws-sdk-go-v2/aws"
	awsconfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	localconfig "github.com/olzzhas/edunite-server/storage_service/internal/config"
)

// S3Repository - interface for AWS S3 storage
type S3Repository interface {
	Upload(ctx context.Context, bucketName, objectName string, data []byte, contentType string) (string, error)
	Download(ctx context.Context, bucketName, objectName string) ([]byte, string, error)
	Delete(ctx context.Context, bucketName, objectName string) error
}

type s3Repository struct {
	client *s3.Client
	config *localconfig.Config
}

// NewS3Repository creates a new S3Repository
func NewS3Repository(cfg *localconfig.Config) (S3Repository, error) {
	// Create AWS credentials from config
	creds := credentials.NewStaticCredentialsProvider(cfg.AWSAccessKey, cfg.AWSSecretKey, "")

	// Load AWS configuration
	awsCfg, err := awsconfig.LoadDefaultConfig(context.Background(),
		awsconfig.WithCredentialsProvider(creds),
		awsconfig.WithRegion("eu-north-1"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}

	// Create S3 client with a custom endpoint URL and force path style
	s3Client := s3.NewFromConfig(awsCfg, func(o *s3.Options) {
		o.UsePathStyle = true
		o.BaseEndpoint = aws.String("https://s3.eu-north-1.amazonaws.com")
	})

	repo := &s3Repository{
		client: s3Client,
		config: cfg,
	}

	// Initialize default buckets with retries
	if err := repo.initializeDefaultBuckets(); err != nil {
		return nil, fmt.Errorf("failed to initialize default buckets: %w", err)
	}

	return repo, nil
}

// initializeDefaultBuckets creates default buckets and sets policies
func (r *s3Repository) initializeDefaultBuckets() error {
	ctx := context.Background()

	// Just try once, since we're now handling BucketAlreadyExists errors
	err := r.createDefaultBuckets(ctx)
	if err != nil {
		return fmt.Errorf("failed to initialize S3 buckets: %w", err)
	}

	fmt.Println("Successfully initialized S3 buckets")
	return nil
}

// createDefaultBuckets creates the default buckets
func (r *s3Repository) createDefaultBuckets(ctx context.Context) error {
	// Skip bucket creation since they already exist
	fmt.Println("Skipping bucket creation - using existing buckets")
	return nil
}

// Upload uploads a file to the specified bucket
func (r *s3Repository) Upload(ctx context.Context, bucketName, objectName string, data []byte, contentType string) (string, error) {
	reader := bytes.NewReader(data)

	// Skip bucket creation - assume buckets already exist
	// Just upload the file directly
	_, err := r.client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:      aws.String(bucketName),
		Key:         aws.String(objectName),
		Body:        reader,
		ContentType: aws.String(contentType),
	})
	if err != nil {
		return "", fmt.Errorf("failed to upload object: %w", err)
	}

	// Generate the URL for the uploaded file
	// Use the correct endpoint format for the region
	fileURL := fmt.Sprintf("https://s3.eu-north-1.amazonaws.com/%s/%s", bucketName, objectName)
	return fileURL, nil
}

// Download downloads a file from the specified bucket
func (r *s3Repository) Download(ctx context.Context, bucketName, objectName string) ([]byte, string, error) {
	// Get the object
	resp, err := r.client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(objectName),
	})
	if err != nil {
		return nil, "", fmt.Errorf("failed to get object: %w", err)
	}
	defer resp.Body.Close()

	// Read the object data
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "", fmt.Errorf("read object error: %w", err)
	}

	// Get content type
	contentType := ""
	if resp.ContentType != nil {
		contentType = *resp.ContentType
	}

	return data, contentType, nil
}

// Delete deletes an object from the specified bucket
func (r *s3Repository) Delete(ctx context.Context, bucketName, objectName string) error {
	_, err := r.client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(objectName),
	})
	if err != nil {
		return fmt.Errorf("failed to remove object: %w", err)
	}
	return nil
}
