{"id": "edunite", "realm": "edunite", "enabled": true, "displayName": "Edunite Realm", "roles": {"realm": [{"name": "student", "description": "Student role"}, {"name": "teacher", "description": "Teacher role"}, {"name": "moderator", "description": "Moderator role"}, {"name": "admin", "description": "Administrator role"}], "client": {}}, "defaultRoles": ["student"], "users": [{"username": "service-account-auth", "serviceAccountClientId": "auth", "enabled": true, "clientRoles": {"realm-management": ["manage-users"]}}, {"username": "admin", "enabled": true, "email": "<EMAIL>", "firstName": "Admin", "lastName": "User", "credentials": [{"type": "password", "value": "admin123", "temporary": false}], "realmRoles": ["admin"]}], "clients": [{"clientId": "auth", "name": "Auth Client", "enabled": true, "protocol": "openid-connect", "publicClient": false, "secret": "302d13e02e28077f6cc3ebe68782230e2e5a7b6fcf708411b52693ea28d14029", "clientAuthenticatorType": "client-secret", "redirectUris": ["*"], "webOrigins": ["*"], "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "bearerOnly": false}], "internationalizationEnabled": false}