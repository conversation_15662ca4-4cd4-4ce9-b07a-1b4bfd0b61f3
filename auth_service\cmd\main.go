package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/olzzhas/edunite-server/auth_service/internal/config"
	"github.com/olzzhas/edunite-server/auth_service/internal/server"
)

func main() {
	// Load configuration
	cfg := config.LoadConfig()

	// Create server
	srv := server.NewServer(cfg)

	// Handle graceful shutdown
	go func() {
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, os.Interrupt, syscall.SIGTERM)
		<-sigCh
		log.Println("Shutting down server...")
		srv.Stop()
	}()

	// Start server
	if err := srv.Start(); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
