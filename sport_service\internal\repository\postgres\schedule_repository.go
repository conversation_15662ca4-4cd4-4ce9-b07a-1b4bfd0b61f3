package postgres

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

type scheduleRepository struct {
	db *pgxpool.Pool
}

// CheckConflict checks if a schedule conflicts with existing schedules
// Returns true if there's a conflict, false otherwise
func (r *scheduleRepository) CheckConflict(ctx context.Context, schedule *domain.Schedule) (bool, error) {
	// Check for teacher conflicts (teacher can't be in two places at once)
	teacherQuery := `
		SELECT 1 FROM sport_schedules
		WHERE teacher_id = $1
		AND id != $2
		AND (
			(start_time <= $3 AND end_time > $3) OR  -- New start time is during an existing schedule
			(start_time < $4 AND end_time >= $4) OR  -- New end time is during an existing schedule
			(start_time >= $3 AND end_time <= $4)    -- Existing schedule is completely within new schedule
		)
		LIMIT 1
	`

	var dummy int
	err := r.db.QueryRow(ctx, teacherQuery,
		schedule.TeacherID,
		schedule.ID, // Use 0 for new schedules
		schedule.StartTime,
		schedule.EndTime,
	).Scan(&dummy)

	if err != nil && !errors.Is(err, pgx.ErrNoRows) {
		return false, fmt.Errorf("failed to check teacher conflict: %w", err)
	}

	if err == nil {
		// Found a conflict with teacher schedule
		return true, nil
	}

	// Check for facility conflicts (facility can't be double-booked)
	facilityQuery := `
		SELECT 1 FROM sport_schedules
		WHERE facility_id = $1
		AND id != $2
		AND (
			(start_time <= $3 AND end_time > $3) OR  -- New start time is during an existing schedule
			(start_time < $4 AND end_time >= $4) OR  -- New end time is during an existing schedule
			(start_time >= $3 AND end_time <= $4)    -- Existing schedule is completely within new schedule
		)
		LIMIT 1
	`

	err = r.db.QueryRow(ctx, facilityQuery,
		schedule.FacilityID,
		schedule.ID, // Use 0 for new schedules
		schedule.StartTime,
		schedule.EndTime,
	).Scan(&dummy)

	if err != nil && !errors.Is(err, pgx.ErrNoRows) {
		return false, fmt.Errorf("failed to check facility conflict: %w", err)
	}

	if err == nil {
		// Found a conflict with facility schedule
		return true, nil
	}

	// No conflicts found
	return false, nil
}

// NewScheduleRepository creates a new schedule repository
func NewScheduleRepository(db *pgxpool.Pool) *scheduleRepository {
	return &scheduleRepository{db: db}
}

// Create creates a new schedule
func (r *scheduleRepository) Create(ctx context.Context, schedule *domain.Schedule) error {
	// Check for conflicts
	hasConflict, err := r.CheckConflict(ctx, schedule)
	if err != nil {
		return fmt.Errorf("failed to check for conflicts: %w", err)
	}
	if hasConflict {
		return domain.ErrScheduleConflict
	}

	query := `
		INSERT INTO sport_schedules (facility_id, teacher_id, semester_id, sport_type_id, start_time, end_time, cancellation_deadline, location)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id, created_at, updated_at, version
	`

	err = r.db.QueryRow(ctx, query,
		schedule.FacilityID,
		schedule.TeacherID,
		schedule.SemesterID,
		schedule.SportTypeID,
		schedule.StartTime,
		schedule.EndTime,
		schedule.CancellationDeadline,
		schedule.Location,
	).Scan(
		&schedule.ID,
		&schedule.CreatedAt,
		&schedule.UpdatedAt,
		&schedule.Version,
	)

	if err != nil {
		return fmt.Errorf("failed to create schedule: %w", err)
	}

	return nil
}

// GetByID retrieves a schedule by ID
func (r *scheduleRepository) GetByID(ctx context.Context, id int64) (*domain.Schedule, error) {
	query := `
		SELECT id, facility_id, teacher_id, semester_id, start_time, end_time, cancellation_deadline, location, created_at, updated_at, version
		FROM sport_schedules
		WHERE id = $1
	`

	var schedule domain.Schedule
	err := r.db.QueryRow(ctx, query, id).Scan(
		&schedule.ID,
		&schedule.FacilityID,
		&schedule.TeacherID,
		&schedule.SemesterID,
		&schedule.StartTime,
		&schedule.EndTime,
		&schedule.CancellationDeadline,
		&schedule.Location,
		&schedule.CreatedAt,
		&schedule.UpdatedAt,
		&schedule.Version,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, domain.ErrScheduleNotFound
		}
		return nil, fmt.Errorf("failed to get schedule: %w", err)
	}

	return &schedule, nil
}

// Update updates an existing schedule
func (r *scheduleRepository) Update(ctx context.Context, schedule *domain.Schedule) error {
	// Check for conflicts
	hasConflict, err := r.CheckConflict(ctx, schedule)
	if err != nil {
		return fmt.Errorf("failed to check for conflicts: %w", err)
	}
	if hasConflict {
		return domain.ErrScheduleConflict
	}

	query := `
		UPDATE sport_schedules
		SET facility_id = $1, teacher_id = $2, semester_id = $3, sport_type_id = $4, start_time = $5, end_time = $6, cancellation_deadline = $7, location = $8, version = version + 1
		WHERE id = $9 AND version = $10
		RETURNING updated_at, version
	`

	err = r.db.QueryRow(ctx, query,
		schedule.FacilityID,
		schedule.TeacherID,
		schedule.SemesterID,
		schedule.SportTypeID,
		schedule.StartTime,
		schedule.EndTime,
		schedule.CancellationDeadline,
		schedule.Location,
		schedule.ID,
		schedule.Version,
	).Scan(
		&schedule.UpdatedAt,
		&schedule.Version,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return domain.ErrScheduleNotFound
		}
		return fmt.Errorf("failed to update schedule: %w", err)
	}

	return nil
}

// Delete deletes a schedule by ID
func (r *scheduleRepository) Delete(ctx context.Context, id int64) error {
	query := `
		DELETE FROM sport_schedules
		WHERE id = $1
	`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete schedule: %w", err)
	}

	if result.RowsAffected() == 0 {
		return domain.ErrScheduleNotFound
	}

	return nil
}

// List retrieves schedules based on filters
func (r *scheduleRepository) List(ctx context.Context, filter domain.ScheduleFilter) ([]*domain.Schedule, error) {
	query := `
		SELECT id, facility_id, teacher_id, semester_id, start_time, end_time, cancellation_deadline, location, created_at, updated_at, version
		FROM sport_schedules
		WHERE 1=1
	`

	var args []interface{}
	var conditions []string

	// Add filter conditions
	if filter.FacilityID != 0 {
		args = append(args, filter.FacilityID)
		conditions = append(conditions, fmt.Sprintf("AND facility_id = $%d", len(args)))
	}

	if filter.TeacherID != 0 {
		args = append(args, filter.TeacherID)
		conditions = append(conditions, fmt.Sprintf("AND teacher_id = $%d", len(args)))
	}

	if filter.SemesterID != 0 {
		args = append(args, filter.SemesterID)
		conditions = append(conditions, fmt.Sprintf("AND semester_id = $%d", len(args)))
	}

	if !filter.StartDate.IsZero() {
		args = append(args, filter.StartDate)
		conditions = append(conditions, fmt.Sprintf("AND start_time >= $%d", len(args)))
	}

	if !filter.EndDate.IsZero() {
		args = append(args, filter.EndDate)
		conditions = append(conditions, fmt.Sprintf("AND end_time <= $%d", len(args)))
	}

	if filter.Location != "" {
		args = append(args, "%"+filter.Location+"%")
		conditions = append(conditions, fmt.Sprintf("AND location ILIKE $%d", len(args)))
	}

	// Add pagination
	if filter.Page <= 0 {
		filter.Page = 1
	}

	if filter.PageSize <= 0 {
		filter.PageSize = 10
	}

	offset := (filter.Page - 1) * filter.PageSize

	for _, condition := range conditions {
		query += " " + condition
	}

	query += " ORDER BY start_time"
	query += fmt.Sprintf(" LIMIT %d OFFSET %d", filter.PageSize, offset)

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to list schedules: %w", err)
	}
	defer rows.Close()

	var schedules []*domain.Schedule
	for rows.Next() {
		var schedule domain.Schedule
		err := rows.Scan(
			&schedule.ID,
			&schedule.FacilityID,
			&schedule.TeacherID,
			&schedule.SemesterID,
			&schedule.StartTime,
			&schedule.EndTime,
			&schedule.CancellationDeadline,
			&schedule.Location,
			&schedule.CreatedAt,
			&schedule.UpdatedAt,
			&schedule.Version,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan schedule: %w", err)
		}
		schedules = append(schedules, &schedule)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating schedules: %w", err)
	}

	return schedules, nil
}

// Count counts schedules based on filters
func (r *scheduleRepository) Count(ctx context.Context, filter domain.ScheduleFilter) (int, error) {
	query := `
		SELECT COUNT(*)
		FROM sport_schedules
		WHERE 1=1
	`

	var args []interface{}
	var conditions []string

	// Add filter conditions
	if filter.FacilityID != 0 {
		args = append(args, filter.FacilityID)
		conditions = append(conditions, fmt.Sprintf("AND facility_id = $%d", len(args)))
	}

	if filter.TeacherID != 0 {
		args = append(args, filter.TeacherID)
		conditions = append(conditions, fmt.Sprintf("AND teacher_id = $%d", len(args)))
	}

	if filter.SemesterID != 0 {
		args = append(args, filter.SemesterID)
		conditions = append(conditions, fmt.Sprintf("AND semester_id = $%d", len(args)))
	}

	if !filter.StartDate.IsZero() {
		args = append(args, filter.StartDate)
		conditions = append(conditions, fmt.Sprintf("AND start_time >= $%d", len(args)))
	}

	if !filter.EndDate.IsZero() {
		args = append(args, filter.EndDate)
		conditions = append(conditions, fmt.Sprintf("AND end_time <= $%d", len(args)))
	}

	if filter.Location != "" {
		args = append(args, "%"+filter.Location+"%")
		conditions = append(conditions, fmt.Sprintf("AND location ILIKE $%d", len(args)))
	}

	for _, condition := range conditions {
		query += " " + condition
	}

	var count int
	err := r.db.QueryRow(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count schedules: %w", err)
	}

	return count, nil
}

// GetByTeacherIDAndTimeRange retrieves schedules for a teacher within a time range
func (r *scheduleRepository) GetByTeacherIDAndTimeRange(ctx context.Context, teacherID int64, start, end time.Time) ([]*domain.Schedule, error) {
	query := `
		SELECT id, facility_id, teacher_id, semester_id, start_time, end_time, cancellation_deadline, location, created_at, updated_at, version
		FROM sport_schedules
		WHERE teacher_id = $1
		AND (
			(start_time >= $2 AND start_time < $3) OR
			(end_time > $2 AND end_time <= $3) OR
			(start_time <= $2 AND end_time >= $3)
		)
		ORDER BY start_time
	`

	rows, err := r.db.Query(ctx, query, teacherID, start, end)
	if err != nil {
		return nil, fmt.Errorf("failed to get schedules by teacher and time range: %w", err)
	}
	defer rows.Close()

	var schedules []*domain.Schedule
	for rows.Next() {
		var schedule domain.Schedule
		err := rows.Scan(
			&schedule.ID,
			&schedule.FacilityID,
			&schedule.TeacherID,
			&schedule.SemesterID,
			&schedule.StartTime,
			&schedule.EndTime,
			&schedule.CancellationDeadline,
			&schedule.Location,
			&schedule.CreatedAt,
			&schedule.UpdatedAt,
			&schedule.Version,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan schedule: %w", err)
		}
		schedules = append(schedules, &schedule)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating schedules: %w", err)
	}

	return schedules, nil
}

// GetByFacilityIDAndTimeRange retrieves schedules for a facility within a time range
func (r *scheduleRepository) GetByFacilityIDAndTimeRange(ctx context.Context, facilityID int64, start, end time.Time) ([]*domain.Schedule, error) {
	query := `
		SELECT id, facility_id, teacher_id, semester_id, start_time, end_time, cancellation_deadline, location, created_at, updated_at, version
		FROM sport_schedules
		WHERE facility_id = $1
		AND (
			(start_time >= $2 AND start_time < $3) OR
			(end_time > $2 AND end_time <= $3) OR
			(start_time <= $2 AND end_time >= $3)
		)
		ORDER BY start_time
	`

	rows, err := r.db.Query(ctx, query, facilityID, start, end)
	if err != nil {
		return nil, fmt.Errorf("failed to get schedules by facility and time range: %w", err)
	}
	defer rows.Close()

	var schedules []*domain.Schedule
	for rows.Next() {
		var schedule domain.Schedule
		err := rows.Scan(
			&schedule.ID,
			&schedule.FacilityID,
			&schedule.TeacherID,
			&schedule.SemesterID,
			&schedule.StartTime,
			&schedule.EndTime,
			&schedule.CancellationDeadline,
			&schedule.Location,
			&schedule.CreatedAt,
			&schedule.UpdatedAt,
			&schedule.Version,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan schedule: %w", err)
		}
		schedules = append(schedules, &schedule)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating schedules: %w", err)
	}

	return schedules, nil
}

// GetBySemesterID retrieves schedules for a semester
func (r *scheduleRepository) GetBySemesterID(ctx context.Context, semesterID int64) ([]*domain.Schedule, error) {
	query := `
		SELECT id, facility_id, teacher_id, semester_id, start_time, end_time, cancellation_deadline, location, created_at, updated_at, version
		FROM sport_schedules
		WHERE semester_id = $1
		ORDER BY start_time
	`

	rows, err := r.db.Query(ctx, query, semesterID)
	if err != nil {
		return nil, fmt.Errorf("failed to get schedules by semester: %w", err)
	}
	defer rows.Close()

	var schedules []*domain.Schedule
	for rows.Next() {
		var schedule domain.Schedule
		err := rows.Scan(
			&schedule.ID,
			&schedule.FacilityID,
			&schedule.TeacherID,
			&schedule.SemesterID,
			&schedule.StartTime,
			&schedule.EndTime,
			&schedule.CancellationDeadline,
			&schedule.Location,
			&schedule.CreatedAt,
			&schedule.UpdatedAt,
			&schedule.Version,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan schedule: %w", err)
		}
		schedules = append(schedules, &schedule)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating schedules: %w", err)
	}

	return schedules, nil
}

// CreateBatch creates multiple schedules at once (for weekly schedules)
func (r *scheduleRepository) CreateBatch(ctx context.Context, schedules []*domain.Schedule) error {
	// First, check for conflicts for all schedules
	for _, schedule := range schedules {
		hasConflict, err := r.CheckConflict(ctx, schedule)
		if err != nil {
			return fmt.Errorf("failed to check for conflicts: %w", err)
		}
		if hasConflict {
			return domain.ErrScheduleConflict
		}
	}

	// Begin transaction
	tx, err := r.db.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	query := `
		INSERT INTO sport_schedules (facility_id, teacher_id, semester_id, sport_type_id, start_time, end_time, cancellation_deadline, location)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id, created_at, updated_at, version
	`

	for i := range schedules {
		err := tx.QueryRow(ctx, query,
			schedules[i].FacilityID,
			schedules[i].TeacherID,
			schedules[i].SemesterID,
			schedules[i].SportTypeID,
			schedules[i].StartTime,
			schedules[i].EndTime,
			schedules[i].CancellationDeadline,
			schedules[i].Location,
		).Scan(
			&schedules[i].ID,
			&schedules[i].CreatedAt,
			&schedules[i].UpdatedAt,
			&schedules[i].Version,
		)

		if err != nil {
			return fmt.Errorf("failed to create schedule in batch: %w", err)
		}
	}

	if err := tx.Commit(ctx); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}
