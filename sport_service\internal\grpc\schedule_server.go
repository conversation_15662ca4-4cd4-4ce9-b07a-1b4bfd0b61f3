package grpc

import (
	"context"
	"errors"
	"time"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
	"github.com/olzzhas/edunite-server/sport_service/internal/service"
	sportpb "github.com/olzzhas/edunite-server/sport_service/proto"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type scheduleServer struct {
	sportpb.UnimplementedScheduleServiceServer
	services *service.Services
}

// NewScheduleServer creates a new schedule gRPC server
func NewScheduleServer(services *service.Services) *scheduleServer {
	return &scheduleServer{
		services: services,
	}
}

// CreateSchedule creates a new schedule
func (s *scheduleServer) CreateSchedule(ctx context.Context, req *sportpb.CreateScheduleRequest) (*sportpb.ScheduleResponse, error) {
	schedule := &domain.Schedule{
		FacilityID:           req.FacilityId,
		TeacherID:            req.TeacherId,
		SemesterID:           req.SemesterId,
		StartTime:            req.StartTime.AsTime(),
		EndTime:              req.EndTime.AsTime(),
		CancellationDeadline: req.CancellationDeadline.AsTime(),
		Location:             req.Location,
	}

	// Create the schedule
	err := s.services.Schedule.CreateSchedule(ctx, schedule)
	if err != nil {
		if errors.Is(err, domain.ErrScheduleConflict) {
			return nil, status.Errorf(codes.FailedPrecondition, "schedule conflicts with existing schedules: teacher or facility is already booked for this time")
		}
		return nil, status.Errorf(codes.Internal, "failed to create schedule: %v", err)
	}

	return s.convertScheduleToProto(ctx, schedule)
}

// GetSchedule retrieves a schedule by ID
func (s *scheduleServer) GetSchedule(ctx context.Context, req *sportpb.GetScheduleRequest) (*sportpb.ScheduleResponse, error) {
	schedule, err := s.services.Schedule.GetSchedule(ctx, req.Id)
	if err != nil {
		if err == domain.ErrScheduleNotFound {
			return nil, status.Errorf(codes.NotFound, "schedule not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to get schedule: %v", err)
	}

	return s.convertScheduleToProto(ctx, schedule)
}

// UpdateSchedule updates an existing schedule
func (s *scheduleServer) UpdateSchedule(ctx context.Context, req *sportpb.UpdateScheduleRequest) (*sportpb.ScheduleResponse, error) {
	schedule := &domain.Schedule{
		ID:                   req.Id,
		FacilityID:           req.FacilityId,
		TeacherID:            req.TeacherId,
		SemesterID:           req.SemesterId,
		StartTime:            req.StartTime.AsTime(),
		EndTime:              req.EndTime.AsTime(),
		CancellationDeadline: req.CancellationDeadline.AsTime(),
		Location:             req.Location,
		Version:              int32(req.Version),
	}

	// Update the schedule
	err := s.services.Schedule.UpdateSchedule(ctx, schedule)
	if err != nil {
		if err == domain.ErrScheduleNotFound {
			return nil, status.Errorf(codes.NotFound, "schedule not found")
		}
		if errors.Is(err, domain.ErrScheduleConflict) {
			return nil, status.Errorf(codes.FailedPrecondition, "schedule conflicts with existing schedules: teacher or facility is already booked for this time")
		}
		return nil, status.Errorf(codes.Internal, "failed to update schedule: %v", err)
	}

	return s.convertScheduleToProto(ctx, schedule)
}

// DeleteSchedule deletes a schedule
func (s *scheduleServer) DeleteSchedule(ctx context.Context, req *sportpb.DeleteScheduleRequest) (*empty.Empty, error) {
	err := s.services.Schedule.DeleteSchedule(ctx, req.Id)
	if err != nil {
		if err == domain.ErrScheduleNotFound {
			return nil, status.Errorf(codes.NotFound, "schedule not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to delete schedule: %v", err)
	}

	return &emptypb.Empty{}, nil
}

// ListSchedules lists schedules with filtering
func (s *scheduleServer) ListSchedules(ctx context.Context, req *sportpb.ListSchedulesRequest) (*sportpb.ListSchedulesResponse, error) {
	filter := domain.ScheduleFilter{
		FacilityID: req.FacilityId,
		TeacherID:  req.TeacherId,
		SemesterID: req.SemesterId,
		Location:   req.Location,
		Page:       int(req.Page),
		PageSize:   int(req.PageSize),
	}

	if req.StartDate != nil {
		filter.StartDate = req.StartDate.AsTime()
	}

	if req.EndDate != nil {
		filter.EndDate = req.EndDate.AsTime()
	}

	schedules, total, err := s.services.Schedule.ListSchedules(ctx, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list schedules: %v", err)
	}

	response := &sportpb.ListSchedulesResponse{
		Total:    int32(total),
		Page:     int32(filter.Page),
		PageSize: int32(filter.PageSize),
	}

	for _, schedule := range schedules {
		scheduleProto, err := s.convertScheduleToProto(ctx, schedule)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to convert schedule: %v", err)
		}
		response.Schedules = append(response.Schedules, scheduleProto)
	}

	return response, nil
}

// CreateWeeklySchedules creates schedules from a weekly template
func (s *scheduleServer) CreateWeeklySchedules(ctx context.Context, req *sportpb.CreateWeeklySchedulesRequest) (*sportpb.CreateWeeklySchedulesResponse, error) {
	// Parse time strings
	startTime, err := time.Parse("15:04:05", req.StartTime)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid start time format: %v", err)
	}

	endTime, err := time.Parse("15:04:05", req.EndTime)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid end time format: %v", err)
	}

	template := &domain.WeeklyScheduleTemplate{
		FacilityID:  req.FacilityId,
		TeacherID:   req.TeacherId,
		SemesterID:  req.SemesterId,
		SportTypeID: req.SportTypeId,
		DayOfWeek:   int(req.DayOfWeek),
		StartTime:   startTime,
		EndTime:     endTime,
		Location:    req.Location,
		StartDate:   req.StartDate.AsTime(),
		EndDate:     req.EndDate.AsTime(),
	}

	// Create the schedules
	err = s.services.Schedule.CreateWeeklySchedules(ctx, template)
	if err != nil {
		if errors.Is(err, domain.ErrScheduleConflict) {
			return nil, status.Errorf(codes.FailedPrecondition, "schedule conflicts with existing schedules: teacher or facility is already booked for this time")
		}
		return nil, status.Errorf(codes.Internal, "failed to create weekly schedules: %v", err)
	}

	// TODO mock schedules data

	response := &sportpb.CreateWeeklySchedulesResponse{
		Count: 0,
	}

	//for _, schedule := range schedules {
	//	scheduleProto, err := s.convertScheduleToProto(ctx, schedule)
	//	if err != nil {
	//		return nil, status.Errorf(codes.Internal, "failed to convert schedule: %v", err)
	//	}
	//	response.Schedules = append(response.Schedules, scheduleProto)
	//}

	return response, nil
}

// GetSchedulesForTeacher retrieves schedules for a teacher
func (s *scheduleServer) GetSchedulesForTeacher(ctx context.Context, req *sportpb.GetSchedulesForTeacherRequest) (*sportpb.ListSchedulesResponse, error) {
	schedules, err := s.services.Schedule.GetSchedulesForTeacher(ctx, req.TeacherId, req.StartDate.AsTime(), req.EndDate.AsTime())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get schedules for teacher: %v", err)
	}

	response := &sportpb.ListSchedulesResponse{
		Total: int32(len(schedules)),
	}

	for _, schedule := range schedules {
		scheduleProto, err := s.convertScheduleToProto(ctx, schedule)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to convert schedule: %v", err)
		}
		response.Schedules = append(response.Schedules, scheduleProto)
	}

	return response, nil
}

// GetSchedulesForFacility retrieves schedules for a facility
func (s *scheduleServer) GetSchedulesForFacility(ctx context.Context, req *sportpb.GetSchedulesForFacilityRequest) (*sportpb.ListSchedulesResponse, error) {
	schedules, err := s.services.Schedule.GetSchedulesForFacility(ctx, req.FacilityId, req.StartDate.AsTime(), req.EndDate.AsTime())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get schedules for facility: %v", err)
	}

	response := &sportpb.ListSchedulesResponse{
		Total: int32(len(schedules)),
	}

	for _, schedule := range schedules {
		scheduleProto, err := s.convertScheduleToProto(ctx, schedule)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to convert schedule: %v", err)
		}
		response.Schedules = append(response.Schedules, scheduleProto)
	}

	return response, nil
}

// GetSchedulesForSemester retrieves schedules for a semester
func (s *scheduleServer) GetSchedulesForSemester(ctx context.Context, req *sportpb.GetSchedulesForSemesterRequest) (*sportpb.ListSchedulesResponse, error) {
	schedules, err := s.services.Schedule.GetSchedulesForSemester(ctx, req.SemesterId)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get schedules for semester: %v", err)
	}

	response := &sportpb.ListSchedulesResponse{
		Total: int32(len(schedules)),
	}

	for _, schedule := range schedules {
		scheduleProto, err := s.convertScheduleToProto(ctx, schedule)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to convert schedule: %v", err)
		}
		response.Schedules = append(response.Schedules, scheduleProto)
	}

	return response, nil
}

// Helper function to convert domain.Schedule to sportpb.ScheduleResponse
func (s *scheduleServer) convertScheduleToProto(ctx context.Context, schedule *domain.Schedule) (*sportpb.ScheduleResponse, error) {
	// Get the facility
	facility, err := s.services.PhysicalEducation.GetFacility(ctx, schedule.FacilityID)
	if err != nil {
		return nil, err
	}

	// Count available spots
	bookingCount, err := s.services.Booking.CountBySchedule(ctx, schedule.ID)
	if err != nil {
		return nil, err
	}

	availableSpots := facility.MaxCapacity - bookingCount
	if availableSpots < 0 {
		availableSpots = 0
	}

	return &sportpb.ScheduleResponse{
		Id:                   schedule.ID,
		FacilityId:           schedule.FacilityID,
		TeacherId:            schedule.TeacherID,
		SemesterId:           schedule.SemesterID,
		StartTime:            timestamppb.New(schedule.StartTime),
		EndTime:              timestamppb.New(schedule.EndTime),
		CancellationDeadline: timestamppb.New(schedule.CancellationDeadline),
		Location:             schedule.Location,
		CreatedAt:            timestamppb.New(schedule.CreatedAt),
		UpdatedAt:            timestamppb.New(schedule.UpdatedAt),
		Version:              schedule.Version,
		Facility: &sportpb.FacilityInfo{
			Id:          facility.ID,
			Title:       facility.Title,
			MaxCapacity: int32(facility.MaxCapacity),
		},
		AvailableSpots: int32(availableSpots),
		TotalSpots:     int32(facility.MaxCapacity),
	}, nil
}
