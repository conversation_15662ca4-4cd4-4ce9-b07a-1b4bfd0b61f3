<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thread Schedule API Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .response {
            margin-top: 15px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 4px;
            white-space: pre-wrap;
            min-height: 100px;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f1f1f1;
            border: 1px solid #ccc;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #fff;
            border-bottom: 1px solid white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>Thread Schedule API Tester</h1>
    
    <div class="tabs">
        <div class="tab active" onclick="openTab(event, 'create')">Create Schedule</div>
        <div class="tab" onclick="openTab(event, 'list')">List Schedules</div>
        <div class="tab" onclick="openTab(event, 'get')">Get Schedule</div>
        <div class="tab" onclick="openTab(event, 'update')">Update Schedule</div>
        <div class="tab" onclick="openTab(event, 'location')">Schedule Location</div>
        <div class="tab" onclick="openTab(event, 'delete')">Delete Schedule</div>
    </div>
    
    <div id="create" class="tab-content active section">
        <h2>Create Schedule</h2>
        <form id="createForm">
            <label for="createThreadId">Thread ID:</label>
            <input type="number" id="createThreadId" required>
            
            <label for="createDayOfWeek">Day of Week (1-7):</label>
            <select id="createDayOfWeek" required>
                <option value="1">Monday (1)</option>
                <option value="2">Tuesday (2)</option>
                <option value="3">Wednesday (3)</option>
                <option value="4">Thursday (4)</option>
                <option value="5">Friday (5)</option>
                <option value="6">Saturday (6)</option>
                <option value="7">Sunday (7)</option>
            </select>
            
            <label for="createStartTime">Start Time (HH:MM:SS):</label>
            <input type="time" id="createStartTime" step="1" required>
            
            <label for="createEndTime">End Time (HH:MM:SS):</label>
            <input type="time" id="createEndTime" step="1" required>
            
            <button type="button" onclick="createSchedule()">Create Schedule</button>
        </form>
        <div class="response" id="createResponse"></div>
    </div>
    
    <div id="list" class="tab-content section">
        <h2>List Schedules</h2>
        <form id="listForm">
            <label for="listThreadId">Thread ID:</label>
            <input type="number" id="listThreadId" required>
            
            <button type="button" onclick="listSchedules()">List Schedules</button>
        </form>
        <div class="response" id="listResponse"></div>
    </div>
    
    <div id="get" class="tab-content section">
        <h2>Get Schedule</h2>
        <form id="getForm">
            <label for="getScheduleId">Schedule ID:</label>
            <input type="number" id="getScheduleId" required>
            
            <button type="button" onclick="getSchedule()">Get Schedule</button>
        </form>
        <div class="response" id="getResponse"></div>
    </div>
    
    <div id="update" class="tab-content section">
        <h2>Update Schedule</h2>
        <form id="updateForm">
            <label for="updateScheduleId">Schedule ID:</label>
            <input type="number" id="updateScheduleId" required>
            
            <label for="updateDayOfWeek">Day of Week (1-7):</label>
            <select id="updateDayOfWeek" required>
                <option value="1">Monday (1)</option>
                <option value="2">Tuesday (2)</option>
                <option value="3">Wednesday (3)</option>
                <option value="4">Thursday (4)</option>
                <option value="5">Friday (5)</option>
                <option value="6">Saturday (6)</option>
                <option value="7">Sunday (7)</option>
            </select>
            
            <label for="updateStartTime">Start Time (HH:MM:SS):</label>
            <input type="time" id="updateStartTime" step="1" required>
            
            <label for="updateEndTime">End Time (HH:MM:SS):</label>
            <input type="time" id="updateEndTime" step="1" required>
            
            <button type="button" onclick="updateSchedule()">Update Schedule</button>
        </form>
        <div class="response" id="updateResponse"></div>
    </div>
    
    <div id="location" class="tab-content section">
        <h2>Schedule Location</h2>
        <form id="locationForm">
            <label for="locationScheduleId">Schedule ID:</label>
            <input type="number" id="locationScheduleId" required>
            
            <label for="location">Location:</label>
            <input type="text" id="location" placeholder="e.g., Room 101">
            
            <button type="button" onclick="getLocation()">Get Location</button>
            <button type="button" onclick="updateLocation()">Update Location</button>
        </form>
        <div class="response" id="locationResponse"></div>
    </div>
    
    <div id="delete" class="tab-content section">
        <h2>Delete Schedule</h2>
        <form id="deleteForm">
            <label for="deleteScheduleId">Schedule ID:</label>
            <input type="number" id="deleteScheduleId" required>
            
            <button type="button" onclick="deleteSchedule()">Delete Schedule</button>
        </form>
        <div class="response" id="deleteResponse"></div>
    </div>
    
    <script>
        // Base URL for API
        const baseUrl = 'http://localhost:8081';
        
        // Tab functionality
        function openTab(evt, tabName) {
            const tabs = document.getElementsByClassName("tab");
            for (let i = 0; i < tabs.length; i++) {
                tabs[i].className = tabs[i].className.replace(" active", "");
            }
            
            const tabContents = document.getElementsByClassName("tab-content");
            for (let i = 0; i < tabContents.length; i++) {
                tabContents[i].className = tabContents[i].className.replace(" active", "");
            }
            
            document.getElementById(tabName).className += " active";
            evt.currentTarget.className += " active";
        }
        
        // Format time input to HH:MM:SS
        function formatTime(timeInput) {
            if (!timeInput) return '';
            if (timeInput.includes(':')) {
                // Make sure it has seconds
                const parts = timeInput.split(':');
                if (parts.length === 2) {
                    return `${parts[0]}:${parts[1]}:00`;
                }
                return timeInput;
            }
            return '';
        }
        
        // API functions
        async function createSchedule() {
            const threadId = document.getElementById('createThreadId').value;
            const dayOfWeek = document.getElementById('createDayOfWeek').value;
            const startTime = formatTime(document.getElementById('createStartTime').value);
            const endTime = formatTime(document.getElementById('createEndTime').value);
            
            const responseElement = document.getElementById('createResponse');
            
            try {
                const response = await fetch(`${baseUrl}/thread/${threadId}/schedules`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        day_of_week: parseInt(dayOfWeek),
                        start_time: startTime,
                        end_time: endTime
                    })
                });
                
                const data = await response.json();
                responseElement.textContent = JSON.stringify(data, null, 2);
                
                // If successful, ask if they want to set a location
                if (response.ok && data.id) {
                    if (confirm('Schedule created successfully! Would you like to set a location for this schedule?')) {
                        document.getElementById('locationScheduleId').value = data.id;
                        openTab({ currentTarget: document.querySelector('.tab:nth-child(5)') }, 'location');
                    }
                }
            } catch (error) {
                responseElement.textContent = `Error: ${error.message}`;
            }
        }
        
        async function listSchedules() {
            const threadId = document.getElementById('listThreadId').value;
            const responseElement = document.getElementById('listResponse');
            
            try {
                const response = await fetch(`${baseUrl}/thread/${threadId}/schedules`);
                const data = await response.json();
                responseElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseElement.textContent = `Error: ${error.message}`;
            }
        }
        
        async function getSchedule() {
            const scheduleId = document.getElementById('getScheduleId').value;
            const responseElement = document.getElementById('getResponse');
            
            try {
                const response = await fetch(`${baseUrl}/thread/schedules/${scheduleId}`);
                const data = await response.json();
                responseElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseElement.textContent = `Error: ${error.message}`;
            }
        }
        
        async function updateSchedule() {
            const scheduleId = document.getElementById('updateScheduleId').value;
            const dayOfWeek = document.getElementById('updateDayOfWeek').value;
            const startTime = formatTime(document.getElementById('updateStartTime').value);
            const endTime = formatTime(document.getElementById('updateEndTime').value);
            
            const responseElement = document.getElementById('updateResponse');
            
            try {
                const response = await fetch(`${baseUrl}/thread/schedules/${scheduleId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        day_of_week: parseInt(dayOfWeek),
                        start_time: startTime,
                        end_time: endTime
                    })
                });
                
                const data = await response.json();
                responseElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseElement.textContent = `Error: ${error.message}`;
            }
        }
        
        async function getLocation() {
            const scheduleId = document.getElementById('locationScheduleId').value;
            const responseElement = document.getElementById('locationResponse');
            
            try {
                const response = await fetch(`${baseUrl}/thread/schedules/${scheduleId}/location`);
                const data = await response.json();
                responseElement.textContent = JSON.stringify(data, null, 2);
                
                // Update the location input if we got a location
                if (response.ok && data.location !== undefined) {
                    document.getElementById('location').value = data.location;
                }
            } catch (error) {
                responseElement.textContent = `Error: ${error.message}`;
            }
        }
        
        async function updateLocation() {
            const scheduleId = document.getElementById('locationScheduleId').value;
            const location = document.getElementById('location').value;
            const responseElement = document.getElementById('locationResponse');
            
            try {
                const response = await fetch(`${baseUrl}/thread/schedules/${scheduleId}/location`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        location: location
                    })
                });
                
                const data = await response.json();
                responseElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseElement.textContent = `Error: ${error.message}`;
            }
        }
        
        async function deleteSchedule() {
            const scheduleId = document.getElementById('deleteScheduleId').value;
            const responseElement = document.getElementById('deleteResponse');
            
            if (!confirm('Are you sure you want to delete this schedule?')) {
                return;
            }
            
            try {
                const response = await fetch(`${baseUrl}/thread/schedules/${scheduleId}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    responseElement.textContent = JSON.stringify(data, null, 2);
                } else {
                    responseElement.textContent = `Error: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                responseElement.textContent = `Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
