package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"google.golang.org/grpc"
	transcriptpb "github.com/olzzhas/edunite-server/course_service/pb/transcript"
)

func main() {
	// Connect to the course service
	conn, err := grpc.Dial("localhost:50053", grpc.WithInsecure())
	if err != nil {
		log.Fatalf("Failed to connect to course service: %v", err)
	}
	defer conn.Close()

	client := transcriptpb.NewTranscriptServiceClient(conn)

	// Test 1: List degrees (should work)
	fmt.Println("Testing ListDegrees...")
	degreesResp, err := client.ListDegrees(context.Background(), &transcriptpb.ListDegreesRequest{
		Page:     1,
		PageSize: 10,
	})
	if err != nil {
		log.Printf("ListDegrees failed: %v", err)
	} else {
		fmt.Printf("✅ ListDegrees successful: Found %d degrees\n", len(degreesResp.Degrees))
		for _, degree := range degreesResp.Degrees {
			fmt.Printf("  - %s (%s)\n", degree.Name, degree.Level)
		}
	}

	// Test 2: Try to get a transcript for user ID 4 (this was failing before)
	fmt.Println("\nTesting GetTranscript for user ID 4...")
	transcriptResp, err := client.GetTranscript(context.Background(), &transcriptpb.GetTranscriptRequest{
		UserId: 4,
	})
	if err != nil {
		// Check if it's the expected "not found" error or the OID scanning error
		if err.Error() == "rpc error: code = NotFound desc = transcript not found" {
			fmt.Println("✅ GetTranscript returned expected 'not found' error (no OID scanning error)")
		} else {
			fmt.Printf("❌ GetTranscript failed with unexpected error: %v\n", err)
		}
	} else {
		fmt.Printf("✅ GetTranscript successful: Found transcript for user %d\n", transcriptResp.Transcript.UserId)
	}

	// Test 3: Create a test transcript to verify the fix works end-to-end
	fmt.Println("\nTesting CreateTranscript...")
	createResp, err := client.CreateTranscript(context.Background(), &transcriptpb.CreateTranscriptRequest{
		UserId:   999, // Use a test user ID
		DegreeId: 1,   // Use the first degree from the migration
	})
	if err != nil {
		fmt.Printf("CreateTranscript failed (expected if user doesn't exist): %v\n", err)
	} else {
		fmt.Printf("✅ CreateTranscript successful: Created transcript ID %d\n", createResp.Transcript.Id)
		
		// Now try to get this transcript to verify scanning works
		fmt.Println("Testing GetTranscript for newly created transcript...")
		getResp, err := client.GetTranscript(context.Background(), &transcriptpb.GetTranscriptRequest{
			UserId:   999,
			DegreeId: 1,
		})
		if err != nil {
			fmt.Printf("❌ GetTranscript failed for newly created transcript: %v\n", err)
		} else {
			fmt.Printf("✅ GetTranscript successful for newly created transcript!\n")
			fmt.Printf("  - Transcript ID: %d\n", getResp.Transcript.Id)
			fmt.Printf("  - User ID: %d\n", getResp.Transcript.UserId)
			fmt.Printf("  - Degree: %s\n", getResp.Transcript.Degree.Name)
		}
	}

	fmt.Println("\n🎉 Database scanning fix test completed!")
}
