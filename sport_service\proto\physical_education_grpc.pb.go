// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: proto/physical_education.proto

package sportpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PhysicalEducationService_GetAvailableSportTypes_FullMethodName   = "/sportpb.PhysicalEducationService/GetAvailableSportTypes"
	PhysicalEducationService_GetAvailableFacilities_FullMethodName   = "/sportpb.PhysicalEducationService/GetAvailableFacilities"
	PhysicalEducationService_GetAvailableSchedules_FullMethodName    = "/sportpb.PhysicalEducationService/GetAvailableSchedules"
	PhysicalEducationService_BookSession_FullMethodName              = "/sportpb.PhysicalEducationService/BookSession"
	PhysicalEducationService_CancelBooking_FullMethodName            = "/sportpb.PhysicalEducationService/CancelBooking"
	PhysicalEducationService_UploadMedicalCertificate_FullMethodName = "/sportpb.PhysicalEducationService/UploadMedicalCertificate"
	PhysicalEducationService_GetUserBookings_FullMethodName          = "/sportpb.PhysicalEducationService/GetUserBookings"
	PhysicalEducationService_GetUserSemesterStats_FullMethodName     = "/sportpb.PhysicalEducationService/GetUserSemesterStats"
	PhysicalEducationService_CheckUserCanBookLFK_FullMethodName      = "/sportpb.PhysicalEducationService/CheckUserCanBookLFK"
	PhysicalEducationService_GetSemesterSportLimit_FullMethodName    = "/sportpb.PhysicalEducationService/GetSemesterSportLimit"
	PhysicalEducationService_GetDailyBookingLimit_FullMethodName     = "/sportpb.PhysicalEducationService/GetDailyBookingLimit"
)

// PhysicalEducationServiceClient is the client API for PhysicalEducationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PhysicalEducationServiceClient interface {
	// Get available sport types
	GetAvailableSportTypes(ctx context.Context, in *GetAvailableSportTypesRequest, opts ...grpc.CallOption) (*ListSportTypesResponse, error)
	// Get available facilities
	GetAvailableFacilities(ctx context.Context, in *GetAvailableFacilitiesRequest, opts ...grpc.CallOption) (*ListFacilitiesResponse, error)
	// Get available schedules
	GetAvailableSchedules(ctx context.Context, in *GetAvailableSchedulesRequest, opts ...grpc.CallOption) (*ListSchedulesResponse, error)
	// Book a session
	BookSession(ctx context.Context, in *BookSessionRequest, opts ...grpc.CallOption) (*BookingResponse, error)
	// Cancel a booking
	CancelBooking(ctx context.Context, in *CancelBookingRequest, opts ...grpc.CallOption) (*BookingResponse, error)
	// Upload a medical certificate
	UploadMedicalCertificate(ctx context.Context, in *UploadCertificateRequest, opts ...grpc.CallOption) (*MedicalCertificateResponse, error)
	// Get user bookings
	GetUserBookings(ctx context.Context, in *GetUserBookingsRequest, opts ...grpc.CallOption) (*ListBookingsResponse, error)
	// Get user semester statistics
	GetUserSemesterStats(ctx context.Context, in *GetUserSemesterStatsRequest, opts ...grpc.CallOption) (*UserSemesterStatsResponse, error)
	// Check if a user can book LFK
	CheckUserCanBookLFK(ctx context.Context, in *CheckUserCanBookLFKRequest, opts ...grpc.CallOption) (*CheckUserCanBookLFKResponse, error)
	// Get semester sport limit
	GetSemesterSportLimit(ctx context.Context, in *GetSemesterLimitBySemesterRequest, opts ...grpc.CallOption) (*SemesterLimitResponse, error)
	// Get daily booking limit
	GetDailyBookingLimit(ctx context.Context, in *GetDailyBookingLimitBySemesterRequest, opts ...grpc.CallOption) (*DailyBookingLimitResponse, error)
}

type physicalEducationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPhysicalEducationServiceClient(cc grpc.ClientConnInterface) PhysicalEducationServiceClient {
	return &physicalEducationServiceClient{cc}
}

func (c *physicalEducationServiceClient) GetAvailableSportTypes(ctx context.Context, in *GetAvailableSportTypesRequest, opts ...grpc.CallOption) (*ListSportTypesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSportTypesResponse)
	err := c.cc.Invoke(ctx, PhysicalEducationService_GetAvailableSportTypes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *physicalEducationServiceClient) GetAvailableFacilities(ctx context.Context, in *GetAvailableFacilitiesRequest, opts ...grpc.CallOption) (*ListFacilitiesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListFacilitiesResponse)
	err := c.cc.Invoke(ctx, PhysicalEducationService_GetAvailableFacilities_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *physicalEducationServiceClient) GetAvailableSchedules(ctx context.Context, in *GetAvailableSchedulesRequest, opts ...grpc.CallOption) (*ListSchedulesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSchedulesResponse)
	err := c.cc.Invoke(ctx, PhysicalEducationService_GetAvailableSchedules_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *physicalEducationServiceClient) BookSession(ctx context.Context, in *BookSessionRequest, opts ...grpc.CallOption) (*BookingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BookingResponse)
	err := c.cc.Invoke(ctx, PhysicalEducationService_BookSession_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *physicalEducationServiceClient) CancelBooking(ctx context.Context, in *CancelBookingRequest, opts ...grpc.CallOption) (*BookingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BookingResponse)
	err := c.cc.Invoke(ctx, PhysicalEducationService_CancelBooking_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *physicalEducationServiceClient) UploadMedicalCertificate(ctx context.Context, in *UploadCertificateRequest, opts ...grpc.CallOption) (*MedicalCertificateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MedicalCertificateResponse)
	err := c.cc.Invoke(ctx, PhysicalEducationService_UploadMedicalCertificate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *physicalEducationServiceClient) GetUserBookings(ctx context.Context, in *GetUserBookingsRequest, opts ...grpc.CallOption) (*ListBookingsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBookingsResponse)
	err := c.cc.Invoke(ctx, PhysicalEducationService_GetUserBookings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *physicalEducationServiceClient) GetUserSemesterStats(ctx context.Context, in *GetUserSemesterStatsRequest, opts ...grpc.CallOption) (*UserSemesterStatsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserSemesterStatsResponse)
	err := c.cc.Invoke(ctx, PhysicalEducationService_GetUserSemesterStats_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *physicalEducationServiceClient) CheckUserCanBookLFK(ctx context.Context, in *CheckUserCanBookLFKRequest, opts ...grpc.CallOption) (*CheckUserCanBookLFKResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckUserCanBookLFKResponse)
	err := c.cc.Invoke(ctx, PhysicalEducationService_CheckUserCanBookLFK_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *physicalEducationServiceClient) GetSemesterSportLimit(ctx context.Context, in *GetSemesterLimitBySemesterRequest, opts ...grpc.CallOption) (*SemesterLimitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SemesterLimitResponse)
	err := c.cc.Invoke(ctx, PhysicalEducationService_GetSemesterSportLimit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *physicalEducationServiceClient) GetDailyBookingLimit(ctx context.Context, in *GetDailyBookingLimitBySemesterRequest, opts ...grpc.CallOption) (*DailyBookingLimitResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DailyBookingLimitResponse)
	err := c.cc.Invoke(ctx, PhysicalEducationService_GetDailyBookingLimit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PhysicalEducationServiceServer is the server API for PhysicalEducationService service.
// All implementations must embed UnimplementedPhysicalEducationServiceServer
// for forward compatibility.
type PhysicalEducationServiceServer interface {
	// Get available sport types
	GetAvailableSportTypes(context.Context, *GetAvailableSportTypesRequest) (*ListSportTypesResponse, error)
	// Get available facilities
	GetAvailableFacilities(context.Context, *GetAvailableFacilitiesRequest) (*ListFacilitiesResponse, error)
	// Get available schedules
	GetAvailableSchedules(context.Context, *GetAvailableSchedulesRequest) (*ListSchedulesResponse, error)
	// Book a session
	BookSession(context.Context, *BookSessionRequest) (*BookingResponse, error)
	// Cancel a booking
	CancelBooking(context.Context, *CancelBookingRequest) (*BookingResponse, error)
	// Upload a medical certificate
	UploadMedicalCertificate(context.Context, *UploadCertificateRequest) (*MedicalCertificateResponse, error)
	// Get user bookings
	GetUserBookings(context.Context, *GetUserBookingsRequest) (*ListBookingsResponse, error)
	// Get user semester statistics
	GetUserSemesterStats(context.Context, *GetUserSemesterStatsRequest) (*UserSemesterStatsResponse, error)
	// Check if a user can book LFK
	CheckUserCanBookLFK(context.Context, *CheckUserCanBookLFKRequest) (*CheckUserCanBookLFKResponse, error)
	// Get semester sport limit
	GetSemesterSportLimit(context.Context, *GetSemesterLimitBySemesterRequest) (*SemesterLimitResponse, error)
	// Get daily booking limit
	GetDailyBookingLimit(context.Context, *GetDailyBookingLimitBySemesterRequest) (*DailyBookingLimitResponse, error)
	mustEmbedUnimplementedPhysicalEducationServiceServer()
}

// UnimplementedPhysicalEducationServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPhysicalEducationServiceServer struct{}

func (UnimplementedPhysicalEducationServiceServer) GetAvailableSportTypes(context.Context, *GetAvailableSportTypesRequest) (*ListSportTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvailableSportTypes not implemented")
}
func (UnimplementedPhysicalEducationServiceServer) GetAvailableFacilities(context.Context, *GetAvailableFacilitiesRequest) (*ListFacilitiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvailableFacilities not implemented")
}
func (UnimplementedPhysicalEducationServiceServer) GetAvailableSchedules(context.Context, *GetAvailableSchedulesRequest) (*ListSchedulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvailableSchedules not implemented")
}
func (UnimplementedPhysicalEducationServiceServer) BookSession(context.Context, *BookSessionRequest) (*BookingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BookSession not implemented")
}
func (UnimplementedPhysicalEducationServiceServer) CancelBooking(context.Context, *CancelBookingRequest) (*BookingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelBooking not implemented")
}
func (UnimplementedPhysicalEducationServiceServer) UploadMedicalCertificate(context.Context, *UploadCertificateRequest) (*MedicalCertificateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadMedicalCertificate not implemented")
}
func (UnimplementedPhysicalEducationServiceServer) GetUserBookings(context.Context, *GetUserBookingsRequest) (*ListBookingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserBookings not implemented")
}
func (UnimplementedPhysicalEducationServiceServer) GetUserSemesterStats(context.Context, *GetUserSemesterStatsRequest) (*UserSemesterStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserSemesterStats not implemented")
}
func (UnimplementedPhysicalEducationServiceServer) CheckUserCanBookLFK(context.Context, *CheckUserCanBookLFKRequest) (*CheckUserCanBookLFKResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserCanBookLFK not implemented")
}
func (UnimplementedPhysicalEducationServiceServer) GetSemesterSportLimit(context.Context, *GetSemesterLimitBySemesterRequest) (*SemesterLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSemesterSportLimit not implemented")
}
func (UnimplementedPhysicalEducationServiceServer) GetDailyBookingLimit(context.Context, *GetDailyBookingLimitBySemesterRequest) (*DailyBookingLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDailyBookingLimit not implemented")
}
func (UnimplementedPhysicalEducationServiceServer) mustEmbedUnimplementedPhysicalEducationServiceServer() {
}
func (UnimplementedPhysicalEducationServiceServer) testEmbeddedByValue() {}

// UnsafePhysicalEducationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PhysicalEducationServiceServer will
// result in compilation errors.
type UnsafePhysicalEducationServiceServer interface {
	mustEmbedUnimplementedPhysicalEducationServiceServer()
}

func RegisterPhysicalEducationServiceServer(s grpc.ServiceRegistrar, srv PhysicalEducationServiceServer) {
	// If the following call pancis, it indicates UnimplementedPhysicalEducationServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PhysicalEducationService_ServiceDesc, srv)
}

func _PhysicalEducationService_GetAvailableSportTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvailableSportTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PhysicalEducationServiceServer).GetAvailableSportTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PhysicalEducationService_GetAvailableSportTypes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PhysicalEducationServiceServer).GetAvailableSportTypes(ctx, req.(*GetAvailableSportTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PhysicalEducationService_GetAvailableFacilities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvailableFacilitiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PhysicalEducationServiceServer).GetAvailableFacilities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PhysicalEducationService_GetAvailableFacilities_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PhysicalEducationServiceServer).GetAvailableFacilities(ctx, req.(*GetAvailableFacilitiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PhysicalEducationService_GetAvailableSchedules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvailableSchedulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PhysicalEducationServiceServer).GetAvailableSchedules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PhysicalEducationService_GetAvailableSchedules_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PhysicalEducationServiceServer).GetAvailableSchedules(ctx, req.(*GetAvailableSchedulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PhysicalEducationService_BookSession_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BookSessionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PhysicalEducationServiceServer).BookSession(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PhysicalEducationService_BookSession_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PhysicalEducationServiceServer).BookSession(ctx, req.(*BookSessionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PhysicalEducationService_CancelBooking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PhysicalEducationServiceServer).CancelBooking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PhysicalEducationService_CancelBooking_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PhysicalEducationServiceServer).CancelBooking(ctx, req.(*CancelBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PhysicalEducationService_UploadMedicalCertificate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadCertificateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PhysicalEducationServiceServer).UploadMedicalCertificate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PhysicalEducationService_UploadMedicalCertificate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PhysicalEducationServiceServer).UploadMedicalCertificate(ctx, req.(*UploadCertificateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PhysicalEducationService_GetUserBookings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBookingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PhysicalEducationServiceServer).GetUserBookings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PhysicalEducationService_GetUserBookings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PhysicalEducationServiceServer).GetUserBookings(ctx, req.(*GetUserBookingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PhysicalEducationService_GetUserSemesterStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSemesterStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PhysicalEducationServiceServer).GetUserSemesterStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PhysicalEducationService_GetUserSemesterStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PhysicalEducationServiceServer).GetUserSemesterStats(ctx, req.(*GetUserSemesterStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PhysicalEducationService_CheckUserCanBookLFK_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserCanBookLFKRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PhysicalEducationServiceServer).CheckUserCanBookLFK(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PhysicalEducationService_CheckUserCanBookLFK_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PhysicalEducationServiceServer).CheckUserCanBookLFK(ctx, req.(*CheckUserCanBookLFKRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PhysicalEducationService_GetSemesterSportLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSemesterLimitBySemesterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PhysicalEducationServiceServer).GetSemesterSportLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PhysicalEducationService_GetSemesterSportLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PhysicalEducationServiceServer).GetSemesterSportLimit(ctx, req.(*GetSemesterLimitBySemesterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PhysicalEducationService_GetDailyBookingLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDailyBookingLimitBySemesterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PhysicalEducationServiceServer).GetDailyBookingLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PhysicalEducationService_GetDailyBookingLimit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PhysicalEducationServiceServer).GetDailyBookingLimit(ctx, req.(*GetDailyBookingLimitBySemesterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PhysicalEducationService_ServiceDesc is the grpc.ServiceDesc for PhysicalEducationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PhysicalEducationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sportpb.PhysicalEducationService",
	HandlerType: (*PhysicalEducationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAvailableSportTypes",
			Handler:    _PhysicalEducationService_GetAvailableSportTypes_Handler,
		},
		{
			MethodName: "GetAvailableFacilities",
			Handler:    _PhysicalEducationService_GetAvailableFacilities_Handler,
		},
		{
			MethodName: "GetAvailableSchedules",
			Handler:    _PhysicalEducationService_GetAvailableSchedules_Handler,
		},
		{
			MethodName: "BookSession",
			Handler:    _PhysicalEducationService_BookSession_Handler,
		},
		{
			MethodName: "CancelBooking",
			Handler:    _PhysicalEducationService_CancelBooking_Handler,
		},
		{
			MethodName: "UploadMedicalCertificate",
			Handler:    _PhysicalEducationService_UploadMedicalCertificate_Handler,
		},
		{
			MethodName: "GetUserBookings",
			Handler:    _PhysicalEducationService_GetUserBookings_Handler,
		},
		{
			MethodName: "GetUserSemesterStats",
			Handler:    _PhysicalEducationService_GetUserSemesterStats_Handler,
		},
		{
			MethodName: "CheckUserCanBookLFK",
			Handler:    _PhysicalEducationService_CheckUserCanBookLFK_Handler,
		},
		{
			MethodName: "GetSemesterSportLimit",
			Handler:    _PhysicalEducationService_GetSemesterSportLimit_Handler,
		},
		{
			MethodName: "GetDailyBookingLimit",
			Handler:    _PhysicalEducationService_GetDailyBookingLimit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/physical_education.proto",
}
