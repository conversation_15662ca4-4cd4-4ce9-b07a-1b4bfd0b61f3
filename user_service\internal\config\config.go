package config

import "os"

type Config struct {
	DatabaseURL  string
	KeycloakURL  string
	ClientID     string
	ClientSecret string

	Database Database
}

type Database struct {
	CA       string
	Host     string
	Port     string
	User     string
	Password string
	Name     string
}

func LoadConfig() *Config {

	database := Database{
		CA:       os.<PERSON>env("DATABASE_CA"),
		Host:     os.<PERSON>("DATABASE_HOST"),
		Port:     os.<PERSON>v("DATABASE_PORT"),
		User:     os.<PERSON><PERSON>("DATABASE_USER"),
		Password: os.<PERSON>env("DATABASE_PASSWORD"),
		Name:     os.<PERSON>env("DATABASE_NAME"),
	}

	return &Config{
		DatabaseURL:  os.Getenv("DATABASE_URL"),
		KeycloakURL:  os.<PERSON>env("KEYCLOAK_URL"),
		ClientID:     "user-service",
		ClientSecret: "your-client-secret",
		Database:     database,
	}
}
