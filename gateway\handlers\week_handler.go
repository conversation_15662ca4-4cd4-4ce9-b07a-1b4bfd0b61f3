package handlers

import (
	"fmt"
	"github.com/gin-gonic/gin"
	threadpb "github.com/olzzhas/edunite-server/course_service/pb/thread"
	"net/http"
	"strconv"
)

// CreateWeekHandler создаёт новую неделю в потоке
func (h *ThreadHandler) CreateWeekHandler(c *gin.Context) {
	var req threadpb.WeekRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing CreateWeek request: %v", err),
			"week",
			map[string]any{"error": err.Error()},
		)
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// Вызываем сервис
	res, err := h.ThreadService.CreateWeek(c.Request.Context(), &req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error creating week: %v", err),
			"week",
			map[string]any{"thread_id": fmt.Sprintf("%d", req.GetThreadId()), "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create week"})
		return
	}

	h.RabbitLogPublisher.PublishLog(
		"info",
		fmt.Sprintf("Week created successfully: %d", res.Id),
		"week",
		map[string]any{"week_id": fmt.Sprintf("%d", res.Id), "thread_id": fmt.Sprintf("%d", res.ThreadId)},
	)

	c.JSON(http.StatusCreated, res)
}

// UpdateWeekHandler обновляет существующую неделю по ID
func (h *ThreadHandler) UpdateWeekHandler(c *gin.Context) {
	// Extract week_id from URL path parameter
	idStr := c.Param("id")
	weekID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid week id"})
		return
	}

	// Parse request body
	var req threadpb.WeekUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing UpdateWeek request: %v", err),
			"week",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// Set the week_id from the URL path parameter
	req.WeekId = weekID

	// Call the service
	res, err := h.ThreadService.UpdateWeek(c.Request.Context(), &req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error updating week: %v", err),
			"week",
			map[string]any{"week_id": idStr, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update week"})
		return
	}

	h.RabbitLogPublisher.PublishLog(
		"info",
		fmt.Sprintf("Week updated successfully: %d", res.Id),
		"week",
		map[string]any{"week_id": idStr},
	)

	c.JSON(http.StatusOK, res)
}

// DeleteWeekHandler удаляет неделю по ID из URL-параметра :id
func (h *ThreadHandler) DeleteWeekHandler(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid week id"})
		return
	}

	_, err = h.ThreadService.DeleteWeek(c.Request.Context(), &threadpb.WeekByID{WeekId: id})
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error deleting week: %v", err),
			"week",
			map[string]any{"week_id": idStr, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete week"})
		return
	}

	h.RabbitLogPublisher.PublishLog(
		"info",
		fmt.Sprintf("Week deleted successfully: %d", id),
		"week",
		map[string]any{"week_id": idStr},
	)

	c.Status(http.StatusNoContent)
}

// GetWeekHandler возвращает неделю по ID из URL-параметра :id
func (h *ThreadHandler) GetWeekHandler(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid week id"})
		return
	}

	res, err := h.ThreadService.GetWeekByID(c.Request.Context(), &threadpb.WeekByID{WeekId: id})
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error fetching week: %v", err),
			"week",
			map[string]any{"week_id": idStr, "error": err.Error()},
		)
		c.JSON(http.StatusNotFound, gin.H{"error": "week not found"})
		return
	}

	c.JSON(http.StatusOK, res)
}

// ListWeeksByThreadHandler возвращает все недели потока по query-параметру thread_id
func (h *ThreadHandler) ListWeeksByThreadHandler(c *gin.Context) {
	threadIDStr := c.Query("thread_id")
	if threadIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "thread_id query parameter is required"})
		return
	}
	threadID, err := strconv.ParseInt(threadIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid thread_id: %v", err)})
		return
	}

	res, err := h.ThreadService.ListWeeksByThread(c.Request.Context(), &threadpb.WeeksForThreadRequest{ThreadId: threadID})
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error listing weeks: %v", err),
			"week",
			map[string]any{"thread_id": threadIDStr, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list weeks"})
		return
	}

	c.JSON(http.StatusOK, res.Weeks)
}
