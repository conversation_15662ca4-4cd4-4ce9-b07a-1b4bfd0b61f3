FROM golang:1.23-alpine

RUN apk add --no-cache protobuf-dev

# Install protoc plugins
RUN go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
RUN go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

WORKDIR /app

COPY . .

# Generate gRPC code
RUN protoc --go_out=. --go_opt=paths=source_relative --go-grpc_out=. --go-grpc_opt=paths=source_relative loggerpb/logger.proto

# The generated files will be in the /app/loggerpb directory
