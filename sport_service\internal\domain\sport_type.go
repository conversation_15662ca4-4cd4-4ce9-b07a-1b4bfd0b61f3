package domain

import (
	"time"
)

// SportTypeCategory represents the category of a sport type
type SportTypeCategory string

const (
	SportTypeCategoryNormal SportTypeCategory = "normal"
	SportTypeCategoryLFK    SportTypeCategory = "lfk"
)

var (
	ErrSportTypeNotFound = Error{"sport type not found"}
)

// SportType represents a type of sport activity
type SportType struct {
	ID                  int64             `json:"id"`
	Title               string            `json:"title"`
	Description         string            `json:"description"`
	Category            SportTypeCategory `json:"category"`
	RequiresCertificate bool              `json:"requires_certificate"`
	CreatedAt           time.Time         `json:"created_at"`
	UpdatedAt           time.Time         `json:"updated_at"`
	Version             int32             `json:"version"`
}

// TeacherSportType represents a relationship between a teacher and a sport type
type TeacherSportType struct {
	TeacherID             int64     `json:"teacher_id"`
	SportTypeID           int64     `json:"sport_type_id"`
	CanReviewCertificates bool      `json:"can_review_certificates"`
	CreatedAt             time.Time `json:"created_at"`
	UpdatedAt             time.Time `json:"updated_at"`
}

// SportTypeFilter represents filters for querying sport types
type SportTypeFilter struct {
	Title               string
	Category            SportTypeCategory
	RequiresCertificate *bool
	Page                int
	PageSize            int
}

// TeacherSportTypeFilter represents filters for querying teacher-sport type relationships
type TeacherSportTypeFilter struct {
	TeacherID             int64
	SportTypeID           int64
	CanReviewCertificates *bool
	Page                  int
	PageSize              int
}
