package database

import (
	"context"
	"errors"
	"time"

	"github.com/jackc/pgx/v4"

	"github.com/jackc/pgx/v4/pgxpool"
)

var (
	ErrScheduleNotFound    = errors.New("thread schedule not found")
	ErrScheduleOverlap     = errors.New("time interval overlaps an existing schedule")
	ErrLocationOverlap     = errors.New("location is already booked for this time interval")
	ErrLocationNotProvided = errors.New("location must be provided for schedule")
)

// ThreadSchedule модель таблицы thread_schedules
type ThreadSchedule struct {
	ID         int64     `json:"id"`
	ThreadID   int64     `json:"thread_id"`
	DayOfWeek  int       `json:"day_of_week"` // 1=Mon…7=Sun
	StartTime  time.Time `json:"start_time"`
	EndTime    time.Time `json:"end_time"`
	Location   string    `json:"location"`    // Derived from locations table
	LocationID int64     `json:"location_id"` // Reference to locations table
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// ThreadScheduleRepository интерфейс CRUD для thread_schedules
type ThreadScheduleRepository interface {
	Create(ctx context.Context, s *ThreadSchedule) error
	ListByThread(ctx context.Context, threadID int64) ([]*ThreadSchedule, error)
	GetByID(ctx context.Context, id int64) (*ThreadSchedule, error)
	Update(ctx context.Context, s *ThreadSchedule) error
	Delete(ctx context.Context, id int64) error
	OverlapExists(ctx context.Context, threadID int64, day int,
		start, end time.Time, excludeID int64) (bool, error)
	// Check if a location is already booked for a specific time slot
	LocationOverlapExists(ctx context.Context, locationID int64, day int,
		start, end time.Time, excludeID int64) (bool, error)
}

// scheduleRepository реализация ThreadScheduleRepository
type scheduleRepository struct {
	db *pgxpool.Pool
}

// NewThreadScheduleRepository конструктор
func NewThreadScheduleRepository(db *pgxpool.Pool) ThreadScheduleRepository {
	return &scheduleRepository{db: db}
}

func timeOnly(t time.Time) time.Time {
	return time.Date(0, 1, 1, t.Hour(), t.Minute(), t.Second(), t.Nanosecond(), time.UTC)
}

func (r *scheduleRepository) Create(ctx context.Context, s *ThreadSchedule) error {
	// We don't need to insert the location field anymore since it's derived from location_id
	const q = `
	INSERT INTO thread_schedules (thread_id, day_of_week, start_time, end_time, location_id)
	VALUES ($1,$2,$3,$4,$5)
	RETURNING id, created_at, updated_at`

	row := r.db.QueryRow(ctx, q,
		s.ThreadID, s.DayOfWeek, timeOnly(s.StartTime), timeOnly(s.EndTime), s.LocationID)

	if err := row.Scan(&s.ID, &s.CreatedAt, &s.UpdatedAt); err != nil {
		return err
	}

	// Update the Location field with the name from the locations table
	if s.LocationID > 0 {
		const locQuery = `SELECT name FROM locations WHERE id = $1`
		var name string
		if err := r.db.QueryRow(ctx, locQuery, s.LocationID).Scan(&name); err == nil {
			s.Location = name
		}
	}

	return nil
}

func (r *scheduleRepository) ListByThread(ctx context.Context, threadID int64) ([]*ThreadSchedule, error) {
	// Always use the location name from the locations table
	// Use COALESCE to handle NULL location_id values
	const q = `
	SELECT ts.id, ts.thread_id, ts.day_of_week, ts.start_time, ts.end_time,
	       COALESCE(l.name, '') as location,
	       COALESCE(ts.location_id, 0) as location_id, ts.created_at, ts.updated_at
	FROM thread_schedules ts
	LEFT JOIN locations l ON ts.location_id = l.id
	WHERE ts.thread_id=$1
	ORDER BY ts.day_of_week, ts.start_time`

	rows, err := r.db.Query(ctx, q, threadID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var list []*ThreadSchedule
	for rows.Next() {
		var s ThreadSchedule
		if err := rows.Scan(&s.ID, &s.ThreadID, &s.DayOfWeek,
			&s.StartTime, &s.EndTime, &s.Location, &s.LocationID, &s.CreatedAt, &s.UpdatedAt); err != nil {
			return nil, err
		}
		list = append(list, &s)
	}
	return list, rows.Err()
}

func (r *scheduleRepository) GetByID(ctx context.Context, id int64) (*ThreadSchedule, error) {
	// Always use the location name from the locations table
	// Use COALESCE to handle NULL location_id values
	const q = `
	SELECT ts.id, ts.thread_id, ts.day_of_week, ts.start_time, ts.end_time,
	       COALESCE(l.name, '') as location,
	       COALESCE(ts.location_id, 0) as location_id, ts.created_at, ts.updated_at
	FROM thread_schedules ts
	LEFT JOIN locations l ON ts.location_id = l.id
	WHERE ts.id=$1`

	var s ThreadSchedule
	if err := r.db.QueryRow(ctx, q, id).Scan(&s.ID, &s.ThreadID, &s.DayOfWeek,
		&s.StartTime, &s.EndTime, &s.Location, &s.LocationID, &s.CreatedAt, &s.UpdatedAt); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, ErrScheduleNotFound
		}
		return nil, err
	}
	return &s, nil
}

func (r *scheduleRepository) Update(ctx context.Context, s *ThreadSchedule) error {
	// We don't need to update the location field anymore since it's derived from location_id
	const q = `
	UPDATE thread_schedules
	   SET day_of_week=$1,
	       start_time=$2,
	       end_time=$3,
	       location_id=$4,
	       updated_at=NOW()
	 WHERE id=$5
	 RETURNING updated_at`

	// After update, get the location name from the locations table
	if err := r.db.QueryRow(ctx, q,
		s.DayOfWeek, timeOnly(s.StartTime), timeOnly(s.EndTime), s.LocationID, s.ID).
		Scan(&s.UpdatedAt); err != nil {
		return err
	}

	// Update the Location field with the name from the locations table
	if s.LocationID > 0 {
		const locQuery = `SELECT name FROM locations WHERE id = $1`
		var name string
		if err := r.db.QueryRow(ctx, locQuery, s.LocationID).Scan(&name); err == nil {
			s.Location = name
		}
	}

	return nil
}

func (r *scheduleRepository) Delete(ctx context.Context, id int64) error {
	cmd, err := r.db.Exec(ctx, `DELETE FROM thread_schedules WHERE id=$1`, id)
	if err != nil {
		return err
	}
	if cmd.RowsAffected() == 0 {
		return ErrScheduleNotFound
	}
	return nil
}

// OverlapExists — есть ли пересечение «start < existing_end && end > existing_start»
// excludeID — ид записи, которую нужно игнорировать (0 → не игнорировать).
func (r *scheduleRepository) OverlapExists(ctx context.Context, threadID int64, day int,
	start, end time.Time, excludeID int64) (bool, error) {

	const q = `
	SELECT 1
	  FROM thread_schedules
	 WHERE thread_id=$1
	   AND day_of_week=$2
	   AND start_time < $4            -- newEnd
	   AND end_time   > $3            -- newStart
	   AND ($5 = 0 OR id <> $5)
	 LIMIT 1`
	var dummy int
	err := r.db.QueryRow(ctx, q,
		threadID, day, timeOnly(start), timeOnly(end), excludeID).Scan(&dummy)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// LocationOverlapExists checks if a location is already booked for a specific time slot
// excludeID — ид записи, которую нужно игнорировать (0 → не игнорировать).
func (r *scheduleRepository) LocationOverlapExists(ctx context.Context, locationID int64, day int,
	start, end time.Time, excludeID int64) (bool, error) {

	// If no location is provided, there can't be a conflict
	if locationID == 0 {
		return false, nil
	}

	const q = `
	SELECT 1
	  FROM thread_schedules
	 WHERE location_id=$1
	   AND day_of_week=$2
	   AND start_time < $4            -- newEnd
	   AND end_time   > $3            -- newStart
	   AND ($5 = 0 OR id <> $5)
	 LIMIT 1`
	var dummy int
	err := r.db.QueryRow(ctx, q,
		locationID, day, timeOnly(start), timeOnly(end), excludeID).Scan(&dummy)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}
