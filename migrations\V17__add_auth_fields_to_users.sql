-- Add username and password_hash columns to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS username VARCHAR(100) UNIQUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255);

-- Update existing users to have a username based on their email (temporary solution)
UPDATE users SET username = SUBSTRING(email FROM 1 FOR POSITION('@' IN email) - 1) WHERE username IS NULL;

-- Make username NOT NULL after setting values for existing users
ALTER TABLE users ALTER COLUMN username SET NOT NULL;
