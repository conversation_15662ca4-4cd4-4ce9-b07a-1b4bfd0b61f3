syntax = "proto3";

package sportpb;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/olzzhas/edunite-server/sport_service/pb/sportpb";

service BookingService {
  // Create a new booking
  rpc CreateBooking(CreateBookingRequest) returns (BookingResponse) {}

  // Get a booking by ID
  rpc GetBooking(GetBookingRequest) returns (BookingResponse) {}

  // Cancel a booking
  rpc CancelBooking(CancelBookingRequest) returns (BookingResponse) {}

  // List bookings for a user
  rpc ListUserBookings(ListUserBookingsRequest) returns (ListBookingsResponse) {}

  // List bookings for a schedule
  rpc ListScheduleBookings(ListScheduleBookingsRequest) returns (ListBookingsResponse) {}

  // Get booking statistics
  rpc GetBookingStats(GetBookingStatsRequest) returns (BookingStatsResponse) {}

  // Get user semester statistics
  rpc GetUserSemesterStats(GetUserSemesterStatsRequest) returns (UserSemesterStatsResponse) {}
}

// Booking status enum
enum BookingStatus {
  PENDING = 0;
  CONFIRMED = 1;
  CANCELLED = 2;
  COMPLETED = 3;
}

// Create booking request
message CreateBookingRequest {
  int64 user_id = 1;
  int64 schedule_id = 2;
}

// Get booking request
message GetBookingRequest {
  int64 id = 1;
}

// Cancel booking request
message CancelBookingRequest {
  int64 id = 1;
  int64 user_id = 2;
}

// List user bookings request
message ListUserBookingsRequest {
  int64 user_id = 1;
  google.protobuf.Timestamp start_date = 2;
  google.protobuf.Timestamp end_date = 3;
  BookingStatus status = 4;
  int32 page = 5;
  int32 page_size = 6;
}

// List schedule bookings request
message ListScheduleBookingsRequest {
  int64 schedule_id = 1;
  BookingStatus status = 2;
  int32 page = 3;
  int32 page_size = 4;
}

// Booking response
message BookingResponse {
  int64 id = 1;
  int64 schedule_id = 2;
  int64 user_id = 3;
  BookingStatus status = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;

  // Additional information
  ScheduleInfo schedule = 7;
  UserInfo user = 8;
}

// List bookings response
message ListBookingsResponse {
  repeated BookingResponse bookings = 1;
  int32 total = 2;
  int32 page = 3;
  int32 page_size = 4;
}

// Get booking stats request
message GetBookingStatsRequest {
  int64 user_id = 1;
  int64 schedule_id = 2;
  google.protobuf.Timestamp start_date = 3;
  google.protobuf.Timestamp end_date = 4;
}

// Booking stats response
message BookingStatsResponse {
  int32 total_bookings = 1;
  int32 pending_bookings = 2;
  int32 confirmed_bookings = 3;
  int32 cancelled_bookings = 4;
  int32 completed_bookings = 5;
}

// Get user semester stats request
message GetUserSemesterStatsRequest {
  int64 user_id = 1;
  int64 semester_id = 2;
}

// User semester stats response
message UserSemesterStatsResponse {
  int64 user_id = 1;
  int64 semester_id = 2;
  int32 total_bookings = 3;
  int32 total_attendance = 4;
  double attendance_rate = 5;
  int32 semester_limit = 6;
  int32 semester_minimum = 7;
  bool passed = 8;
}

// Schedule info (simplified)
message ScheduleInfo {
  int64 id = 1;
  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
  string location = 4;
  string facility_title = 5;
}

// User info (simplified)
message UserInfo {
  int64 id = 1;
  string name = 2;
}
