package postgres

import (
	"context"
	"errors"
	"fmt"

	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

type dailyBookingLimitRepository struct {
	db *pgxpool.Pool
}

// NewDailyBookingLimitRepository creates a new daily booking limit repository
func NewDailyBookingLimitRepository(db *pgxpool.Pool) *dailyBookingLimitRepository {
	return &dailyBookingLimitRepository{db: db}
}

// Create creates a new daily booking limit
func (r *dailyBookingLimitRepository) Create(ctx context.Context, limit *domain.DailyBookingLimit) error {
	query := `
		INSERT INTO daily_booking_limits (semester_id, max_bookings_per_day)
		VALUES ($1, $2)
		RETURNING id, created_at, updated_at
	`
	
	err := r.db.QueryRow(ctx, query,
		limit.SemesterID,
		limit.MaxBookingsPerDay,
	).Scan(
		&limit.ID,
		&limit.CreatedAt,
		&limit.UpdatedAt,
	)
	
	if err != nil {
		return fmt.Errorf("failed to create daily booking limit: %w", err)
	}
	
	return nil
}

// GetByID retrieves a daily booking limit by ID
func (r *dailyBookingLimitRepository) GetByID(ctx context.Context, id int64) (*domain.DailyBookingLimit, error) {
	query := `
		SELECT id, semester_id, max_bookings_per_day, created_at, updated_at
		FROM daily_booking_limits
		WHERE id = $1
	`
	
	var limit domain.DailyBookingLimit
	err := r.db.QueryRow(ctx, query, id).Scan(
		&limit.ID,
		&limit.SemesterID,
		&limit.MaxBookingsPerDay,
		&limit.CreatedAt,
		&limit.UpdatedAt,
	)
	
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, domain.ErrDailyBookingLimitNotFound
		}
		return nil, fmt.Errorf("failed to get daily booking limit: %w", err)
	}
	
	return &limit, nil
}

// GetBySemesterID retrieves a daily booking limit by semester ID
func (r *dailyBookingLimitRepository) GetBySemesterID(ctx context.Context, semesterID int64) (*domain.DailyBookingLimit, error) {
	query := `
		SELECT id, semester_id, max_bookings_per_day, created_at, updated_at
		FROM daily_booking_limits
		WHERE semester_id = $1
	`
	
	var limit domain.DailyBookingLimit
	err := r.db.QueryRow(ctx, query, semesterID).Scan(
		&limit.ID,
		&limit.SemesterID,
		&limit.MaxBookingsPerDay,
		&limit.CreatedAt,
		&limit.UpdatedAt,
	)
	
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, domain.ErrDailyBookingLimitNotFound
		}
		return nil, fmt.Errorf("failed to get daily booking limit: %w", err)
	}
	
	return &limit, nil
}

// Update updates an existing daily booking limit
func (r *dailyBookingLimitRepository) Update(ctx context.Context, limit *domain.DailyBookingLimit) error {
	query := `
		UPDATE daily_booking_limits
		SET max_bookings_per_day = $1
		WHERE id = $2
		RETURNING updated_at
	`
	
	err := r.db.QueryRow(ctx, query,
		limit.MaxBookingsPerDay,
		limit.ID,
	).Scan(
		&limit.UpdatedAt,
	)
	
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return domain.ErrDailyBookingLimitNotFound
		}
		return fmt.Errorf("failed to update daily booking limit: %w", err)
	}
	
	return nil
}

// Delete deletes a daily booking limit by ID
func (r *dailyBookingLimitRepository) Delete(ctx context.Context, id int64) error {
	query := `
		DELETE FROM daily_booking_limits
		WHERE id = $1
	`
	
	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete daily booking limit: %w", err)
	}
	
	if result.RowsAffected() == 0 {
		return domain.ErrDailyBookingLimitNotFound
	}
	
	return nil
}

// List retrieves all daily booking limits
func (r *dailyBookingLimitRepository) List(ctx context.Context, page, pageSize int) ([]*domain.DailyBookingLimit, error) {
	query := `
		SELECT id, semester_id, max_bookings_per_day, created_at, updated_at
		FROM daily_booking_limits
		ORDER BY semester_id
	`
	
	// Add pagination
	if page <= 0 {
		page = 1
	}
	
	if pageSize <= 0 {
		pageSize = 10
	}
	
	offset := (page - 1) * pageSize
	
	query += fmt.Sprintf(" LIMIT %d OFFSET %d", pageSize, offset)
	
	rows, err := r.db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to list daily booking limits: %w", err)
	}
	defer rows.Close()
	
	var limits []*domain.DailyBookingLimit
	for rows.Next() {
		var limit domain.DailyBookingLimit
		err := rows.Scan(
			&limit.ID,
			&limit.SemesterID,
			&limit.MaxBookingsPerDay,
			&limit.CreatedAt,
			&limit.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan daily booking limit: %w", err)
		}
		limits = append(limits, &limit)
	}
	
	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating daily booking limits: %w", err)
	}
	
	return limits, nil
}

// Count counts all daily booking limits
func (r *dailyBookingLimitRepository) Count(ctx context.Context) (int, error) {
	query := `
		SELECT COUNT(*)
		FROM daily_booking_limits
	`
	
	var count int
	err := r.db.QueryRow(ctx, query).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count daily booking limits: %w", err)
	}
	
	return count, nil
}
