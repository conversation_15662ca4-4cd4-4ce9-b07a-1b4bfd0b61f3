syntax = "proto3";

package storagepb;

import "google/protobuf/empty.proto";

option go_package = "/pb";
service StorageService {
  // Загрузка файла в MinIO
  rpc UploadFile(UploadFileRequest) returns (UploadFileResponse);

  // Загрузка (скачивание) файла из MinIO
  rpc DownloadFile(DownloadFileRequest) returns (DownloadFileResponse);

  // Удаление файла из MinIO
  rpc DeleteFile(DeleteFileRequest) returns (google.protobuf.Empty);
}

message UploadFileRequest {
  // bucket, куда мы складываем файл (например "edunite-bucket")
  string bucket_name = 1;

  // как будет называться файл в MinIO (пример "images/banner.jpg")
  string object_name = 2;

  // MIME-тип (например "image/jpeg")
  string content_type = 3;

  // Содержимое файла в формате bytes (байтовый поток).
  bytes file_data = 4;
}

message UploadFileResponse {
  // URL файла в MinIO (или публичная ссылка, если хотите)
  string file_url = 1;
}

message DownloadFileRequest {
  string bucket_name = 1;
  string object_name = 2;
}

message DownloadFileResponse {
  string content_type = 1;
  bytes file_data = 2;
}

message DeleteFileRequest {
  string bucket_name = 1;
  string object_name = 2;
}
