openapi: 3.0.0
info:
  title: Edunite Course API
  description: API documentation for the Course endpoints of the Edunite platform
  version: 1.0.0
  contact:
    name: Edunite Support
    email: <EMAIL>

servers:
  - url: http://localhost:8081
    description: Local development server
  - url: https://api.edunite.com
    description: Production server

tags:
  - name: courses
    description: Course management operations

paths:
  /course:
    post:
      tags:
        - courses
      summary: Create a new course
      description: Creates a new course in the system
      operationId: createCourse
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - title
              properties:
                title:
                  type: string
                  description: Title of the course
                  example: "Introduction to Computer Science"
                description:
                  type: string
                  description: Description of the course
                  example: "A comprehensive introduction to the fundamental concepts of computer science."
                banner_image:
                  type: string
                  format: binary
                  description: Banner image for the course (max 2MB)
      responses:
        '201':
          description: Course created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Course created successfully"
                  course_id:
                    type: integer
                    format: int64
                    example: 1
                  image_url:
                    type: string
                    example: "course-images/1609459200000_course_banner.jpg"
        '400':
          description: Invalid request body
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '413':
          description: File size exceeds limit
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    get:
      tags:
        - courses
      summary: List all courses
      description: Returns a list of all courses in the system
      operationId: getAllCourses
      responses:
        '200':
          description: List of courses
          content:
            application/json:
              schema:
                type: object
                properties:
                  courses:
                    type: array
                    items:
                      $ref: '#/components/schemas/Course'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /course/{id}:
    get:
      tags:
        - courses
      summary: Get course by ID
      description: Returns a specific course by its ID
      operationId: getCourseByID
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the course to retrieve
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Course details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Course'
        '400':
          description: Invalid course ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Course not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    put:
      tags:
        - courses
      summary: Update course
      description: Updates an existing course
      operationId: updateCourse
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the course to update
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - title
              properties:
                title:
                  type: string
                  description: Updated title of the course
                  example: "Advanced Computer Science"
                description:
                  type: string
                  description: Updated description of the course
                  example: "An advanced course covering complex computer science topics."
                banner_image_url:
                  type: string
                  description: URL of the updated banner image
                  example: "course-images/1609459200000_updated_banner.jpg"
      responses:
        '200':
          description: Course updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Course'
        '400':
          description: Invalid request body or course ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Course not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    delete:
      tags:
        - courses
      summary: Delete course
      description: Deletes a course by its ID
      operationId: deleteCourse
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the course to delete
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Course deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Course deleted successfully"
        '400':
          description: Invalid course ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Course not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Course:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        title:
          type: string
          example: "Introduction to Computer Science"
        description:
          type: string
          example: "A comprehensive introduction to the fundamental concepts of computer science."
        banner_image_url:
          type: string
          example: "course-images/1609459200000_course_banner.jpg"
        created_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"

    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
          example: "Invalid request body"
