openapi: 3.0.0
info:
  title: Edunite Semester API
  description: API documentation for the Semester endpoints of the Edunite platform
  version: 1.0.0
  contact:
    name: Edunite Support
    email: <EMAIL>

servers:
  - url: http://localhost:8081
    description: Local development server
  - url: https://api.edunite.com
    description: Production server

tags:
  - name: semesters
    description: Semester management operations

paths:
  /semester:
    post:
      tags:
        - semesters
      summary: Create a new semester
      description: Creates a new semester in the system
      operationId: createSemester
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                name:
                  type: string
                  description: Name of the semester
                  example: "Fall 2023"
                start_date:
                  type: string
                  format: date-time
                  description: Start date of the semester (RFC3339 format)
                  example: "2023-09-01T00:00:00Z"
                end_date:
                  type: string
                  format: date-time
                  description: End date of the semester (RFC3339 format)
                  example: "2023-12-31T23:59:59Z"
      responses:
        '201':
          description: Semester created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Semester'
        '400':
          description: Invalid request body
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    get:
      tags:
        - semesters
      summary: List all semesters
      description: Returns a list of all semesters in the system
      operationId: listSemesters
      responses:
        '200':
          description: List of semesters
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Semester'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /semester/{id}:
    get:
      tags:
        - semesters
      summary: Get semester by ID
      description: Returns a specific semester by its ID
      operationId: getSemesterByID
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the semester to retrieve
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Semester details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Semester'
        '400':
          description: Invalid semester ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Semester not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    put:
      tags:
        - semesters
      summary: Update semester
      description: Updates an existing semester
      operationId: updateSemester
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the semester to update
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                name:
                  type: string
                  description: Updated name of the semester
                  example: "Fall 2023 - Updated"
                start_date:
                  type: string
                  format: date-time
                  description: Updated start date of the semester (RFC3339 format)
                  example: "2023-08-15T00:00:00Z"
                end_date:
                  type: string
                  format: date-time
                  description: Updated end date of the semester (RFC3339 format)
                  example: "2023-12-15T23:59:59Z"
      responses:
        '200':
          description: Semester updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Semester'
        '400':
          description: Invalid request body or semester ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Semester not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    delete:
      tags:
        - semesters
      summary: Delete semester
      description: Deletes a semester by its ID
      operationId: deleteSemester
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the semester to delete
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Semester deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Semester deleted successfully"
        '400':
          description: Invalid semester ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Semester not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Semester:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        name:
          type: string
          example: "Fall 2023"
        start_date:
          type: string
          format: date-time
          example: "2023-09-01T00:00:00Z"
        end_date:
          type: string
          format: date-time
          example: "2023-12-31T23:59:59Z"
        created_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2023-01-01T12:00:00Z"

    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
          example: "Invalid request body"
