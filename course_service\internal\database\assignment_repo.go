package database

import (
	"context"
	"errors"
	"time"

	"github.com/jackc/pgconn"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
)

// Доменные ошибки
var (
	ErrAssignmentNotFound = errors.New("assignment not found")
	ErrAssignmentConflict = errors.New("assignment already exists")
)

// Assignment модель таблицы assignments
type Assignment struct {
	ID                int64     `json:"id"`
	WeekID            int64     `json:"week_id"`
	Title             string    `json:"title"`
	Description       string    `json:"description"`
	DueDate           time.Time `json:"due_date"`
	MaxPoints         int32     `json:"max_points"`
	AssignmentGroupID int64     `json:"assignment_group_id"`
	Type              string    `json:"type"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// ValidateAssignment проверяет входные данные
func ValidateAssignment(v *validator.Validator, a *Assignment) {
	v.Check(a.WeekID > 0, "week_id", "must be provided and > 0")
	v.Check(a.Title != "", "title", "must be provided")
	v.Check(len(a.Title) <= 255, "title", "max length is 255 characters")
	v.Check(len(a.Description) <= 50000, "description", "max length is 50000 characters")

	// Set default type if not specified
	if a.Type == "" {
		a.Type = "task" // Default to 'task' if not specified
	}

	// Validate type
	v.Check(a.Type == "task" || a.Type == "info", "type", "must be either 'task' or 'info'")

	// Only validate max_points and due_date for 'task' type
	if a.Type == "task" {
		v.Check(a.MaxPoints >= 0, "max_points", "must be >= 0")
		// Due date is required for tasks
		v.Check(!a.DueDate.IsZero(), "due_date", "must be provided for task type")
	} else if a.Type == "info" {
		// For 'info' type, set max_points to 0 regardless of input
		a.MaxPoints = 0
		// Due date is not required for info type, but if provided, it should be valid
		if a.DueDate.IsZero() {
			// Set a far future date if not provided
			a.DueDate = time.Now().AddDate(10, 0, 0) // 10 years in the future
		}
	}
}

// AssignmentRepository интерфейс для CRUD операций
type AssignmentRepository interface {
	CreateAssignment(ctx context.Context, a *Assignment) error
	GetAssignment(ctx context.Context, id int64) (*Assignment, error)
	ListAssignmentsByWeek(ctx context.Context, weekID int64) ([]*Assignment, error)
	UpdateAssignment(ctx context.Context, a *Assignment) error
	DeleteAssignment(ctx context.Context, id int64) error
}

type assignmentRepository struct {
	db *pgxpool.Pool
}

// NewAssignmentRepository конструктор
func NewAssignmentRepository(db *pgxpool.Pool) AssignmentRepository {
	return &assignmentRepository{db: db}
}

// CreateAssignment вставляет новую задачу
func (r *assignmentRepository) CreateAssignment(ctx context.Context, a *Assignment) error {
	query := `
	INSERT INTO assignments (week_id, title, description, due_date, max_points, assignment_group_id, type)
	VALUES ($1,$2,$3,$4,$5,$6,$7)
	RETURNING id, created_at, updated_at`
	row := r.db.QueryRow(ctx, query,
		a.WeekID, a.Title, a.Description, a.DueDate, a.MaxPoints, a.AssignmentGroupID, a.Type)
	if err := row.Scan(&a.ID, &a.CreatedAt, &a.UpdatedAt); err != nil {
		var pgErr *pgconn.PgError
		if errors.As(err, &pgErr) && pgErr.Code == "23505" {
			return ErrAssignmentConflict
		}
		return err
	}
	return nil
}

// GetAssignment возвращает задачу по ID
func (r *assignmentRepository) GetAssignment(ctx context.Context, id int64) (*Assignment, error) {
	query := `SELECT id, week_id, title, description, due_date, max_points, assignment_group_id, type, created_at, updated_at FROM assignments WHERE id=$1`
	a := &Assignment{}
	err := r.db.QueryRow(ctx, query, id).Scan(
		&a.ID, &a.WeekID, &a.Title, &a.Description, &a.DueDate,
		&a.MaxPoints, &a.AssignmentGroupID, &a.Type, &a.CreatedAt, &a.UpdatedAt,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, ErrAssignmentNotFound
		}
		return nil, err
	}
	return a, nil
}

// ListAssignmentsByWeek возвращает все задачи для недели
func (r *assignmentRepository) ListAssignmentsByWeek(ctx context.Context, weekID int64) ([]*Assignment, error) {
	query := `SELECT id, week_id, title, description, due_date, max_points, assignment_group_id, type, created_at, updated_at FROM assignments WHERE week_id=$1 ORDER BY due_date`
	rows, err := r.db.Query(ctx, query, weekID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var list []*Assignment
	for rows.Next() {
		a := &Assignment{}
		if err := rows.Scan(
			&a.ID, &a.WeekID, &a.Title, &a.Description, &a.DueDate,
			&a.MaxPoints, &a.AssignmentGroupID, &a.Type, &a.CreatedAt, &a.UpdatedAt,
		); err != nil {
			return nil, err
		}
		list = append(list, a)
	}
	return list, rows.Err()
}

// UpdateAssignment обновляет сущность
func (r *assignmentRepository) UpdateAssignment(ctx context.Context, a *Assignment) error {
	query := `UPDATE assignments SET week_id=$1, title=$2, description=$3, due_date=$4, max_points=$5, assignment_group_id=$6, type=$7, updated_at=NOW() WHERE id=$8 RETURNING created_at, updated_at`
	row := r.db.QueryRow(ctx, query,
		a.WeekID, a.Title, a.Description, a.DueDate, a.MaxPoints, a.AssignmentGroupID, a.Type, a.ID)
	if err := row.Scan(&a.CreatedAt, &a.UpdatedAt); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return ErrAssignmentNotFound
		}
		return err
	}
	return nil
}

// DeleteAssignment удаляет запись
func (r *assignmentRepository) DeleteAssignment(ctx context.Context, id int64) error {
	tag, err := r.db.Exec(ctx, `DELETE FROM assignments WHERE id=$1`, id)
	if err != nil {
		return err
	}
	if tag.RowsAffected() == 0 {
		return ErrAssignmentNotFound
	}
	return nil
}
