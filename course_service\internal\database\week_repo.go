package database

import (
	"context"
	"errors"
	"github.com/jackc/pgconn"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
	"time"
)

var (
	ErrWeekNotFound = errors.New("week not found")
	ErrWeekConflict = errors.New("week already exists for this thread and number")
)

// Week модель таблицы week
type Week struct {
	ID          int64     `json:"id"`
	ThreadID    int64     `json:"thread_id"`
	WeekNumber  uint32    `json:"week_number"`
	Type        string    `json:"type"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type WeekRepository interface {
	CreateWeek(ctx context.Context, week *Week) error
	UpdateWeek(ctx context.Context, week *Week) error
	DeleteWeek(ctx context.Context, id int64) error
	GetWeek(ctx context.Context, id int64) (*Week, error)
	GetWeeksByThreadID(ctx context.Context, threadID int64) ([]*Week, error)
}

type weekRepository struct {
	db *pgxpool.Pool
}

// NewWeekRepository конструктор
func NewWeekRepository(db *pgxpool.Pool) WeekRepository {
	return &weekRepository{db: db}
}

func ValidateWeek(v *validator.Validator, w *Week) {
	v.Check(w.ThreadID > 0, "thread_id", "must be provided and > 0")
	v.Check(w.WeekNumber > 0 && w.WeekNumber <= 52, "week_number", "must be between 1 and 52")
	v.Check(validator.PermittedValue(w.Type, "midterm", "endterm", "final"),
		"type", "must be one of 'midterm','endterm','final'")
	v.Check(w.Title != "", "title", "must be provided")
	v.Check(len(w.Title) <= 255, "title", "max length is 255 characters")
	v.Check(len(w.Description) <= 2000, "description", "max length is 2000 characters")
}

func (r *weekRepository) CreateWeek(ctx context.Context, w *Week) error {
	query := `
		INSERT INTO weeks(thread_id, week_number, type, title, description)
		VALUES ($1,$2,$3,$4,$5)
		RETURNING id, created_at, updated_at
	`
	row := r.db.QueryRow(ctx, query, w.ThreadID, w.WeekNumber, w.Type, w.Title, w.Description)
	if err := row.Scan(&w.ID, &w.CreatedAt, &w.UpdatedAt); err != nil {
		var pgErr *pgconn.PgError
		if errors.As(err, &pgErr) && pgErr.Code == "23505" {
			return ErrWeekConflict
		}
		return err
	}
	return nil
}

func (r *weekRepository) UpdateWeek(ctx context.Context, w *Week) error {
	query := `
		UPDATE weeks
		SET thread_id=$1, week_number=$2, type=$3, title=$4, description=$5, updated_at=NOW()
		WHERE id=$6
		RETURNING created_at, updated_at
	`
	row := r.db.QueryRow(ctx, query, w.ThreadID, w.WeekNumber, w.Type, w.Title, w.Description, w.ID)
	if err := row.Scan(&w.CreatedAt, &w.UpdatedAt); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return ErrWeekNotFound
		}
		return err
	}
	return nil
}

func (r *weekRepository) DeleteWeek(ctx context.Context, id int64) error {
	cmd, err := r.db.Exec(ctx, `DELETE FROM weeks WHERE id = $1`, id)
	if err != nil {
		return err
	}
	if cmd.RowsAffected() == 0 {
		return ErrWeekNotFound
	}
	return nil
}

func (r *weekRepository) GetWeek(ctx context.Context, id int64) (*Week, error) {
	query := `
		SELECT id, thread_id, week_number, type, title, description, created_at, updated_at
		FROM weeks WHERE id = $1
	`
	w := &Week{}
	err := r.db.QueryRow(ctx, query, id).Scan(
		&w.ID, &w.ThreadID, &w.WeekNumber, &w.Type,
		&w.Title, &w.Description, &w.CreatedAt, &w.UpdatedAt,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, ErrWeekNotFound
		}
		return nil, err
	}
	return w, nil
}

func (r *weekRepository) GetWeeksByThreadID(ctx context.Context, threadID int64) ([]*Week, error) {
	query := `
		SELECT id, thread_id, week_number, type, title, description, created_at, updated_at
		FROM weeks WHERE thread_id = $1 ORDER BY week_number
	`
	rows, err := r.db.Query(ctx, query, threadID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var list []*Week
	for rows.Next() {
		w := &Week{}
		err = rows.Scan(
			&w.ID, &w.ThreadID, &w.WeekNumber, &w.Type,
			&w.Title, &w.Description, &w.CreatedAt, &w.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		list = append(list, w)
	}
	if rows.Err() != nil {
		return nil, rows.Err()
	}
	return list, nil
}
