package grpc

import (
	"context"
	"errors"
	sportpb "github.com/olzzhas/edunite-server/sport_service/proto"

	"github.com/golang/protobuf/ptypes/empty"
	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
	"github.com/olzzhas/edunite-server/sport_service/internal/service"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type semesterLimitServer struct {
	sportpb.UnimplementedSemesterLimitServiceServer
	services *service.Services
}

// NewSemesterLimitServer creates a new semester limit gRPC server
func NewSemesterLimitServer(services *service.Services) *semesterLimitServer {
	return &semesterLimitServer{
		services: services,
	}
}

// CreateSemesterLimit creates a new semester sport limit
func (s *semesterLimitServer) CreateSemesterLimit(ctx context.Context, req *sportpb.CreateSemesterLimitRequest) (*sportpb.SemesterLimitResponse, error) {
	limit := &domain.SemesterSportLimit{
		SemesterID: req.SemesterId,
		MinLessons: int(req.MinLessons),
		MaxLessons: int(req.MaxLessons),
	}

	err := s.services.SemesterSportLimit.CreateSemesterSportLimit(ctx, limit)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to create semester limit: %v", err)
	}

	return s.convertSemesterLimitToProto(limit), nil
}

// GetSemesterLimit retrieves a semester sport limit by ID
func (s *semesterLimitServer) GetSemesterLimit(ctx context.Context, req *sportpb.GetSemesterLimitRequest) (*sportpb.SemesterLimitResponse, error) {
	limit, err := s.services.SemesterSportLimit.GetSemesterSportLimit(ctx, req.Id)
	if err != nil {
		if err == domain.ErrSemesterSportLimitNotFound {
			return nil, status.Errorf(codes.NotFound, "semester limit not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to get semester limit: %v", err)
	}

	return s.convertSemesterLimitToProto(limit), nil
}

// GetSemesterLimitBySemester retrieves a semester sport limit by semester ID
func (s *semesterLimitServer) GetSemesterLimitBySemester(ctx context.Context, req *sportpb.GetSemesterLimitBySemesterRequest) (*sportpb.SemesterLimitResponse, error) {
	limit, err := s.services.SemesterSportLimit.GetSemesterSportLimitBySemesterID(ctx, req.SemesterId)
	if err != nil {
		if err == domain.ErrSemesterSportLimitNotFound {
			return nil, status.Errorf(codes.NotFound, "semester limit not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to get semester limit: %v", err)
	}

	return s.convertSemesterLimitToProto(limit), nil
}

// UpdateSemesterLimit updates an existing semester sport limit
func (s *semesterLimitServer) UpdateSemesterLimit(ctx context.Context, req *sportpb.UpdateSemesterLimitRequest) (*sportpb.SemesterLimitResponse, error) {
	limit := &domain.SemesterSportLimit{
		ID:         req.Id,
		MinLessons: int(req.MinLessons),
		MaxLessons: int(req.MaxLessons),
	}

	err := s.services.SemesterSportLimit.UpdateSemesterSportLimit(ctx, limit)
	if err != nil {
		if err == domain.ErrSemesterSportLimitNotFound {
			return nil, status.Errorf(codes.NotFound, "semester limit not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to update semester limit: %v", err)
	}

	// Get the updated limit
	updatedLimit, err := s.services.SemesterSportLimit.GetSemesterSportLimit(ctx, req.Id)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get updated semester limit: %v", err)
	}

	return s.convertSemesterLimitToProto(updatedLimit), nil
}

// DeleteSemesterLimit deletes a semester sport limit
func (s *semesterLimitServer) DeleteSemesterLimit(ctx context.Context, req *sportpb.DeleteSemesterLimitRequest) (*empty.Empty, error) {
	err := s.services.SemesterSportLimit.DeleteSemesterSportLimit(ctx, req.Id)
	if err != nil {
		if errors.Is(err, domain.ErrSemesterSportLimitNotFound) {
			return nil, status.Errorf(codes.NotFound, "semester limit not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to delete semester limit: %v", err)
	}

	return &emptypb.Empty{}, nil
}

// ListSemesterLimits lists all semester sport limits
func (s *semesterLimitServer) ListSemesterLimits(ctx context.Context, req *sportpb.ListSemesterLimitsRequest) (*sportpb.ListSemesterLimitsResponse, error) {
	filter := domain.SemesterSportLimitFilter{
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
	}

	limits, total, err := s.services.SemesterSportLimit.ListSemesterSportLimits(ctx, filter)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list semester limits: %v", err)
	}

	response := &sportpb.ListSemesterLimitsResponse{
		Total:    int32(total),
		Page:     int32(filter.Page),
		PageSize: int32(filter.PageSize),
	}

	for _, limit := range limits {
		response.Limits = append(response.Limits, s.convertSemesterLimitToProto(limit))
	}

	return response, nil
}

// CreateDailyBookingLimit creates a new daily booking limit
func (s *semesterLimitServer) CreateDailyBookingLimit(ctx context.Context, req *sportpb.CreateDailyBookingLimitRequest) (*sportpb.DailyBookingLimitResponse, error) {
	limit := &domain.DailyBookingLimit{
		SemesterID:        req.SemesterId,
		MaxBookingsPerDay: int(req.MaxBookingsPerDay),
	}

	err := s.services.DailyBookingLimit.CreateDailyBookingLimit(ctx, limit)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to create daily booking limit: %v", err)
	}

	return s.convertDailyBookingLimitToProto(limit), nil
}

// GetDailyBookingLimit retrieves a daily booking limit by ID
func (s *semesterLimitServer) GetDailyBookingLimit(ctx context.Context, req *sportpb.GetDailyBookingLimitRequest) (*sportpb.DailyBookingLimitResponse, error) {
	limit, err := s.services.DailyBookingLimit.GetDailyBookingLimit(ctx, req.Id)
	if err != nil {
		if err == domain.ErrDailyBookingLimitNotFound {
			return nil, status.Errorf(codes.NotFound, "daily booking limit not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to get daily booking limit: %v", err)
	}

	return s.convertDailyBookingLimitToProto(limit), nil
}

// GetDailyBookingLimitBySemester retrieves a daily booking limit by semester ID
func (s *semesterLimitServer) GetDailyBookingLimitBySemester(ctx context.Context, req *sportpb.GetDailyBookingLimitBySemesterRequest) (*sportpb.DailyBookingLimitResponse, error) {
	limit, err := s.services.DailyBookingLimit.GetDailyBookingLimitBySemesterID(ctx, req.SemesterId)
	if err != nil {
		if err == domain.ErrDailyBookingLimitNotFound {
			return nil, status.Errorf(codes.NotFound, "daily booking limit not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to get daily booking limit: %v", err)
	}

	return s.convertDailyBookingLimitToProto(limit), nil
}

// UpdateDailyBookingLimit updates an existing daily booking limit
func (s *semesterLimitServer) UpdateDailyBookingLimit(ctx context.Context, req *sportpb.UpdateDailyBookingLimitRequest) (*sportpb.DailyBookingLimitResponse, error) {
	limit := &domain.DailyBookingLimit{
		ID:                req.Id,
		MaxBookingsPerDay: int(req.MaxBookingsPerDay),
	}

	err := s.services.DailyBookingLimit.UpdateDailyBookingLimit(ctx, limit)
	if err != nil {
		if err == domain.ErrDailyBookingLimitNotFound {
			return nil, status.Errorf(codes.NotFound, "daily booking limit not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to update daily booking limit: %v", err)
	}

	// Get the updated limit
	updatedLimit, err := s.services.DailyBookingLimit.GetDailyBookingLimit(ctx, req.Id)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get updated daily booking limit: %v", err)
	}

	return s.convertDailyBookingLimitToProto(updatedLimit), nil
}

// DeleteDailyBookingLimit deletes a daily booking limit
func (s *semesterLimitServer) DeleteDailyBookingLimit(ctx context.Context, req *sportpb.DeleteDailyBookingLimitRequest) (*empty.Empty, error) {
	err := s.services.DailyBookingLimit.DeleteDailyBookingLimit(ctx, req.Id)
	if err != nil {
		if errors.Is(err, domain.ErrDailyBookingLimitNotFound) {
			return nil, status.Errorf(codes.NotFound, "daily booking limit not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to delete daily booking limit: %v", err)
	}

	return &emptypb.Empty{}, nil
}

// ListDailyBookingLimits lists all daily booking limits
func (s *semesterLimitServer) ListDailyBookingLimits(ctx context.Context, req *sportpb.ListDailyBookingLimitsRequest) (*sportpb.ListDailyBookingLimitsResponse, error) {
	limits, total, err := s.services.DailyBookingLimit.ListDailyBookingLimits(ctx, int(req.Page), int(req.PageSize))
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list daily booking limits: %v", err)
	}

	response := &sportpb.ListDailyBookingLimitsResponse{
		Total:    int32(total),
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	for _, limit := range limits {
		response.Limits = append(response.Limits, s.convertDailyBookingLimitToProto(limit))
	}

	return response, nil
}

// Helper function to convert domain.SemesterSportLimit to sportpb.SemesterLimitResponse
func (s *semesterLimitServer) convertSemesterLimitToProto(limit *domain.SemesterSportLimit) *sportpb.SemesterLimitResponse {
	return &sportpb.SemesterLimitResponse{
		Id:         limit.ID,
		SemesterId: limit.SemesterID,
		MinLessons: int32(limit.MinLessons),
		MaxLessons: int32(limit.MaxLessons),
		CreatedAt:  timestamppb.New(limit.CreatedAt),
		UpdatedAt:  timestamppb.New(limit.UpdatedAt),
	}
}

// Helper function to convert domain.DailyBookingLimit to sportpb.DailyBookingLimitResponse
func (s *semesterLimitServer) convertDailyBookingLimitToProto(limit *domain.DailyBookingLimit) *sportpb.DailyBookingLimitResponse {
	return &sportpb.DailyBookingLimitResponse{
		Id:                limit.ID,
		SemesterId:        limit.SemesterID,
		MaxBookingsPerDay: int32(limit.MaxBookingsPerDay),
		CreatedAt:         timestamppb.New(limit.CreatedAt),
		UpdatedAt:         timestamppb.New(limit.UpdatedAt),
	}
}
