package clients

import (
	"encoding/json"
	"fmt"
	"log"
	"time"
)

// FallbackLogPublisher is a simple logger that prints to stdout
// It's used when RabbitMQ is not available
type FallbackLogPublisher struct{}

// NewFallbackLogPublisher creates a new FallbackLogPublisher
func NewFallbackLogPublisher() *FallbackLogPublisher {
	return &FallbackLogPublisher{}
}

// PublishLog prints the log to stdout
func (p *FallbackLogPublisher) PublishLog(level, message, service string, data map[string]any) error {
	logEvent := map[string]interface{}{
		"level":    level,
		"message":  message,
		"service":  service,
		"data":     data,
		"datetime": time.Now().UTC().Format(time.RFC3339),
	}

	jsonData, err := json.Marshal(logEvent)
	if err != nil {
		log.Printf("FallbackLogger: failed to marshal: %v", err)
		return err
	}

	fmt.Printf("FALLBACK LOG: %s\n", string(jsonData))
	return nil
}

// Close is a no-op for the fallback logger
func (p *FallbackLogPublisher) Close() {
	// No-op
}
