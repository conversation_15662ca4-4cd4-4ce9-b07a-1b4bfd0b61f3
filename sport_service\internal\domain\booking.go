package domain

import (
	"time"
)

// BookingStatus represents the status of a booking
type BookingStatus string

const (
	BookingStatusPending   BookingStatus = "pending"
	BookingStatusConfirmed BookingStatus = "confirmed"
	BookingStatusCancelled BookingStatus = "cancelled"
	BookingStatusCompleted BookingStatus = "completed"
)

// Booking represents a user's booking of a facility at a specific time
type Booking struct {
	ID         int64         `json:"id"`
	ScheduleID int64         `json:"schedule_id"`
	UserID     int64         `json:"user_id"`
	Status     BookingStatus `json:"status"`
	CreatedAt  time.Time     `json:"created_at"`
	UpdatedAt  time.Time     `json:"updated_at"`
}

// BookingFilter represents filters for querying bookings
type BookingFilter struct {
	ScheduleID int64
	UserID     int64
	Status     BookingStatus
	StartDate  time.Time
	EndDate    time.Time
	Page       int
	PageSize   int
}

// BookingStats represents statistics about bookings
type BookingStats struct {
	TotalBookings     int `json:"total_bookings"`
	PendingBookings   int `json:"pending_bookings"`
	ConfirmedBookings int `json:"confirmed_bookings"`
	CancelledBookings int `json:"cancelled_bookings"`
	CompletedBookings int `json:"completed_bookings"`
}

// Error definitions for booking operations
var (
	ErrBookingNotFound       = Error{"booking not found"}
	ErrDuplicateBooking      = Error{"booking already exists"}
	ErrScheduleFull          = Error{"schedule is at maximum capacity"}
	ErrBookingClosed         = Error{"booking is closed for this schedule"}
	ErrCancellationDeadline  = Error{"cancellation deadline has passed"}
	ErrDailyBookingLimit     = Error{"daily booking limit reached"}
	ErrSemesterBookingLimit  = Error{"semester booking limit reached"}
	ErrMedicalCertificateRequired = Error{"medical certificate required for this facility"}
	ErrInvalidMedicalCertificate  = Error{"valid medical certificate not found"}
)
