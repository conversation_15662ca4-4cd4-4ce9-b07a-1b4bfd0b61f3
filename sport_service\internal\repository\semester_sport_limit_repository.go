package repository

import (
	"context"

	"github.com/olzzhas/edunite-server/sport_service/internal/domain"
)

// SemesterSportLimitRepository defines the interface for semester sport limit data access
type SemesterSportLimitRepository interface {
	// Create creates a new semester sport limit
	Create(ctx context.Context, limit *domain.SemesterSportLimit) error

	// GetByID retrieves a semester sport limit by ID
	GetByID(ctx context.Context, id int64) (*domain.SemesterSportLimit, error)

	// GetBySemesterID retrieves a semester sport limit by semester ID
	GetBySemesterID(ctx context.Context, semesterID int64) (*domain.SemesterSportLimit, error)

	// Update updates an existing semester sport limit
	Update(ctx context.Context, limit *domain.SemesterSportLimit) error

	// Delete deletes a semester sport limit by ID
	Delete(ctx context.Context, id int64) error

	// List retrieves semester sport limits based on filters
	List(ctx context.Context, filter domain.SemesterSportLimitFilter) ([]*domain.SemesterSportLimit, error)

	// Count counts semester sport limits based on filters
	Count(ctx context.Context, filter domain.SemesterSportLimitFilter) (int, error)

	// GetUserSemesterStats retrieves a user's statistics for a semester
	GetUserSemesterStats(ctx context.Context, userID, semesterID int64) ([]*domain.UserSemesterStats, error)
}
