package dto

import "time"

// AssignmentWithSubmissionDTO — одна строка из запроса
type AssignmentWithSubmissionDTO struct {
	// поля из assignments
	AssID             int64
	WeekID            int64
	Title             string
	Description       *string
	DueDate           *time.Time
	MaxPoints         *int32
	AssignmentGroupID *int64
	Type              string
	AssCreatedAt      time.Time
	AssUpdatedAt      time.Time

	// поля из submission (может быть NULL)
	SubID        *int64
	UserID       *int64
	SubmittedAt  *time.Time
	FileURLs     []string
	Comment      *string
	Score        *int32
	Feedback     *string
	SubCreatedAt *time.Time
	SubUpdatedAt *time.Time
}
