package thread

import (
	"context"
	"errors"

	"github.com/olzzhas/edunite-server/course_service/internal/database"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// GetLocationByName retrieves a location by name
func (s *Service) GetLocationByName(
	ctx context.Context, name string,
) (*database.Location, error) {
	location, err := s.locationRepo.GetByName(ctx, name)
	if err != nil {
		if errors.Is(err, database.ErrLocationNotFound) {
			return nil, status.Errorf(codes.NotFound, "location not found")
		}
		return nil, status.Errorf(codes.Internal, "fetch location: %v", err)
	}
	return location, nil
}
