syntax = "proto3";

package sportpb;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "proto/schedule.proto";

option go_package = "github.com/olzzhas/edunite-server/sport_service/pb/sportpb";

service SemesterLimitService {
  // Create a new semester sport limit
  rpc CreateSemesterLimit(CreateSemesterLimitRequest) returns (SemesterLimitResponse) {}

  // Get a semester sport limit by ID
  rpc GetSemesterLimit(GetSemesterLimitRequest) returns (SemesterLimitResponse) {}

  // Get a semester sport limit by semester ID
  rpc GetSemesterLimitBySemester(GetSemesterLimitBySemesterRequest) returns (SemesterLimitResponse) {}

  // Update an existing semester sport limit
  rpc UpdateSemesterLimit(UpdateSemesterLimitRequest) returns (SemesterLimitResponse) {}

  // Delete a semester sport limit
  rpc DeleteSemesterLimit(DeleteSemesterLimitRequest) returns (google.protobuf.Empty) {}

  // List all semester sport limits
  rpc ListSemesterLimits(ListSemesterLimitsRequest) returns (ListSemesterLimitsResponse) {}

  // Create a new daily booking limit
  rpc CreateDailyBookingLimit(CreateDailyBookingLimitRequest) returns (DailyBookingLimitResponse) {}

  // Get a daily booking limit by ID
  rpc GetDailyBookingLimit(GetDailyBookingLimitRequest) returns (DailyBookingLimitResponse) {}

  // Get a daily booking limit by semester ID
  rpc GetDailyBookingLimitBySemester(GetDailyBookingLimitBySemesterRequest) returns (DailyBookingLimitResponse) {}

  // Update an existing daily booking limit
  rpc UpdateDailyBookingLimit(UpdateDailyBookingLimitRequest) returns (DailyBookingLimitResponse) {}

  // Delete a daily booking limit
  rpc DeleteDailyBookingLimit(DeleteDailyBookingLimitRequest) returns (google.protobuf.Empty) {}

  // List all daily booking limits
  rpc ListDailyBookingLimits(ListDailyBookingLimitsRequest) returns (ListDailyBookingLimitsResponse) {}
}

// Create semester limit request
message CreateSemesterLimitRequest {
  int64 semester_id = 1;
  int32 min_lessons = 2;
  int32 max_lessons = 3;
}

// Get semester limit request
message GetSemesterLimitRequest {
  int64 id = 1;
}

// Get semester limit by semester request
message GetSemesterLimitBySemesterRequest {
  int64 semester_id = 1;
}

// Update semester limit request
message UpdateSemesterLimitRequest {
  int64 id = 1;
  int32 min_lessons = 2;
  int32 max_lessons = 3;
}

// Delete semester limit request
message DeleteSemesterLimitRequest {
  int64 id = 1;
}

// List semester limits request
message ListSemesterLimitsRequest {
  int32 page = 1;
  int32 page_size = 2;
}

// Semester limit response
message SemesterLimitResponse {
  int64 id = 1;
  int64 semester_id = 2;
  int32 min_lessons = 3;
  int32 max_lessons = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;

  // Additional information
  SemesterInfo semester = 7;
}

// List semester limits response
message ListSemesterLimitsResponse {
  repeated SemesterLimitResponse limits = 1;
  int32 total = 2;
  int32 page = 3;
  int32 page_size = 4;
}

// Create daily booking limit request
message CreateDailyBookingLimitRequest {
  int64 semester_id = 1;
  int32 max_bookings_per_day = 2;
}

// Get daily booking limit request
message GetDailyBookingLimitRequest {
  int64 id = 1;
}

// Get daily booking limit by semester request
message GetDailyBookingLimitBySemesterRequest {
  int64 semester_id = 1;
}

// Update daily booking limit request
message UpdateDailyBookingLimitRequest {
  int64 id = 1;
  int32 max_bookings_per_day = 2;
}

// Delete daily booking limit request
message DeleteDailyBookingLimitRequest {
  int64 id = 1;
}

// List daily booking limits request
message ListDailyBookingLimitsRequest {
  int32 page = 1;
  int32 page_size = 2;
}

// Daily booking limit response
message DailyBookingLimitResponse {
  int64 id = 1;
  int64 semester_id = 2;
  int32 max_bookings_per_day = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;

  // Additional information
  SemesterInfo semester = 6;
}

// List daily booking limits response
message ListDailyBookingLimitsResponse {
  repeated DailyBookingLimitResponse limits = 1;
  int32 total = 2;
  int32 page = 3;
  int32 page_size = 4;
}
