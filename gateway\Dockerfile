FROM golang:1.23-alpine

# Install git for go modules and protoc-gen-* if needed
RUN apk add --no-cache git

WORKDIR /app

# Copy local modules for your 'replace' directive
COPY sport_service /sport_service
COPY user_service   /user_service
COPY course_service /course_service
COPY storage_service /storage_service
COPY auth_service /auth_service

# Copy gateway module files and download deps
COPY ./gateway/go.mod ./gateway/go.sum ./
RUN go mod download

# Copy only the Gateway code
COPY gateway ./

# Build or run
EXPOSE 8081
CMD ["go", "run", "main.go"]
