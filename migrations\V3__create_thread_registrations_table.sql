-- thread_registrations
CREATE TABLE IF NOT EXISTS thread_registrations (
    user_id BIGINT NOT NULL,
    thread_id BIGINT NOT NULL,
    registration_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    final_grade FLOAT CHECK (final_grade >= 0 AND final_grade <= 100),
    PRIMARY KEY (user_id, thread_id),
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (thread_id) REFERENCES threads(id) ON DELETE CASCADE
);

-- Опционально: Добавление отдельных индексов для оптимизации запросов
CREATE INDEX IF NOT EXISTS idx_thread_registrations_user_id ON thread_registrations(user_id);
CREATE INDEX IF NOT EXISTS idx_thread_registrations_thread_id ON thread_registrations(thread_id);
