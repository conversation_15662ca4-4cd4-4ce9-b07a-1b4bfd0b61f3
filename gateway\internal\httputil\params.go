package httputil

import (
	"github.com/olzzhas/edunite-server/gateway/pkg/validator"
	"strconv"

	"github.com/gin-gonic/gin"
)

// ReadString вытаскивает строковый параметр или значение по умолчанию.
func ReadString(c *gin.Context, key, def string, v *validator.Validator) string {
	s := c.Query(key)
	if s == "" {
		return def
	}
	return s
}

// ReadInt вытаскивает целочисленный параметр или значение по умолчанию.
// При ошибке парсинга добавляет запись в v.Errors.
func ReadInt(c *gin.Context, key string, def int, v *validator.Validator) int {
	s := c.<PERSON><PERSON>(key)
	if s == "" {
		return def
	}
	i, err := strconv.Atoi(s)
	if err != nil {
		v.AddError(key, "must be an integer value")
		return def
	}
	return i
}
