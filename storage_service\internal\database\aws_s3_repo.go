package database

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/olzzhas/edunite-server/storage_service/internal/config"
)

// AWSS3Repository - interface for AWS S3 storage
type AWSS3Repository interface {
	Upload(ctx context.Context, bucketName, objectName string, data []byte, contentType string) (string, error)
	Download(ctx context.Context, bucketName, objectName string) ([]byte, string, error)
	Delete(ctx context.Context, bucketName, objectName string) error
}

type awsS3Repository struct {
	client   *s3.S3
	uploader *s3manager.Uploader
	config   *config.Config
	session  *session.Session
}

// NewAWSS3Repository creates a new AWSS3Repository
func NewAWSS3Repository(cfg *config.Config) (AWSS3Repository, error) {
	// Create a custom HTTP client with longer timeouts
	httpClient := &http.Client{
		Timeout: time.Second * 30,
	}

	// Create a new AWS session with the custom HTTP client
	// Use the region from the config
	sess, err := session.NewSession(&aws.Config{
		Region:      aws.String(cfg.AWSRegion),
		Credentials: credentials.NewStaticCredentials(cfg.AWSAccessKey, cfg.AWSSecretKey, ""),
		HTTPClient:  httpClient,
		// Do not use path-style addressing
		S3ForcePathStyle: aws.Bool(false),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create AWS session: %w", err)
	}

	// Create S3 client
	s3Client := s3.New(sess)

	// Create S3 uploader
	uploader := s3manager.NewUploader(sess)

	repo := &awsS3Repository{
		client:   s3Client,
		uploader: uploader,
		config:   cfg,
		session:  sess,
	}

	// Skip bucket initialization
	fmt.Println("Skipping bucket creation - using existing buckets")

	return repo, nil
}

// Upload uploads a file to the specified bucket
func (r *awsS3Repository) Upload(ctx context.Context, bucketName, objectName string, data []byte, contentType string) (string, error) {
	// Use a fixed bucket name from the config
	fixedBucketName := "edunite-storage"

	// Check if the bucket exists, if not create it
	_, err := r.client.HeadBucket(&s3.HeadBucketInput{
		Bucket: aws.String(fixedBucketName),
	})
	if err != nil {
		fmt.Printf("Error checking bucket %s: %v\n", fixedBucketName, err)

		// Try to create the bucket
		_, err = r.client.CreateBucket(&s3.CreateBucketInput{
			Bucket: aws.String(fixedBucketName),
			CreateBucketConfiguration: &s3.CreateBucketConfiguration{
				LocationConstraint: aws.String(r.config.AWSRegion),
			},
		})
		if err != nil {
			return "", fmt.Errorf("failed to create bucket: %w", err)
		}

		// Wait until the bucket is created
		err = r.client.WaitUntilBucketExists(&s3.HeadBucketInput{
			Bucket: aws.String(fixedBucketName),
		})
		if err != nil {
			return "", fmt.Errorf("failed to wait for bucket creation: %w", err)
		}

		// Make the bucket public
		_, err = r.client.PutBucketPolicy(&s3.PutBucketPolicyInput{
			Bucket: aws.String(fixedBucketName),
			Policy: aws.String(`{
				"Version": "2012-10-17",
				"Statement": [
					{
						"Sid": "PublicReadGetObject",
						"Effect": "Allow",
						"Principal": "*",
						"Action": "s3:GetObject",
						"Resource": "arn:aws:s3:::` + fixedBucketName + `/*"
					}
				]
			}`),
		})
		if err != nil {
			fmt.Printf("Warning: Could not set bucket policy: %v\n", err)
		}
	}

	// Create a folder structure based on the original bucket name
	folderPath := bucketName + "/"
	fullObjectName := folderPath + objectName

	// Use the S3 manager uploader for better performance and reliability
	result, err := r.uploader.Upload(&s3manager.UploadInput{
		Bucket:      aws.String(fixedBucketName),
		Key:         aws.String(fullObjectName),
		Body:        bytes.NewReader(data),
		ContentType: aws.String(contentType),
		ACL:         aws.String("public-read"), // Make the object publicly readable
	})

	if err != nil {
		return "", fmt.Errorf("failed to upload object: %w", err)
	}

	// Return the URL from the upload result
	return result.Location, nil
}

// Download downloads a file from the specified bucket
func (r *awsS3Repository) Download(ctx context.Context, bucketName, objectName string) ([]byte, string, error) {
	// Use a fixed bucket name from the config
	fixedBucketName := "edunite-storage"

	// Create a folder structure based on the original bucket name
	folderPath := bucketName + "/"
	fullObjectName := folderPath + objectName

	// Create a downloader
	downloader := s3manager.NewDownloader(r.session)

	// Create a buffer to write the downloaded content
	buf := aws.NewWriteAtBuffer([]byte{})

	// Download the object
	_, err := downloader.Download(buf, &s3.GetObjectInput{
		Bucket: aws.String(fixedBucketName),
		Key:    aws.String(fullObjectName),
	})

	if err != nil {
		return nil, "", fmt.Errorf("failed to download object: %w", err)
	}

	// Get the object to retrieve content type
	resp, err := r.client.HeadObject(&s3.HeadObjectInput{
		Bucket: aws.String(fixedBucketName),
		Key:    aws.String(fullObjectName),
	})

	if err != nil {
		return nil, "", fmt.Errorf("failed to get object metadata: %w", err)
	}

	// Get content type
	contentType := ""
	if resp.ContentType != nil {
		contentType = *resp.ContentType
	}

	return buf.Bytes(), contentType, nil
}

// Delete deletes an object from the specified bucket
func (r *awsS3Repository) Delete(ctx context.Context, bucketName, objectName string) error {
	// Use a fixed bucket name from the config
	fixedBucketName := "edunite-storage"

	// Create a folder structure based on the original bucket name
	folderPath := bucketName + "/"
	fullObjectName := folderPath + objectName

	_, err := r.client.DeleteObject(&s3.DeleteObjectInput{
		Bucket: aws.String(fixedBucketName),
		Key:    aws.String(fullObjectName),
	})
	if err != nil {
		return fmt.Errorf("failed to remove object: %w", err)
	}
	return nil
}
