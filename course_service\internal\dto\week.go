package dto

import "time"

// «плоская» строка из LEFT JOIN – удобно сканировать QueryRow/Query
type WeekAssignmentSubmission struct {
	// week
	WeekID      int64
	ThreadID    int64
	WeekNumber  int
	Type        string
	WeekTitle   string
	WeekDescr   string
	WeekCreated time.Time
	WeekUpdated time.Time

	// assignment (может быть NULL → sql.NullX)
	AssID        *int64
	AssTitle     *string
	AssDescr     *string
	AssDueDate   *time.Time
	AssMaxPoints *int
	AssType      *string

	// submission (NULL‑able)
	SubID        *int64
	SubScore     *int32
	SubComment   *string
	SubFeedback  *string
	SubFileURLs  []string
	SubSubmitted *time.Time
	SubCreated   *time.Time
	SubUpdated   *time.Time
}
