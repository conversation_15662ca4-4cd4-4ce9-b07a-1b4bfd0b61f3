-- Create sport schedules table
CREATE TABLE IF NOT EXISTS sport_schedules (
    id BIGSERIAL PRIMARY KEY,
    facility_id BIGINT NOT NULL REFERENCES facilities(id) ON DELETE CASCADE,
    teacher_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    semester_id BIGINT NOT NULL REFERENCES semesters(id) ON DELETE CASCADE,
    sport_type_id BIGINT NOT NULL REFERENCES sport_types(id) ON DELETE CASCADE,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    cancellation_deadline TIMESTAMP NOT NULL, -- 20:00 the day before
    location VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INT NOT NULL DEFAULT 1
);

-- Create trigger for updated_at
CREATE TRIGGER trg_sport_schedules_updated
    BEFORE UPDATE ON sport_schedules
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();
