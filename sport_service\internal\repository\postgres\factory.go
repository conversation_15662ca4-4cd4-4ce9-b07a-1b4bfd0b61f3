package postgres

import (
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/sport_service/internal/repository"
)

// NewRepositories creates all repositories
func NewRepositories(db *pgxpool.Pool) *Repositories {
	return &Repositories{
		SportType:          NewSportTypeRepository(db),
		TeacherSportType:   NewTeacherSportTypeRepository(db),
		Facility:           NewFacilityRepository(db),
		Schedule:           NewScheduleRepository(db),
		Booking:            NewBookingRepository(db),
		MedicalCertificate: NewMedicalCertificateRepository(db),
		SemesterSportLimit: NewSemesterSportLimitRepository(db),
		DailyBookingLimit:  NewDailyBookingLimitRepository(db),
	}
}

// Repositories holds all repository implementations
type Repositories struct {
	SportType          repository.SportTypeRepository
	TeacherSportType   repository.TeacherSportTypeRepository
	Facility           repository.FacilityRepository
	Schedule           repository.ScheduleRepository
	Booking            repository.BookingRepository
	MedicalCertificate repository.MedicalCertificateRepository
	SemesterSportLimit repository.SemesterSportLimitRepository
	DailyBookingLimit  repository.DailyBookingLimitRepository
}
