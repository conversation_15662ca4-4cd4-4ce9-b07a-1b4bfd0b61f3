package handlers

import (
	threadpb "github.com/olzzhas/edunite-server/course_service/pb/thread"
)

type threadHTTP struct {
	*threadpb.ThreadWithDetails `json:",inline"`                   // «Вклеить» все существующие поля
	Teacher                     *teacherDTO                        `json:"teacher,omitempty"`
	Schedules                   []*threadpb.ThreadScheduleResponse `json:"schedules,omitempty"`
}

type teacherDTO struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	Surname string `json:"surname"`
	Email   string `json:"email"`
}
