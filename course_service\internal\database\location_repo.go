package database

import (
	"context"
	"errors"
	"time"

	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
)

var (
	ErrLocationNotFound = errors.New("location not found")
	ErrLocationConflict = errors.New("location already exists with this name")
)

// Location model for the locations table
type Location struct {
	ID          int64     `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Capacity    int       `json:"capacity"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// LocationRepository interface for CRUD operations on locations
type LocationRepository interface {
	Create(ctx context.Context, l *Location) error
	GetByID(ctx context.Context, id int64) (*Location, error)
	GetByName(ctx context.Context, name string) (*Location, error)
	List(ctx context.Context) ([]*Location, error)
	Update(ctx context.Context, l *Location) error
	Delete(ctx context.Context, id int64) error
	// Check if a location is available during a specific time slot
	IsLocationAvailable(ctx context.Context, locationID int64, day int, 
		start, end time.Time, excludeScheduleID int64) (bool, error)
}

// locationRepository implementation of LocationRepository
type locationRepository struct {
	db *pgxpool.Pool
}

// NewLocationRepository constructor
func NewLocationRepository(db *pgxpool.Pool) LocationRepository {
	return &locationRepository{db: db}
}

// Create creates a new location
func (r *locationRepository) Create(ctx context.Context, l *Location) error {
	const q = `
	INSERT INTO locations (name, description, capacity)
	VALUES ($1, $2, $3)
	RETURNING id, created_at, updated_at`
	
	row := r.db.QueryRow(ctx, q, l.Name, l.Description, l.Capacity)
	return row.Scan(&l.ID, &l.CreatedAt, &l.UpdatedAt)
}

// GetByID retrieves a location by ID
func (r *locationRepository) GetByID(ctx context.Context, id int64) (*Location, error) {
	const q = `
	SELECT id, name, description, capacity, created_at, updated_at
	FROM locations
	WHERE id = $1`
	
	var l Location
	if err := r.db.QueryRow(ctx, q, id).Scan(
		&l.ID, &l.Name, &l.Description, &l.Capacity, &l.CreatedAt, &l.UpdatedAt); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, ErrLocationNotFound
		}
		return nil, err
	}
	return &l, nil
}

// GetByName retrieves a location by name
func (r *locationRepository) GetByName(ctx context.Context, name string) (*Location, error) {
	const q = `
	SELECT id, name, description, capacity, created_at, updated_at
	FROM locations
	WHERE name = $1`
	
	var l Location
	if err := r.db.QueryRow(ctx, q, name).Scan(
		&l.ID, &l.Name, &l.Description, &l.Capacity, &l.CreatedAt, &l.UpdatedAt); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, ErrLocationNotFound
		}
		return nil, err
	}
	return &l, nil
}

// List retrieves all locations
func (r *locationRepository) List(ctx context.Context) ([]*Location, error) {
	const q = `
	SELECT id, name, description, capacity, created_at, updated_at
	FROM locations
	ORDER BY name`
	
	rows, err := r.db.Query(ctx, q)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var locations []*Location
	for rows.Next() {
		var l Location
		if err := rows.Scan(
			&l.ID, &l.Name, &l.Description, &l.Capacity, &l.CreatedAt, &l.UpdatedAt); err != nil {
			return nil, err
		}
		locations = append(locations, &l)
	}
	
	return locations, rows.Err()
}

// Update updates an existing location
func (r *locationRepository) Update(ctx context.Context, l *Location) error {
	const q = `
	UPDATE locations
	SET name = $1, description = $2, capacity = $3, updated_at = NOW()
	WHERE id = $4
	RETURNING updated_at`
	
	return r.db.QueryRow(ctx, q, l.Name, l.Description, l.Capacity, l.ID).Scan(&l.UpdatedAt)
}

// Delete deletes a location
func (r *locationRepository) Delete(ctx context.Context, id int64) error {
	cmd, err := r.db.Exec(ctx, `DELETE FROM locations WHERE id = $1`, id)
	if err != nil {
		return err
	}
	if cmd.RowsAffected() == 0 {
		return ErrLocationNotFound
	}
	return nil
}

// IsLocationAvailable checks if a location is available during a specific time slot
func (r *locationRepository) IsLocationAvailable(ctx context.Context, locationID int64, day int, 
	start, end time.Time, excludeScheduleID int64) (bool, error) {
	
	const q = `
	SELECT 1
	FROM thread_schedules
	WHERE location_id = $1
	AND day_of_week = $2
	AND start_time < $4            -- newEnd
	AND end_time > $3              -- newStart
	AND ($5 = 0 OR id <> $5)
	LIMIT 1`
	
	var dummy int
	err := r.db.QueryRow(ctx, q,
		locationID, day, timeOnly(start), timeOnly(end), excludeScheduleID).Scan(&dummy)
	
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return true, nil // Location is available
		}
		return false, err
	}
	
	return false, nil // Location is not available
}
