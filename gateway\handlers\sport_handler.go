package handlers

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
	sportpb "github.com/olzzhas/edunite-server/sport_service/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// SportHandler handles HTTP requests for sport services
type SportHandler struct {
	RabbitLogPublisher       clients.LogPublisher
	SportTypeClient          *clients.SportTypeClient
	FacilityClient           *clients.FacilityClient
	ScheduleClient           *clients.ScheduleClient
	BookingClient            *clients.BookingClient
	MedicalCertificateClient *clients.MedicalCertificateClient
	SemesterLimitClient      *clients.SemesterLimitClient
	PhysicalEducationClient  *clients.PhysicalEducationClient
}

// GetSchedule handles the request to get a sport schedule by ID
func (h *SportHandler) GetSchedule(c *gin.Context) {
	// Parse and validate the schedule ID from the URL parameter
	scheduleIDStr := c.Param("id")
	scheduleID, err := strconv.ParseInt(scheduleIDStr, 10, 64)
	if err != nil || scheduleID <= 0 {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Invalid schedule ID format: %s", scheduleIDStr),
			"sport_schedule",
			map[string]any{"error": "invalid schedule_id"},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid schedule_id"})
		return
	}

	// Call the schedule service to get the schedule by ID
	schedule, err := h.ScheduleClient.GetSchedule(c.Request.Context(), scheduleID)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error fetching schedule with ID %d: %v", scheduleID, err),
			"sport_schedule",
			map[string]any{"schedule_id": scheduleID, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to fetch schedule"})
		return
	}

	// Return the schedule data
	c.JSON(http.StatusOK, gin.H{"schedule": schedule})
}

// ListSchedules handles the request to list sport schedules with filtering
func (h *SportHandler) ListSchedules(c *gin.Context) {
	// Parse query parameters
	facilityIDStr := c.Query("facility_id")
	teacherIDStr := c.Query("teacher_id")
	semesterIDStr := c.Query("semester_id")
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	location := c.Query("location")
	pageStr := c.Query("page")
	pageSizeStr := c.Query("page_size")

	// Set default values for pagination
	page := int32(1)
	pageSize := int32(20)

	// Parse facility_id if provided
	var facilityID int64
	if facilityIDStr != "" {
		var err error
		facilityID, err = strconv.ParseInt(facilityIDStr, 10, 64)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Invalid facility_id format: %s", facilityIDStr),
				"sport_schedule",
				map[string]any{"error": "invalid facility_id"},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid facility_id"})
			return
		}
	}

	// Parse teacher_id if provided
	var teacherID int64
	if teacherIDStr != "" {
		var err error
		teacherID, err = strconv.ParseInt(teacherIDStr, 10, 64)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Invalid teacher_id format: %s", teacherIDStr),
				"sport_schedule",
				map[string]any{"error": "invalid teacher_id"},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid teacher_id"})
			return
		}
	}

	// Parse semester_id if provided
	var semesterID int64
	if semesterIDStr != "" {
		var err error
		semesterID, err = strconv.ParseInt(semesterIDStr, 10, 64)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Invalid semester_id format: %s", semesterIDStr),
				"sport_schedule",
				map[string]any{"error": "invalid semester_id"},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid semester_id"})
			return
		}
	}

	// Parse page if provided
	if pageStr != "" {
		pageInt, err := strconv.ParseInt(pageStr, 10, 32)
		if err != nil || pageInt < 1 {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Invalid page format: %s", pageStr),
				"sport_schedule",
				map[string]any{"error": "invalid page"},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid page"})
			return
		}
		page = int32(pageInt)
	}

	// Parse page_size if provided
	if pageSizeStr != "" {
		pageSizeInt, err := strconv.ParseInt(pageSizeStr, 10, 32)
		if err != nil || pageSizeInt < 1 || pageSizeInt > 100 {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Invalid page_size format: %s", pageSizeStr),
				"sport_schedule",
				map[string]any{"error": "invalid page_size"},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid page_size"})
			return
		}
		pageSize = int32(pageSizeInt)
	}

	// Parse start_date if provided
	var startDate *timestamppb.Timestamp
	if startDateStr != "" {
		parsedStartDate, err := time.Parse(time.RFC3339, startDateStr)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Invalid start_date format: %s", startDateStr),
				"sport_schedule",
				map[string]any{"error": "invalid start_date"},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid start_date format, use RFC3339"})
			return
		}
		startDate = timestamppb.New(parsedStartDate)
	}

	// Parse end_date if provided
	var endDate *timestamppb.Timestamp
	if endDateStr != "" {
		parsedEndDate, err := time.Parse(time.RFC3339, endDateStr)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Invalid end_date format: %s", endDateStr),
				"sport_schedule",
				map[string]any{"error": "invalid end_date"},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid end_date format, use RFC3339"})
			return
		}
		endDate = timestamppb.New(parsedEndDate)
	}

	// Call the schedule service to list schedules with filtering
	schedules, err := h.ScheduleClient.ListSchedules(
		c.Request.Context(),
		facilityID,
		teacherID,
		semesterID,
		startDate,
		endDate,
		location,
		page,
		pageSize,
	)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error listing schedules: %v", err),
			"sport_schedule",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list schedules"})
		return
	}

	// Return the schedules data
	c.JSON(http.StatusOK, schedules)
}

// SportTypeRequest is a struct for binding sport type request data
type SportTypeRequest struct {
	Title               string                    `json:"title" binding:"required"`
	Description         string                    `json:"description"`
	Category            sportpb.SportTypeCategory `json:"category"`
	RequiresCertificate bool                      `json:"requires_certificate"`
}

// FacilityRequest is a struct for binding facility request data
type FacilityRequest struct {
	Name     string `json:"name" binding:"required"`
	Address  string `json:"address" binding:"required"`
	Capacity int32  `json:"capacity" binding:"required"`
}

// CreateSportType creates a new sport type
func (h *SportHandler) CreateSportType(c *gin.Context) {
	var req SportTypeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing CreateSportType request: %v", err),
			"sport",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	resp, err := h.SportTypeClient.CreateSportType(
		c.Request.Context(),
		req.Title,
		req.Description,
		req.Category,
		req.RequiresCertificate,
	)
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error creating sport type: %v", err),
			"sport",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

// GetSportType gets a sport type by ID
func (h *SportHandler) GetSportType(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid id"})
		return
	}

	resp, err := h.SportTypeClient.GetSportType(c.Request.Context(), id)
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting sport type: %v", err),
			"sport",
			map[string]any{"error": err.Error(), "id": id},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// UpdateSportType updates a sport type
func (h *SportHandler) UpdateSportType(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid id"})
		return
	}

	var req SportTypeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing UpdateSportType request: %v", err),
			"sport",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	resp, err := h.SportTypeClient.UpdateSportType(
		c.Request.Context(),
		id,
		req.Title,
		req.Description,
		req.Category,
		req.RequiresCertificate,
	)
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error updating sport type: %v", err),
			"sport",
			map[string]any{"error": err.Error(), "id": id},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// DeleteSportType deletes a sport type
func (h *SportHandler) DeleteSportType(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid id"})
		return
	}

	err = h.SportTypeClient.DeleteSportType(c.Request.Context(), id)
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error deleting sport type: %v", err),
			"sport",
			map[string]any{"error": err.Error(), "id": id},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// ListSportTypes lists all sport types
func (h *SportHandler) ListSportTypes(c *gin.Context) {
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "10")

	page, err := strconv.ParseInt(pageStr, 10, 32)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 32)
	if err != nil || pageSize < 1 {
		pageSize = 10
	}

	resp, err := h.SportTypeClient.ListSportTypes(c.Request.Context(), int32(page), int32(pageSize))
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error listing sport types: %v", err),
			"sport",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// CreateFacility creates a new facility
func (h *SportHandler) CreateFacility(c *gin.Context) {
	var req FacilityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing CreateFacility request: %v", err),
			"sport",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	resp, err := h.FacilityClient.CreateFacility(
		c.Request.Context(),
		req.Name,
		req.Address,
		req.Capacity,
	)
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error creating facility: %v", err),
			"sport",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

// GetFacility gets a facility by ID
func (h *SportHandler) GetFacility(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid id"})
		return
	}

	resp, err := h.FacilityClient.GetFacility(c.Request.Context(), id)
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting facility: %v", err),
			"sport",
			map[string]any{"error": err.Error(), "id": id},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// UpdateFacility updates a facility
func (h *SportHandler) UpdateFacility(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid id"})
		return
	}

	var req FacilityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing UpdateFacility request: %v", err),
			"sport",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	resp, err := h.FacilityClient.UpdateFacility(
		c.Request.Context(),
		id,
		req.Name,
		req.Address,
		req.Capacity,
	)
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error updating facility: %v", err),
			"sport",
			map[string]any{"error": err.Error(), "id": id},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// DeleteFacility deletes a facility
func (h *SportHandler) DeleteFacility(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid id"})
		return
	}

	err = h.FacilityClient.DeleteFacility(c.Request.Context(), id)
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error deleting facility: %v", err),
			"sport",
			map[string]any{"error": err.Error(), "id": id},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// ListFacilities lists all facilities
func (h *SportHandler) ListFacilities(c *gin.Context) {
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "10")

	page, err := strconv.ParseInt(pageStr, 10, 32)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 32)
	if err != nil || pageSize < 1 {
		pageSize = 10
	}

	resp, err := h.FacilityClient.ListFacilities(c.Request.Context(), int32(page), int32(pageSize))
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error listing facilities: %v", err),
			"sport",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetAvailableSportTypes gets available sport types for physical education
func (h *SportHandler) GetAvailableSportTypes(c *gin.Context) {
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "10")

	page, err := strconv.ParseInt(pageStr, 10, 32)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 32)
	if err != nil || pageSize < 1 {
		pageSize = 10
	}

	resp, err := h.PhysicalEducationClient.GetAvailableSportTypes(c.Request.Context(), int32(page), int32(pageSize))
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting available sport types: %v", err),
			"sport",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetAvailableFacilities gets available facilities for physical education
func (h *SportHandler) GetAvailableFacilities(c *gin.Context) {
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "10")

	page, err := strconv.ParseInt(pageStr, 10, 32)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.ParseInt(pageSizeStr, 10, 32)
	if err != nil || pageSize < 1 {
		pageSize = 10
	}

	resp, err := h.PhysicalEducationClient.GetAvailableFacilities(c.Request.Context(), int32(page), int32(pageSize))
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting available facilities: %v", err),
			"sport",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// BookSession books a session for physical education
func (h *SportHandler) BookSession(c *gin.Context) {
	var req struct {
		UserID     int64 `json:"user_id" binding:"required"`
		ScheduleID int64 `json:"schedule_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing BookSession request: %v", err),
			"sport",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	resp, err := h.PhysicalEducationClient.BookSession(c.Request.Context(), req.UserID, req.ScheduleID)
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error booking session: %v", err),
			"sport",
			map[string]any{"error": err.Error(), "user_id": req.UserID, "schedule_id": req.ScheduleID},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

// CancelBooking cancels a booking for physical education
func (h *SportHandler) CancelBooking(c *gin.Context) {
	bookingIdParam := c.Param("booking_id")
	bookingId, err := strconv.ParseInt(bookingIdParam, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid booking id"})
		return
	}

	userIdParam := c.Param("user_id")
	userId, err := strconv.ParseInt(userIdParam, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid user id"})
		return
	}

	resp, err := h.PhysicalEducationClient.CancelBooking(c.Request.Context(), bookingId, userId)
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error canceling booking: %v", err),
			"sport",
			map[string]any{"error": err.Error(), "booking_id": bookingId, "user_id": userId},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// WeeklyScheduleRequest is a struct for binding weekly schedule request data
type WeeklyScheduleRequest struct {
	FacilityID  int64  `json:"facility_id" binding:"required"`
	TeacherID   int64  `json:"teacher_id" binding:"required"`
	SemesterID  int64  `json:"semester_id" binding:"required"`
	SportTypeID int64  `json:"sport_type_id" binding:"required"`           // Added sport_type_id field
	DayOfWeek   int32  `json:"day_of_week" binding:"required,min=1,max=7"` // 1=Monday, 7=Sunday
	StartTime   string `json:"start_time" binding:"required"`              // Format: "HH:MM:SS"
	EndTime     string `json:"end_time" binding:"required"`                // Format: "HH:MM:SS"
	Location    string `json:"location" binding:"required"`
	StartDate   string `json:"start_date" binding:"required"` // Format: RFC3339
	EndDate     string `json:"end_date" binding:"required"`   // Format: RFC3339
}

// CreateWeeklySchedules creates schedules from a weekly template
func (h *SportHandler) CreateWeeklySchedules(c *gin.Context) {
	// Read the raw request body for logging
	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error reading request body: %v", err),
			"sport",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to read request body"})
		return
	}

	// Log the request body
	_ = h.RabbitLogPublisher.PublishLog(
		"info",
		"Received CreateWeeklySchedules request",
		"sport",
		map[string]any{"request_body": string(bodyBytes)},
	)

	// Restore the request body for binding
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	var req WeeklyScheduleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing CreateWeeklySchedules request: %v", err),
			"sport",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// Parse dates
	startDate, err := time.Parse(time.RFC3339, req.StartDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid start_date format, use RFC3339"})
		return
	}

	endDate, err := time.Parse(time.RFC3339, req.EndDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid end_date format, use RFC3339"})
		return
	}

	// Convert to protobuf timestamps
	startTimestamp := timestamppb.New(startDate)
	endTimestamp := timestamppb.New(endDate)

	resp, err := h.ScheduleClient.CreateWeeklySchedules(
		c.Request.Context(),
		req.FacilityID,
		req.TeacherID,
		req.SemesterID,
		req.SportTypeID, // Added SportTypeID
		req.DayOfWeek,
		req.StartTime,
		req.EndTime,
		req.Location,
		startTimestamp,
		endTimestamp,
	)
	if err != nil {
		errorMsg := err.Error()
		statusCode := http.StatusInternalServerError

		// Check if it's a conflict error
		if strings.Contains(errorMsg, "conflicts with existing schedules") {
			errorMsg = "Schedule conflicts with existing schedules: teacher or facility is already booked for this time"
			statusCode = http.StatusConflict
		}

		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error creating weekly schedules: %v", err),
			"sport",
			map[string]any{
				"error":         errorMsg,
				"status_code":   statusCode,
				"facility_id":   req.FacilityID,
				"teacher_id":    req.TeacherID,
				"semester_id":   req.SemesterID,
				"sport_type_id": req.SportTypeID,
				"day_of_week":   req.DayOfWeek,
				"start_time":    req.StartTime,
				"end_time":      req.EndTime,
				"location":      req.Location,
			},
		)
		c.JSON(statusCode, gin.H{"error": errorMsg})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

// SportSchedulePattern represents a pattern for creating schedules for a sport type
type SportSchedulePattern struct {
	SportTypeID int64                   `json:"sport_type_id" binding:"required"`
	Schedules   []WeeklyScheduleRequest `json:"schedules"`
}

// CreateSportSchedulesRequest is a struct for binding multiple sport schedule patterns
type CreateSportSchedulesRequest struct {
	SemesterID int64                  `json:"semester_id" binding:"required"`
	StartDate  string                 `json:"start_date" binding:"required"` // Format: RFC3339
	EndDate    string                 `json:"end_date" binding:"required"`   // Format: RFC3339
	Patterns   []SportSchedulePattern `json:"patterns"`
}

// CreateSportSchedules creates schedules for multiple sport types based on patterns
func (h *SportHandler) CreateSportSchedules(c *gin.Context) {
	// Read the raw request body for logging
	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error reading request body: %v", err),
			"sport",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to read request body"})
		return
	}

	// Log the request body
	_ = h.RabbitLogPublisher.PublishLog(
		"info",
		"Received CreateSportSchedules request",
		"sport",
		map[string]any{"request_body": string(bodyBytes)},
	)

	// Restore the request body for binding
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	var req CreateSportSchedulesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		_ = h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing CreateSportSchedules request: %v", err),
			"sport",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// Check if patterns array is empty
	if len(req.Patterns) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "patterns array cannot be empty"})
		return
	}

	// Parse dates
	startDate, err := time.Parse(time.RFC3339, req.StartDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid start_date format, use RFC3339"})
		return
	}

	endDate, err := time.Parse(time.RFC3339, req.EndDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid end_date format, use RFC3339"})
		return
	}

	// Convert to protobuf timestamps
	startTimestamp := timestamppb.New(startDate)
	endTimestamp := timestamppb.New(endDate)

	// Process each pattern
	var allResponses []*sportpb.CreateWeeklySchedulesResponse
	var errors []string

	for patternIndex, pattern := range req.Patterns {
		// Check if schedules array is empty
		if len(pattern.Schedules) == 0 {
			errors = append(errors, fmt.Sprintf("Schedules array for sport type %d cannot be empty", pattern.SportTypeID))
			continue
		}

		for scheduleIndex := range pattern.Schedules {
			// Get a reference to the schedule so we can modify it
			schedule := &req.Patterns[patternIndex].Schedules[scheduleIndex]

			// Override semester ID with the one from the request
			schedule.SemesterID = req.SemesterID

			// Set the sport type ID from the pattern
			schedule.SportTypeID = pattern.SportTypeID

			resp, err := h.ScheduleClient.CreateWeeklySchedules(
				c.Request.Context(),
				schedule.FacilityID,
				schedule.TeacherID,
				schedule.SemesterID,  // Use the schedule's SemesterID which we just set
				schedule.SportTypeID, // Use the sport type ID from the pattern
				schedule.DayOfWeek,
				schedule.StartTime,
				schedule.EndTime,
				schedule.Location,
				startTimestamp,
				endTimestamp,
			)

			if err != nil {
				errorMsg := err.Error()
				statusCode := http.StatusInternalServerError

				// Check if it's a conflict error
				if strings.Contains(errorMsg, "conflicts with existing schedules") {
					errorMsg = fmt.Sprintf("Schedule for sport type %d conflicts with existing schedules: teacher or facility is already booked for this time", pattern.SportTypeID)
					statusCode = http.StatusConflict
				}

				_ = h.RabbitLogPublisher.PublishLog(
					"error",
					fmt.Sprintf("Error creating weekly schedules for sport type %d: %v", pattern.SportTypeID, err),
					"sport",
					map[string]any{
						"error":         errorMsg,
						"status_code":   statusCode,
						"sport_type_id": pattern.SportTypeID,
						"facility_id":   schedule.FacilityID,
						"teacher_id":    schedule.TeacherID,
						"semester_id":   schedule.SemesterID,
						"day_of_week":   schedule.DayOfWeek,
						"start_time":    schedule.StartTime,
						"end_time":      schedule.EndTime,
						"location":      schedule.Location,
					},
				)
				errors = append(errors, errorMsg)
				continue
			}

			allResponses = append(allResponses, resp)
		}
	}

	if len(errors) > 0 {
		c.JSON(http.StatusPartialContent, gin.H{
			"message": "Some schedules could not be created",
			"errors":  errors,
			"created": allResponses,
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":   "All schedules created successfully",
		"schedules": allResponses,
	})
}
