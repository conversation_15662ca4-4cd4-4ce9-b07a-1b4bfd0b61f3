// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.2
// source: pb/thread/thread.proto

package threadpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ThreadService_CreateThread_FullMethodName                    = "/threadpb.ThreadService/CreateThread"
	ThreadService_ListThreadsByCourse_FullMethodName             = "/threadpb.ThreadService/ListThreadsByCourse"
	ThreadService_ListThreads_FullMethodName                     = "/threadpb.ThreadService/ListThreads"
	ThreadService_GetThreadByID_FullMethodName                   = "/threadpb.ThreadService/GetThreadByID"
	ThreadService_UpdateThreadByID_FullMethodName                = "/threadpb.ThreadService/UpdateThreadByID"
	ThreadService_DeleteThreadByID_FullMethodName                = "/threadpb.ThreadService/DeleteThreadByID"
	ThreadService_RegisterUserToThread_FullMethodName            = "/threadpb.ThreadService/RegisterUserToThread"
	ThreadService_RegisterManyUsersToThread_FullMethodName       = "/threadpb.ThreadService/RegisterManyUsersToThread"
	ThreadService_RemoveRegistrationToThread_FullMethodName      = "/threadpb.ThreadService/RemoveRegistrationToThread"
	ThreadService_RemoveManyRegistrationsToThread_FullMethodName = "/threadpb.ThreadService/RemoveManyRegistrationsToThread"
	ThreadService_ListThreadRegistrations_FullMethodName         = "/threadpb.ThreadService/ListThreadRegistrations"
	ThreadService_CreateWeek_FullMethodName                      = "/threadpb.ThreadService/CreateWeek"
	ThreadService_ListWeeksForThread_FullMethodName              = "/threadpb.ThreadService/ListWeeksForThread"
	ThreadService_GetWeekByID_FullMethodName                     = "/threadpb.ThreadService/GetWeekByID"
	ThreadService_UpdateWeek_FullMethodName                      = "/threadpb.ThreadService/UpdateWeek"
	ThreadService_DeleteWeekByID_FullMethodName                  = "/threadpb.ThreadService/DeleteWeekByID"
	ThreadService_CreateThreadSchedule_FullMethodName            = "/threadpb.ThreadService/CreateThreadSchedule"
	ThreadService_ListThreadSchedules_FullMethodName             = "/threadpb.ThreadService/ListThreadSchedules"
	ThreadService_GetThreadScheduleByID_FullMethodName           = "/threadpb.ThreadService/GetThreadScheduleByID"
	ThreadService_UpdateThreadSchedule_FullMethodName            = "/threadpb.ThreadService/UpdateThreadSchedule"
	ThreadService_DeleteThreadScheduleByID_FullMethodName        = "/threadpb.ThreadService/DeleteThreadScheduleByID"
	ThreadService_CreateLocation_FullMethodName                  = "/threadpb.ThreadService/CreateLocation"
	ThreadService_GetLocationByID_FullMethodName                 = "/threadpb.ThreadService/GetLocationByID"
	ThreadService_ListLocations_FullMethodName                   = "/threadpb.ThreadService/ListLocations"
	ThreadService_UpdateLocation_FullMethodName                  = "/threadpb.ThreadService/UpdateLocation"
	ThreadService_DeleteLocation_FullMethodName                  = "/threadpb.ThreadService/DeleteLocation"
	ThreadService_CheckLocationAvailability_FullMethodName       = "/threadpb.ThreadService/CheckLocationAvailability"
	ThreadService_ListThreadsForUser_FullMethodName              = "/threadpb.ThreadService/ListThreadsForUser"
	ThreadService_ListWeeksWithHomework_FullMethodName           = "/threadpb.ThreadService/ListWeeksWithHomework"
	ThreadService_CheckPrerequisites_FullMethodName              = "/threadpb.ThreadService/CheckPrerequisites"
)

// ThreadServiceClient is the client API for ThreadService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ========================================================
//
//	SERVICE
//
// ========================================================
type ThreadServiceClient interface {
	// -------- Thread ------------------------------------------------------
	CreateThread(ctx context.Context, in *ThreadRequest, opts ...grpc.CallOption) (*ThreadResponse, error)
	ListThreadsByCourse(ctx context.Context, in *ThreadsByCourseRequest, opts ...grpc.CallOption) (*ThreadsResponse, error)
	ListThreads(ctx context.Context, in *ThreadEmptyRequest, opts ...grpc.CallOption) (*ThreadsResponse, error)
	GetThreadByID(ctx context.Context, in *ThreadByID, opts ...grpc.CallOption) (*ThreadResponse, error)
	UpdateThreadByID(ctx context.Context, in *ThreadUpdateRequest, opts ...grpc.CallOption) (*ThreadResponse, error)
	DeleteThreadByID(ctx context.Context, in *ThreadByID, opts ...grpc.CallOption) (*ThreadEmptyResponse, error)
	// -------- Registrations ----------------------------------------------
	RegisterUserToThread(ctx context.Context, in *RegisterUserToThreadRequest, opts ...grpc.CallOption) (*ThreadEmptyResponse, error)
	RegisterManyUsersToThread(ctx context.Context, in *RegisterManyUsersToThreadRequest, opts ...grpc.CallOption) (*ThreadEmptyResponse, error)
	RemoveRegistrationToThread(ctx context.Context, in *RemoveRegistrationToThreadRequest, opts ...grpc.CallOption) (*ThreadEmptyResponse, error)
	RemoveManyRegistrationsToThread(ctx context.Context, in *RemoveManyRegistrationsToThreadRequest, opts ...grpc.CallOption) (*ThreadEmptyResponse, error)
	ListThreadRegistrations(ctx context.Context, in *ThreadByID, opts ...grpc.CallOption) (*ListThreadRegistrationsResponse, error)
	// -------- Week --------------------------------------------------------
	CreateWeek(ctx context.Context, in *WeekRequest, opts ...grpc.CallOption) (*WeekResponse, error)
	ListWeeksForThread(ctx context.Context, in *WeeksForThreadRequest, opts ...grpc.CallOption) (*WeeksResponse, error)
	GetWeekByID(ctx context.Context, in *WeekByID, opts ...grpc.CallOption) (*WeekResponse, error)
	UpdateWeek(ctx context.Context, in *WeekUpdateRequest, opts ...grpc.CallOption) (*WeekResponse, error)
	DeleteWeekByID(ctx context.Context, in *WeekByID, opts ...grpc.CallOption) (*ThreadEmptyResponse, error)
	// -------- Schedule ----------------------------------------------------
	CreateThreadSchedule(ctx context.Context, in *ThreadScheduleRequest, opts ...grpc.CallOption) (*ThreadScheduleResponse, error)
	ListThreadSchedules(ctx context.Context, in *ThreadSchedulesRequest, opts ...grpc.CallOption) (*ThreadSchedulesResponse, error)
	GetThreadScheduleByID(ctx context.Context, in *ThreadScheduleByID, opts ...grpc.CallOption) (*ThreadScheduleResponse, error)
	UpdateThreadSchedule(ctx context.Context, in *ThreadScheduleUpdateRequest, opts ...grpc.CallOption) (*ThreadScheduleResponse, error)
	DeleteThreadScheduleByID(ctx context.Context, in *ThreadScheduleByID, opts ...grpc.CallOption) (*ThreadEmptyResponse, error)
	// -------- Location -----------------------------------------------------
	CreateLocation(ctx context.Context, in *LocationRequest, opts ...grpc.CallOption) (*LocationResponse, error)
	GetLocationByID(ctx context.Context, in *LocationByID, opts ...grpc.CallOption) (*LocationResponse, error)
	ListLocations(ctx context.Context, in *LocationEmptyRequest, opts ...grpc.CallOption) (*LocationsResponse, error)
	UpdateLocation(ctx context.Context, in *LocationUpdateRequest, opts ...grpc.CallOption) (*LocationResponse, error)
	DeleteLocation(ctx context.Context, in *LocationByID, opts ...grpc.CallOption) (*ThreadEmptyResponse, error)
	CheckLocationAvailability(ctx context.Context, in *LocationAvailabilityRequest, opts ...grpc.CallOption) (*LocationAvailabilityResponse, error)
	// -------- For user ----------------------------------------------------
	ListThreadsForUser(ctx context.Context, in *UserThreadsRequest, opts ...grpc.CallOption) (*UserThreadsResponse, error)
	ListWeeksWithHomework(ctx context.Context, in *WeeksWithHwRequest, opts ...grpc.CallOption) (*WeeksWithHwResponse, error)
	// -------- Prerequisites ------------------------------------------------
	CheckPrerequisites(ctx context.Context, in *CheckPrerequisitesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type threadServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewThreadServiceClient(cc grpc.ClientConnInterface) ThreadServiceClient {
	return &threadServiceClient{cc}
}

func (c *threadServiceClient) CreateThread(ctx context.Context, in *ThreadRequest, opts ...grpc.CallOption) (*ThreadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadResponse)
	err := c.cc.Invoke(ctx, ThreadService_CreateThread_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) ListThreadsByCourse(ctx context.Context, in *ThreadsByCourseRequest, opts ...grpc.CallOption) (*ThreadsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadsResponse)
	err := c.cc.Invoke(ctx, ThreadService_ListThreadsByCourse_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) ListThreads(ctx context.Context, in *ThreadEmptyRequest, opts ...grpc.CallOption) (*ThreadsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadsResponse)
	err := c.cc.Invoke(ctx, ThreadService_ListThreads_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) GetThreadByID(ctx context.Context, in *ThreadByID, opts ...grpc.CallOption) (*ThreadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadResponse)
	err := c.cc.Invoke(ctx, ThreadService_GetThreadByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) UpdateThreadByID(ctx context.Context, in *ThreadUpdateRequest, opts ...grpc.CallOption) (*ThreadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadResponse)
	err := c.cc.Invoke(ctx, ThreadService_UpdateThreadByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) DeleteThreadByID(ctx context.Context, in *ThreadByID, opts ...grpc.CallOption) (*ThreadEmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadEmptyResponse)
	err := c.cc.Invoke(ctx, ThreadService_DeleteThreadByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) RegisterUserToThread(ctx context.Context, in *RegisterUserToThreadRequest, opts ...grpc.CallOption) (*ThreadEmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadEmptyResponse)
	err := c.cc.Invoke(ctx, ThreadService_RegisterUserToThread_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) RegisterManyUsersToThread(ctx context.Context, in *RegisterManyUsersToThreadRequest, opts ...grpc.CallOption) (*ThreadEmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadEmptyResponse)
	err := c.cc.Invoke(ctx, ThreadService_RegisterManyUsersToThread_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) RemoveRegistrationToThread(ctx context.Context, in *RemoveRegistrationToThreadRequest, opts ...grpc.CallOption) (*ThreadEmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadEmptyResponse)
	err := c.cc.Invoke(ctx, ThreadService_RemoveRegistrationToThread_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) RemoveManyRegistrationsToThread(ctx context.Context, in *RemoveManyRegistrationsToThreadRequest, opts ...grpc.CallOption) (*ThreadEmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadEmptyResponse)
	err := c.cc.Invoke(ctx, ThreadService_RemoveManyRegistrationsToThread_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) ListThreadRegistrations(ctx context.Context, in *ThreadByID, opts ...grpc.CallOption) (*ListThreadRegistrationsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListThreadRegistrationsResponse)
	err := c.cc.Invoke(ctx, ThreadService_ListThreadRegistrations_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) CreateWeek(ctx context.Context, in *WeekRequest, opts ...grpc.CallOption) (*WeekResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WeekResponse)
	err := c.cc.Invoke(ctx, ThreadService_CreateWeek_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) ListWeeksForThread(ctx context.Context, in *WeeksForThreadRequest, opts ...grpc.CallOption) (*WeeksResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WeeksResponse)
	err := c.cc.Invoke(ctx, ThreadService_ListWeeksForThread_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) GetWeekByID(ctx context.Context, in *WeekByID, opts ...grpc.CallOption) (*WeekResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WeekResponse)
	err := c.cc.Invoke(ctx, ThreadService_GetWeekByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) UpdateWeek(ctx context.Context, in *WeekUpdateRequest, opts ...grpc.CallOption) (*WeekResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WeekResponse)
	err := c.cc.Invoke(ctx, ThreadService_UpdateWeek_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) DeleteWeekByID(ctx context.Context, in *WeekByID, opts ...grpc.CallOption) (*ThreadEmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadEmptyResponse)
	err := c.cc.Invoke(ctx, ThreadService_DeleteWeekByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) CreateThreadSchedule(ctx context.Context, in *ThreadScheduleRequest, opts ...grpc.CallOption) (*ThreadScheduleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadScheduleResponse)
	err := c.cc.Invoke(ctx, ThreadService_CreateThreadSchedule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) ListThreadSchedules(ctx context.Context, in *ThreadSchedulesRequest, opts ...grpc.CallOption) (*ThreadSchedulesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadSchedulesResponse)
	err := c.cc.Invoke(ctx, ThreadService_ListThreadSchedules_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) GetThreadScheduleByID(ctx context.Context, in *ThreadScheduleByID, opts ...grpc.CallOption) (*ThreadScheduleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadScheduleResponse)
	err := c.cc.Invoke(ctx, ThreadService_GetThreadScheduleByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) UpdateThreadSchedule(ctx context.Context, in *ThreadScheduleUpdateRequest, opts ...grpc.CallOption) (*ThreadScheduleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadScheduleResponse)
	err := c.cc.Invoke(ctx, ThreadService_UpdateThreadSchedule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) DeleteThreadScheduleByID(ctx context.Context, in *ThreadScheduleByID, opts ...grpc.CallOption) (*ThreadEmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadEmptyResponse)
	err := c.cc.Invoke(ctx, ThreadService_DeleteThreadScheduleByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) CreateLocation(ctx context.Context, in *LocationRequest, opts ...grpc.CallOption) (*LocationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LocationResponse)
	err := c.cc.Invoke(ctx, ThreadService_CreateLocation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) GetLocationByID(ctx context.Context, in *LocationByID, opts ...grpc.CallOption) (*LocationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LocationResponse)
	err := c.cc.Invoke(ctx, ThreadService_GetLocationByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) ListLocations(ctx context.Context, in *LocationEmptyRequest, opts ...grpc.CallOption) (*LocationsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LocationsResponse)
	err := c.cc.Invoke(ctx, ThreadService_ListLocations_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) UpdateLocation(ctx context.Context, in *LocationUpdateRequest, opts ...grpc.CallOption) (*LocationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LocationResponse)
	err := c.cc.Invoke(ctx, ThreadService_UpdateLocation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) DeleteLocation(ctx context.Context, in *LocationByID, opts ...grpc.CallOption) (*ThreadEmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ThreadEmptyResponse)
	err := c.cc.Invoke(ctx, ThreadService_DeleteLocation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) CheckLocationAvailability(ctx context.Context, in *LocationAvailabilityRequest, opts ...grpc.CallOption) (*LocationAvailabilityResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LocationAvailabilityResponse)
	err := c.cc.Invoke(ctx, ThreadService_CheckLocationAvailability_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) ListThreadsForUser(ctx context.Context, in *UserThreadsRequest, opts ...grpc.CallOption) (*UserThreadsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserThreadsResponse)
	err := c.cc.Invoke(ctx, ThreadService_ListThreadsForUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) ListWeeksWithHomework(ctx context.Context, in *WeeksWithHwRequest, opts ...grpc.CallOption) (*WeeksWithHwResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WeeksWithHwResponse)
	err := c.cc.Invoke(ctx, ThreadService_ListWeeksWithHomework_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *threadServiceClient) CheckPrerequisites(ctx context.Context, in *CheckPrerequisitesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, ThreadService_CheckPrerequisites_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ThreadServiceServer is the server API for ThreadService service.
// All implementations must embed UnimplementedThreadServiceServer
// for forward compatibility.
//
// ========================================================
//
//	SERVICE
//
// ========================================================
type ThreadServiceServer interface {
	// -------- Thread ------------------------------------------------------
	CreateThread(context.Context, *ThreadRequest) (*ThreadResponse, error)
	ListThreadsByCourse(context.Context, *ThreadsByCourseRequest) (*ThreadsResponse, error)
	ListThreads(context.Context, *ThreadEmptyRequest) (*ThreadsResponse, error)
	GetThreadByID(context.Context, *ThreadByID) (*ThreadResponse, error)
	UpdateThreadByID(context.Context, *ThreadUpdateRequest) (*ThreadResponse, error)
	DeleteThreadByID(context.Context, *ThreadByID) (*ThreadEmptyResponse, error)
	// -------- Registrations ----------------------------------------------
	RegisterUserToThread(context.Context, *RegisterUserToThreadRequest) (*ThreadEmptyResponse, error)
	RegisterManyUsersToThread(context.Context, *RegisterManyUsersToThreadRequest) (*ThreadEmptyResponse, error)
	RemoveRegistrationToThread(context.Context, *RemoveRegistrationToThreadRequest) (*ThreadEmptyResponse, error)
	RemoveManyRegistrationsToThread(context.Context, *RemoveManyRegistrationsToThreadRequest) (*ThreadEmptyResponse, error)
	ListThreadRegistrations(context.Context, *ThreadByID) (*ListThreadRegistrationsResponse, error)
	// -------- Week --------------------------------------------------------
	CreateWeek(context.Context, *WeekRequest) (*WeekResponse, error)
	ListWeeksForThread(context.Context, *WeeksForThreadRequest) (*WeeksResponse, error)
	GetWeekByID(context.Context, *WeekByID) (*WeekResponse, error)
	UpdateWeek(context.Context, *WeekUpdateRequest) (*WeekResponse, error)
	DeleteWeekByID(context.Context, *WeekByID) (*ThreadEmptyResponse, error)
	// -------- Schedule ----------------------------------------------------
	CreateThreadSchedule(context.Context, *ThreadScheduleRequest) (*ThreadScheduleResponse, error)
	ListThreadSchedules(context.Context, *ThreadSchedulesRequest) (*ThreadSchedulesResponse, error)
	GetThreadScheduleByID(context.Context, *ThreadScheduleByID) (*ThreadScheduleResponse, error)
	UpdateThreadSchedule(context.Context, *ThreadScheduleUpdateRequest) (*ThreadScheduleResponse, error)
	DeleteThreadScheduleByID(context.Context, *ThreadScheduleByID) (*ThreadEmptyResponse, error)
	// -------- Location -----------------------------------------------------
	CreateLocation(context.Context, *LocationRequest) (*LocationResponse, error)
	GetLocationByID(context.Context, *LocationByID) (*LocationResponse, error)
	ListLocations(context.Context, *LocationEmptyRequest) (*LocationsResponse, error)
	UpdateLocation(context.Context, *LocationUpdateRequest) (*LocationResponse, error)
	DeleteLocation(context.Context, *LocationByID) (*ThreadEmptyResponse, error)
	CheckLocationAvailability(context.Context, *LocationAvailabilityRequest) (*LocationAvailabilityResponse, error)
	// -------- For user ----------------------------------------------------
	ListThreadsForUser(context.Context, *UserThreadsRequest) (*UserThreadsResponse, error)
	ListWeeksWithHomework(context.Context, *WeeksWithHwRequest) (*WeeksWithHwResponse, error)
	// -------- Prerequisites ------------------------------------------------
	CheckPrerequisites(context.Context, *CheckPrerequisitesRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedThreadServiceServer()
}

// UnimplementedThreadServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedThreadServiceServer struct{}

func (UnimplementedThreadServiceServer) CreateThread(context.Context, *ThreadRequest) (*ThreadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateThread not implemented")
}
func (UnimplementedThreadServiceServer) ListThreadsByCourse(context.Context, *ThreadsByCourseRequest) (*ThreadsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListThreadsByCourse not implemented")
}
func (UnimplementedThreadServiceServer) ListThreads(context.Context, *ThreadEmptyRequest) (*ThreadsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListThreads not implemented")
}
func (UnimplementedThreadServiceServer) GetThreadByID(context.Context, *ThreadByID) (*ThreadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetThreadByID not implemented")
}
func (UnimplementedThreadServiceServer) UpdateThreadByID(context.Context, *ThreadUpdateRequest) (*ThreadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateThreadByID not implemented")
}
func (UnimplementedThreadServiceServer) DeleteThreadByID(context.Context, *ThreadByID) (*ThreadEmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteThreadByID not implemented")
}
func (UnimplementedThreadServiceServer) RegisterUserToThread(context.Context, *RegisterUserToThreadRequest) (*ThreadEmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterUserToThread not implemented")
}
func (UnimplementedThreadServiceServer) RegisterManyUsersToThread(context.Context, *RegisterManyUsersToThreadRequest) (*ThreadEmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterManyUsersToThread not implemented")
}
func (UnimplementedThreadServiceServer) RemoveRegistrationToThread(context.Context, *RemoveRegistrationToThreadRequest) (*ThreadEmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveRegistrationToThread not implemented")
}
func (UnimplementedThreadServiceServer) RemoveManyRegistrationsToThread(context.Context, *RemoveManyRegistrationsToThreadRequest) (*ThreadEmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveManyRegistrationsToThread not implemented")
}
func (UnimplementedThreadServiceServer) ListThreadRegistrations(context.Context, *ThreadByID) (*ListThreadRegistrationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListThreadRegistrations not implemented")
}
func (UnimplementedThreadServiceServer) CreateWeek(context.Context, *WeekRequest) (*WeekResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWeek not implemented")
}
func (UnimplementedThreadServiceServer) ListWeeksForThread(context.Context, *WeeksForThreadRequest) (*WeeksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWeeksForThread not implemented")
}
func (UnimplementedThreadServiceServer) GetWeekByID(context.Context, *WeekByID) (*WeekResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWeekByID not implemented")
}
func (UnimplementedThreadServiceServer) UpdateWeek(context.Context, *WeekUpdateRequest) (*WeekResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWeek not implemented")
}
func (UnimplementedThreadServiceServer) DeleteWeekByID(context.Context, *WeekByID) (*ThreadEmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWeekByID not implemented")
}
func (UnimplementedThreadServiceServer) CreateThreadSchedule(context.Context, *ThreadScheduleRequest) (*ThreadScheduleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateThreadSchedule not implemented")
}
func (UnimplementedThreadServiceServer) ListThreadSchedules(context.Context, *ThreadSchedulesRequest) (*ThreadSchedulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListThreadSchedules not implemented")
}
func (UnimplementedThreadServiceServer) GetThreadScheduleByID(context.Context, *ThreadScheduleByID) (*ThreadScheduleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetThreadScheduleByID not implemented")
}
func (UnimplementedThreadServiceServer) UpdateThreadSchedule(context.Context, *ThreadScheduleUpdateRequest) (*ThreadScheduleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateThreadSchedule not implemented")
}
func (UnimplementedThreadServiceServer) DeleteThreadScheduleByID(context.Context, *ThreadScheduleByID) (*ThreadEmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteThreadScheduleByID not implemented")
}
func (UnimplementedThreadServiceServer) CreateLocation(context.Context, *LocationRequest) (*LocationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLocation not implemented")
}
func (UnimplementedThreadServiceServer) GetLocationByID(context.Context, *LocationByID) (*LocationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLocationByID not implemented")
}
func (UnimplementedThreadServiceServer) ListLocations(context.Context, *LocationEmptyRequest) (*LocationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLocations not implemented")
}
func (UnimplementedThreadServiceServer) UpdateLocation(context.Context, *LocationUpdateRequest) (*LocationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLocation not implemented")
}
func (UnimplementedThreadServiceServer) DeleteLocation(context.Context, *LocationByID) (*ThreadEmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLocation not implemented")
}
func (UnimplementedThreadServiceServer) CheckLocationAvailability(context.Context, *LocationAvailabilityRequest) (*LocationAvailabilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckLocationAvailability not implemented")
}
func (UnimplementedThreadServiceServer) ListThreadsForUser(context.Context, *UserThreadsRequest) (*UserThreadsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListThreadsForUser not implemented")
}
func (UnimplementedThreadServiceServer) ListWeeksWithHomework(context.Context, *WeeksWithHwRequest) (*WeeksWithHwResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWeeksWithHomework not implemented")
}
func (UnimplementedThreadServiceServer) CheckPrerequisites(context.Context, *CheckPrerequisitesRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPrerequisites not implemented")
}
func (UnimplementedThreadServiceServer) mustEmbedUnimplementedThreadServiceServer() {}
func (UnimplementedThreadServiceServer) testEmbeddedByValue()                       {}

// UnsafeThreadServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ThreadServiceServer will
// result in compilation errors.
type UnsafeThreadServiceServer interface {
	mustEmbedUnimplementedThreadServiceServer()
}

func RegisterThreadServiceServer(s grpc.ServiceRegistrar, srv ThreadServiceServer) {
	// If the following call pancis, it indicates UnimplementedThreadServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ThreadService_ServiceDesc, srv)
}

func _ThreadService_CreateThread_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).CreateThread(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_CreateThread_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).CreateThread(ctx, req.(*ThreadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_ListThreadsByCourse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreadsByCourseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).ListThreadsByCourse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_ListThreadsByCourse_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).ListThreadsByCourse(ctx, req.(*ThreadsByCourseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_ListThreads_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreadEmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).ListThreads(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_ListThreads_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).ListThreads(ctx, req.(*ThreadEmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_GetThreadByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreadByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).GetThreadByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_GetThreadByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).GetThreadByID(ctx, req.(*ThreadByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_UpdateThreadByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreadUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).UpdateThreadByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_UpdateThreadByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).UpdateThreadByID(ctx, req.(*ThreadUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_DeleteThreadByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreadByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).DeleteThreadByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_DeleteThreadByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).DeleteThreadByID(ctx, req.(*ThreadByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_RegisterUserToThread_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterUserToThreadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).RegisterUserToThread(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_RegisterUserToThread_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).RegisterUserToThread(ctx, req.(*RegisterUserToThreadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_RegisterManyUsersToThread_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterManyUsersToThreadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).RegisterManyUsersToThread(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_RegisterManyUsersToThread_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).RegisterManyUsersToThread(ctx, req.(*RegisterManyUsersToThreadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_RemoveRegistrationToThread_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveRegistrationToThreadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).RemoveRegistrationToThread(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_RemoveRegistrationToThread_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).RemoveRegistrationToThread(ctx, req.(*RemoveRegistrationToThreadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_RemoveManyRegistrationsToThread_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveManyRegistrationsToThreadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).RemoveManyRegistrationsToThread(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_RemoveManyRegistrationsToThread_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).RemoveManyRegistrationsToThread(ctx, req.(*RemoveManyRegistrationsToThreadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_ListThreadRegistrations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreadByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).ListThreadRegistrations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_ListThreadRegistrations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).ListThreadRegistrations(ctx, req.(*ThreadByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_CreateWeek_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeekRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).CreateWeek(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_CreateWeek_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).CreateWeek(ctx, req.(*WeekRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_ListWeeksForThread_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeeksForThreadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).ListWeeksForThread(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_ListWeeksForThread_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).ListWeeksForThread(ctx, req.(*WeeksForThreadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_GetWeekByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeekByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).GetWeekByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_GetWeekByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).GetWeekByID(ctx, req.(*WeekByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_UpdateWeek_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeekUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).UpdateWeek(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_UpdateWeek_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).UpdateWeek(ctx, req.(*WeekUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_DeleteWeekByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeekByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).DeleteWeekByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_DeleteWeekByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).DeleteWeekByID(ctx, req.(*WeekByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_CreateThreadSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreadScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).CreateThreadSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_CreateThreadSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).CreateThreadSchedule(ctx, req.(*ThreadScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_ListThreadSchedules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreadSchedulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).ListThreadSchedules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_ListThreadSchedules_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).ListThreadSchedules(ctx, req.(*ThreadSchedulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_GetThreadScheduleByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreadScheduleByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).GetThreadScheduleByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_GetThreadScheduleByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).GetThreadScheduleByID(ctx, req.(*ThreadScheduleByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_UpdateThreadSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreadScheduleUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).UpdateThreadSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_UpdateThreadSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).UpdateThreadSchedule(ctx, req.(*ThreadScheduleUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_DeleteThreadScheduleByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ThreadScheduleByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).DeleteThreadScheduleByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_DeleteThreadScheduleByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).DeleteThreadScheduleByID(ctx, req.(*ThreadScheduleByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_CreateLocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LocationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).CreateLocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_CreateLocation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).CreateLocation(ctx, req.(*LocationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_GetLocationByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LocationByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).GetLocationByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_GetLocationByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).GetLocationByID(ctx, req.(*LocationByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_ListLocations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LocationEmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).ListLocations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_ListLocations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).ListLocations(ctx, req.(*LocationEmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_UpdateLocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LocationUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).UpdateLocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_UpdateLocation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).UpdateLocation(ctx, req.(*LocationUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_DeleteLocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LocationByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).DeleteLocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_DeleteLocation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).DeleteLocation(ctx, req.(*LocationByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_CheckLocationAvailability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LocationAvailabilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).CheckLocationAvailability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_CheckLocationAvailability_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).CheckLocationAvailability(ctx, req.(*LocationAvailabilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_ListThreadsForUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserThreadsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).ListThreadsForUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_ListThreadsForUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).ListThreadsForUser(ctx, req.(*UserThreadsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_ListWeeksWithHomework_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WeeksWithHwRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).ListWeeksWithHomework(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_ListWeeksWithHomework_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).ListWeeksWithHomework(ctx, req.(*WeeksWithHwRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThreadService_CheckPrerequisites_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckPrerequisitesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThreadServiceServer).CheckPrerequisites(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ThreadService_CheckPrerequisites_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThreadServiceServer).CheckPrerequisites(ctx, req.(*CheckPrerequisitesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ThreadService_ServiceDesc is the grpc.ServiceDesc for ThreadService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ThreadService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "threadpb.ThreadService",
	HandlerType: (*ThreadServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateThread",
			Handler:    _ThreadService_CreateThread_Handler,
		},
		{
			MethodName: "ListThreadsByCourse",
			Handler:    _ThreadService_ListThreadsByCourse_Handler,
		},
		{
			MethodName: "ListThreads",
			Handler:    _ThreadService_ListThreads_Handler,
		},
		{
			MethodName: "GetThreadByID",
			Handler:    _ThreadService_GetThreadByID_Handler,
		},
		{
			MethodName: "UpdateThreadByID",
			Handler:    _ThreadService_UpdateThreadByID_Handler,
		},
		{
			MethodName: "DeleteThreadByID",
			Handler:    _ThreadService_DeleteThreadByID_Handler,
		},
		{
			MethodName: "RegisterUserToThread",
			Handler:    _ThreadService_RegisterUserToThread_Handler,
		},
		{
			MethodName: "RegisterManyUsersToThread",
			Handler:    _ThreadService_RegisterManyUsersToThread_Handler,
		},
		{
			MethodName: "RemoveRegistrationToThread",
			Handler:    _ThreadService_RemoveRegistrationToThread_Handler,
		},
		{
			MethodName: "RemoveManyRegistrationsToThread",
			Handler:    _ThreadService_RemoveManyRegistrationsToThread_Handler,
		},
		{
			MethodName: "ListThreadRegistrations",
			Handler:    _ThreadService_ListThreadRegistrations_Handler,
		},
		{
			MethodName: "CreateWeek",
			Handler:    _ThreadService_CreateWeek_Handler,
		},
		{
			MethodName: "ListWeeksForThread",
			Handler:    _ThreadService_ListWeeksForThread_Handler,
		},
		{
			MethodName: "GetWeekByID",
			Handler:    _ThreadService_GetWeekByID_Handler,
		},
		{
			MethodName: "UpdateWeek",
			Handler:    _ThreadService_UpdateWeek_Handler,
		},
		{
			MethodName: "DeleteWeekByID",
			Handler:    _ThreadService_DeleteWeekByID_Handler,
		},
		{
			MethodName: "CreateThreadSchedule",
			Handler:    _ThreadService_CreateThreadSchedule_Handler,
		},
		{
			MethodName: "ListThreadSchedules",
			Handler:    _ThreadService_ListThreadSchedules_Handler,
		},
		{
			MethodName: "GetThreadScheduleByID",
			Handler:    _ThreadService_GetThreadScheduleByID_Handler,
		},
		{
			MethodName: "UpdateThreadSchedule",
			Handler:    _ThreadService_UpdateThreadSchedule_Handler,
		},
		{
			MethodName: "DeleteThreadScheduleByID",
			Handler:    _ThreadService_DeleteThreadScheduleByID_Handler,
		},
		{
			MethodName: "CreateLocation",
			Handler:    _ThreadService_CreateLocation_Handler,
		},
		{
			MethodName: "GetLocationByID",
			Handler:    _ThreadService_GetLocationByID_Handler,
		},
		{
			MethodName: "ListLocations",
			Handler:    _ThreadService_ListLocations_Handler,
		},
		{
			MethodName: "UpdateLocation",
			Handler:    _ThreadService_UpdateLocation_Handler,
		},
		{
			MethodName: "DeleteLocation",
			Handler:    _ThreadService_DeleteLocation_Handler,
		},
		{
			MethodName: "CheckLocationAvailability",
			Handler:    _ThreadService_CheckLocationAvailability_Handler,
		},
		{
			MethodName: "ListThreadsForUser",
			Handler:    _ThreadService_ListThreadsForUser_Handler,
		},
		{
			MethodName: "ListWeeksWithHomework",
			Handler:    _ThreadService_ListWeeksWithHomework_Handler,
		},
		{
			MethodName: "CheckPrerequisites",
			Handler:    _ThreadService_CheckPrerequisites_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/thread/thread.proto",
}
