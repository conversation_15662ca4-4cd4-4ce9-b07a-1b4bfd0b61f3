package handlers

import (
	"fmt"
	semesterpb "github.com/olzzhas/edunite-server/course_service/pb/semester"
	"google.golang.org/protobuf/types/known/timestamppb"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
)

// SemesterHandler обрабатывает HTTP-запросы, связанные с семестрами
type SemesterHandler struct {
	RabbitLogPublisher clients.LogPublisher
	SemesterService    *clients.SemesterClient
}

// SemesterCreateRequest - промежуточная структура для связывания JSON-запроса
type SemesterCreateRequest struct {
	Name      string `json:"name" binding:"required"`
	StartDate string `json:"start_date"` // Формат RFC3339
	EndDate   string `json:"end_date"`   // Формат RFC3339
}

// CreateSemesterHandler создает новый семестр
func (h *SemesterHandler) CreateSemesterHandler(c *gin.Context) {
	var req SemesterCreateRequest

	// Парсим JSON-тело запроса в промежуточную структуру
	if err := c.ShouldBindJSON(&req); err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing create semester request: %v", err),
			"semester",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// Преобразование строковых дат в timestamppb.Timestamp
	var startDate, endDate *timestamppb.Timestamp
	var err error

	if req.StartDate != "" {
		t, err := time.Parse(time.RFC3339, req.StartDate)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Invalid start_date format: %v", err),
				"semester",
				map[string]any{"start_date": req.StartDate, "error": err.Error()},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid start_date format, expected RFC3339"})
			return
		}
		startDate = timestamppb.New(t)
	}

	if req.EndDate != "" {
		t, err := time.Parse(time.RFC3339, req.EndDate)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Invalid end_date format: %v", err),
				"semester",
				map[string]any{"end_date": req.EndDate, "error": err.Error()},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid end_date format, expected RFC3339"})
			return
		}
		endDate = timestamppb.New(t)
	}

	// Формируем запрос для SemesterService
	semesterReq := &semesterpb.SemesterRequest{
		Name:      req.Name,
		StartDate: startDate,
		EndDate:   endDate,
	}

	// Отправляем запрос в SemesterService
	resp, err := h.SemesterService.CreateSemester(c.Request.Context(), semesterReq)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error creating semester: %v", err),
			"semester",
			map[string]any{"name": req.Name, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create semester"})
		return
	}

	// Логируем успешное создание семестра
	h.RabbitLogPublisher.PublishLog(
		"info",
		fmt.Sprintf("Semester created successfully: %d", resp.Id),
		"semester",
		map[string]any{
			"name":        req.Name,
			"semester_id": strconv.FormatInt(resp.Id, 10),
		},
	)

	// Отправляем успешный ответ
	c.JSON(http.StatusCreated, gin.H{
		"message":     "Semester created successfully",
		"semester_id": resp.Id,
		"name":        resp.Name,
		"start_date":  resp.StartDate.AsTime().Format(time.RFC3339),
		"end_date":    resp.EndDate.AsTime().Format(time.RFC3339),
		"created_at":  resp.CreatedAt.AsTime().Format(time.RFC3339),
		"updated_at":  resp.UpdatedAt.AsTime().Format(time.RFC3339),
	})
}

// ListSemestersHandler получает список всех семестров
func (h *SemesterHandler) ListSemestersHandler(c *gin.Context) {
	// Создаем пустой запрос
	req := &semesterpb.SemesterEmptyRequest{}

	// Отправляем запрос в SemesterService
	resp, err := h.SemesterService.ListSemesters(c.Request.Context(), req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error listing semesters: %v", err),
			"semester",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list semesters"})
		return
	}

	// Отправляем успешный ответ
	c.JSON(http.StatusOK, gin.H{
		"semesters": resp.Semesters,
	})
}

// ListSemestersWithBreaksHandler получает список всех семестров с их выходными днями
func (h *SemesterHandler) ListSemestersWithBreaksHandler(c *gin.Context) {
	// Получаем все семестры с их выходными днями
	semestersMap, breaksMap, err := h.SemesterService.GetAllSemestersWithBreaks(c.Request.Context())
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error listing semesters with breaks: %v", err),
			"semester",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list semesters with breaks"})
		return
	}

	// Создаем структуру для ответа
	type SemesterWithBreaks struct {
		ID        int64                             `json:"id"`
		Name      string                            `json:"name"`
		StartDate *timestamppb.Timestamp            `json:"start_date"`
		EndDate   *timestamppb.Timestamp            `json:"end_date"`
		CreatedAt *timestamppb.Timestamp            `json:"created_at"`
		UpdatedAt *timestamppb.Timestamp            `json:"updated_at"`
		Breaks    []*semesterpb.SemesterBreakResponse `json:"breaks"`
	}

	// Формируем список семестров с их выходными днями
	var semestersWithBreaks []*SemesterWithBreaks
	for semesterID, semester := range semestersMap {
		semesterWithBreaks := &SemesterWithBreaks{
			ID:        semester.Id,
			Name:      semester.Name,
			StartDate: semester.StartDate,
			EndDate:   semester.EndDate,
			CreatedAt: semester.CreatedAt,
			UpdatedAt: semester.UpdatedAt,
			Breaks:    breaksMap[semesterID],
		}
		semestersWithBreaks = append(semestersWithBreaks, semesterWithBreaks)
	}

	// Отправляем успешный ответ
	c.JSON(http.StatusOK, gin.H{
		"semesters": semestersWithBreaks,
	})
}

// GetSemesterByIDHandler получает семестр по ID
func (h *SemesterHandler) GetSemesterByIDHandler(c *gin.Context) {
	semesterIDStr := c.Param("id")
	if semesterIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "semester_id is required"})
		return
	}

	semesterID, err := strconv.ParseInt(semesterIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid semester_id"})
		return
	}

	// Формируем запрос
	req := &semesterpb.SemesterByID{
		Id: semesterID,
	}

	// Отправляем запрос в SemesterService
	resp, err := h.SemesterService.GetSemesterByID(c.Request.Context(), req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting semester by ID: %v", err),
			"semester",
			map[string]any{"semester_id": semesterIDStr, "error": err.Error()},
		)
		c.JSON(http.StatusNotFound, gin.H{"error": "semester not found"})
		return
	}

	// Отправляем успешный ответ
	c.JSON(http.StatusOK, gin.H{
		"semester": resp,
	})
}

// UpdateSemesterHandler обновляет семестр по ID
func (h *SemesterHandler) UpdateSemesterHandler(c *gin.Context) {
	semesterIDStr := c.Param("id")
	if semesterIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "semester_id is required"})
		return
	}

	semesterID, err := strconv.ParseInt(semesterIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid semester_id"})
		return
	}

	var req semesterpb.SemesterUpdateRequest

	// Парсим JSON-тело запроса
	if err := c.ShouldBindJSON(&req); err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing update semester request: %v", err),
			"semester",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// Устанавливаем ID семестра
	req.Id = semesterID

	// Валидация обязательных полей
	if req.Name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "name is required"})
		return
	}

	// Отправляем запрос в SemesterService
	resp, err := h.SemesterService.UpdateSemester(c.Request.Context(), &req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error updating semester: %v", err),
			"semester",
			map[string]any{"semester_id": semesterIDStr, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update semester"})
		return
	}

	// Логируем успешное обновление семестра
	h.RabbitLogPublisher.PublishLog(
		"info",
		fmt.Sprintf("Semester updated successfully: %d", resp.Id),
		"semester",
		map[string]any{
			"semester_id": resp.Id,
			"name":        resp.Name,
		},
	)

	// Отправляем успешный ответ
	c.JSON(http.StatusOK, gin.H{
		"message":     "Semester updated successfully",
		"semester_id": resp.Id,
		"name":        resp.Name,
		"created_at":  resp.CreatedAt.AsTime(),
		"updated_at":  resp.UpdatedAt.AsTime(),
	})
}

// DeleteSemesterHandler удаляет семестр по ID
func (h *SemesterHandler) DeleteSemesterHandler(c *gin.Context) {
	semesterIDStr := c.Param("id")
	if semesterIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "semester_id is required"})
		return
	}

	semesterID, err := strconv.ParseInt(semesterIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid semester_id"})
		return
	}

	// Формируем запрос
	req := &semesterpb.SemesterByID{
		Id: semesterID,
	}

	// Отправляем запрос в SemesterService
	_, err = h.SemesterService.DeleteSemester(c.Request.Context(), req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error deleting semester: %v", err),
			"semester",
			map[string]any{"semester_id": semesterIDStr, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete semester"})
		return
	}

	// Логируем успешное удаление семестра
	h.RabbitLogPublisher.PublishLog(
		"info",
		fmt.Sprintf("Semester deleted successfully: %d", semesterID),
		"semester",
		map[string]any{"semester_id": semesterIDStr},
	)

	// Отправляем успешный ответ
	c.JSON(http.StatusOK, gin.H{"message": "Semester deleted successfully"})
}

// ListSemesterBreaksHandler возвращает список дат каникул для семестра
func (h *SemesterHandler) ListSemesterBreaksHandler(c *gin.Context) {
	// Получаем semester_id из URL
	semesterIDStr := c.Param("id")
	if semesterIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "semester_id is required"})
		return
	}

	semesterID, err := strconv.ParseInt(semesterIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid semester_id"})
		return
	}

	// Формируем gRPC‑запрос
	req := &semesterpb.SemesterByID{Id: semesterID}

	// Вызываем SemesterService
	resp, err := h.SemesterService.ListSemesterBreaks(c.Request.Context(), req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error listing semester breaks: %v", err),
			"semester_break",
			map[string]any{"semester_id": semesterIDStr, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list semester breaks"})
		return
	}

	// Отправляем успешный ответ
	c.JSON(http.StatusOK, gin.H{"breaks": resp.SemesterBreaks})
}

type addSemesterBreakDTO struct {
	BreakDate   string `json:"break_date" binding:"required,datetime=2006-01-02"`
	Description string `json:"description"`
}

// 2) Хэндлер.
func (h *SemesterHandler) AddSemesterBreakHandler(c *gin.Context) {
	// --- validate path ---
	semesterID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil || semesterID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid semester_id"})
		return
	}

	// --- bind body into DTO ---
	var body addSemesterBreakDTO
	if err := c.ShouldBindJSON(&body); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body", "details": err.Error()})
		return
	}

	// --- convert break_date -> timestamppb.Timestamp ---
	const layout = "2006-01-02" // ожидаем YYYY-MM-DD
	t, err := time.Parse(layout, body.BreakDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid break_date format; expected YYYY-MM-DD"})
		return
	}

	// --- собираем protobuf-запрос ---
	req := &semesterpb.SemesterBreakRequest{
		SemesterId:  semesterID,
		BreakDate:   timestamppb.New(t),
		Description: body.Description,
	}

	// --- вызываем сервис ---
	resp, err := h.SemesterService.AddSemesterBreak(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to add semester break"})
		return
	}

	c.JSON(http.StatusOK, resp)
}
