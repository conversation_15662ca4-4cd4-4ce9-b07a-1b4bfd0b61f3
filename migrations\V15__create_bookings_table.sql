-- Create booking status enum (CockroachDB compatible)
CREATE TYPE booking_status AS ENUM (
    'pending',
    'confirmed',
    'cancelled',
    'completed'
);

-- Create bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id BIGSERIAL PRIMARY KEY,
    schedule_id BIGINT NOT NULL REFERENCES sport_schedules(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status booking_status NOT NULL DEFAULT 'confirmed',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(schedule_id, user_id)
);

-- Create trigger for updated_at
CREATE TRIGGER trg_bookings_updated
    BEFORE UPDATE ON bookings
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

-- Create attendance status enum (CockroachDB compatible)
CREATE TYPE sport_attendance_status AS ENUM (
    'unmarked',
    'present',
    'absent',
    'excused'
);

-- Create sport attendance table
CREATE TABLE IF NOT EXISTS sport_attendance (
    booking_id BIGINT PRIMARY KEY REFERENCES bookings(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    schedule_id BIGINT NOT NULL REFERENCES sport_schedules(id) ON DELETE CASCADE,
    status sport_attendance_status NOT NULL DEFAULT 'unmarked',
    notes TEXT,
    marked_by BIGINT REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger for updated_at
CREATE TRIGGER trg_sport_attendance_updated
    BEFORE UPDATE ON sport_attendance
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();
