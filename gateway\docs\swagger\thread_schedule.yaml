openapi: 3.0.0
info:
  title: Thread Schedule API
  description: API for managing thread schedules in the Edunite platform
  version: 1.0.0
  contact:
    name: Edunite Support
    email: <EMAIL>

servers:
  - url: http://localhost:8081
    description: Local development server
  - url: https://api.edunite.com
    description: Production server

tags:
  - name: schedules
    description: Thread schedule operations

paths:
  /thread/{thread_id}/schedules:
    post:
      tags:
        - schedules
      summary: Create a new schedule for a thread
      description: Creates a new schedule entry for a specific thread
      operationId: createThreadSchedule
      parameters:
        - name: thread_id
          in: path
          required: true
          description: ID of the thread
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ThreadScheduleRequest'
      responses:
        '201':
          description: Schedule created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThreadScheduleResponse'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    get:
      tags:
        - schedules
      summary: List all schedules for a thread
      description: Returns all schedule entries for a specific thread
      operationId: listThreadSchedules
      parameters:
        - name: thread_id
          in: path
          required: true
          description: ID of the thread
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: List of schedules
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ThreadScheduleResponse'
        '400':
          description: Invalid thread ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /thread/schedules/{schedule_id}:
    get:
      tags:
        - schedules
      summary: Get a schedule by ID
      description: Returns a specific schedule by its ID
      operationId: getThreadScheduleByID
      parameters:
        - name: schedule_id
          in: path
          required: true
          description: ID of the schedule
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Schedule details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThreadScheduleResponse'
        '400':
          description: Invalid schedule ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Schedule not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    put:
      tags:
        - schedules
      summary: Update a schedule
      description: Updates an existing schedule
      operationId: updateThreadSchedule
      parameters:
        - name: schedule_id
          in: path
          required: true
          description: ID of the schedule
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ThreadScheduleUpdateRequest'
      responses:
        '200':
          description: Schedule updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ThreadScheduleResponse'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Schedule not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    delete:
      tags:
        - schedules
      summary: Delete a schedule
      description: Deletes a specific schedule
      operationId: deleteThreadSchedule
      parameters:
        - name: schedule_id
          in: path
          required: true
          description: ID of the schedule
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Schedule deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties: {}
        '400':
          description: Invalid schedule ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Schedule not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /thread/schedules/{schedule_id}/location:
    get:
      tags:
        - schedules
      summary: Get schedule location
      description: Returns the location field for a specific schedule
      operationId: getScheduleLocation
      parameters:
        - name: schedule_id
          in: path
          required: true
          description: ID of the schedule
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Schedule location
          content:
            application/json:
              schema:
                type: object
                properties:
                  location:
                    type: string
                    description: The location of the schedule
                    example: "Room 101"
        '400':
          description: Invalid schedule ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Schedule not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    put:
      tags:
        - schedules
      summary: Update schedule location
      description: Updates the location field for a specific schedule
      operationId: updateScheduleLocation
      parameters:
        - name: schedule_id
          in: path
          required: true
          description: ID of the schedule
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - location
              properties:
                location:
                  type: string
                  description: The new location for the schedule
                  example: "Room 202"
      responses:
        '200':
          description: Schedule location updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "schedule location updated successfully"
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Schedule not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  schemas:
    ThreadScheduleRequest:
      type: object
      required:
        - day_of_week
        - start_time
        - end_time
      properties:
        day_of_week:
          type: integer
          format: int32
          minimum: 1
          maximum: 7
          description: Day of the week (1=Monday, 7=Sunday)
          example: 1
        start_time:
          type: string
          format: time
          description: Start time in HH:MM:SS format
          example: "09:00:00"
        end_time:
          type: string
          format: time
          description: End time in HH:MM:SS format
          example: "10:30:00"
    
    ThreadScheduleUpdateRequest:
      type: object
      required:
        - day_of_week
        - start_time
        - end_time
      properties:
        thread_id:
          type: integer
          format: int64
          description: ID of the thread
          example: 1
        day_of_week:
          type: integer
          format: int32
          minimum: 1
          maximum: 7
          description: Day of the week (1=Monday, 7=Sunday)
          example: 1
        start_time:
          type: string
          format: time
          description: Start time in HH:MM:SS format
          example: "09:00:00"
        end_time:
          type: string
          format: time
          description: End time in HH:MM:SS format
          example: "10:30:00"
    
    ThreadScheduleResponse:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Unique identifier for the schedule
          example: 1
        thread_id:
          type: integer
          format: int64
          description: ID of the thread
          example: 1
        day_of_week:
          type: integer
          format: int32
          description: Day of the week (1=Monday, 7=Sunday)
          example: 1
        start_time:
          type: string
          format: time
          description: Start time in HH:MM:SS format
          example: "09:00:00"
        end_time:
          type: string
          format: time
          description: End time in HH:MM:SS format
          example: "10:30:00"
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2025-04-23T10:00:00Z"
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2025-04-23T10:00:00Z"
    
    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
          example: "invalid request body"
