{"info": {"_postman_id": "e5a7d8f1-3b2c-4e5d-8f9a-1c2d3e4f5a6b", "name": "Edunite Location Endpoints", "description": "Collection of endpoints for managing locations and schedules with location conflict checking", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Locations", "description": "Endpoints for managing locations (cabinets/rooms)", "item": [{"name": "Create Location", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/locations", "host": ["{{base_url}}"], "path": ["locations"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Room 101\",\n  \"description\": \"Main lecture hall\",\n  \"capacity\": 50\n}"}, "description": "Create a new location (cabinet/room)"}, "response": []}, {"name": "Get Location by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/locations/:location_id", "host": ["{{base_url}}"], "path": ["locations", ":location_id"], "variable": [{"key": "location_id", "value": "1", "description": "ID of the location to retrieve"}]}, "description": "Get details of a specific location by ID"}, "response": []}, {"name": "List All Locations", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/locations", "host": ["{{base_url}}"], "path": ["locations"]}, "description": "Get a list of all available locations"}, "response": []}, {"name": "Update Location", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/locations/:location_id", "host": ["{{base_url}}"], "path": ["locations", ":location_id"], "variable": [{"key": "location_id", "value": "1", "description": "ID of the location to update"}]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Room 101A\",\n  \"description\": \"Updated lecture hall\",\n  \"capacity\": 60\n}"}, "description": "Update an existing location"}, "response": []}, {"name": "Delete Location", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/locations/:location_id", "host": ["{{base_url}}"], "path": ["locations", ":location_id"], "variable": [{"key": "location_id", "value": "1", "description": "ID of the location to delete"}]}, "description": "Delete a location"}, "response": []}, {"name": "Check Location Availability", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/locations/check-availability", "host": ["{{base_url}}"], "path": ["locations", "check-availability"]}, "body": {"mode": "raw", "raw": "{\n  \"location_id\": 1,\n  \"day_of_week\": 1,\n  \"start_time\": \"09:00:00\",\n  \"end_time\": \"10:30:00\",\n  \"exclude_schedule_id\": 0\n}"}, "description": "Check if a location is available during a specific time slot"}, "response": []}]}, {"name": "Schedules with Location", "description": "Endpoints for managing schedules with location information", "item": [{"name": "Create Schedule with Location", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/thread/:thread_id/schedules", "host": ["{{base_url}}"], "path": ["thread", ":thread_id", "schedules"], "variable": [{"key": "thread_id", "value": "1", "description": "ID of the thread to create a schedule for"}]}, "body": {"mode": "raw", "raw": "{\n  \"day_of_week\": 1,\n  \"start_time\": \"09:00:00\",\n  \"end_time\": \"10:30:00\",\n  \"location\": \"Room 101\"\n}"}, "description": "Create a new schedule with location information"}, "response": []}, {"name": "Update Schedule with Location", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/thread/schedules/:schedule_id", "host": ["{{base_url}}"], "path": ["thread", "schedules", ":schedule_id"], "variable": [{"key": "schedule_id", "value": "1", "description": "ID of the schedule to update"}]}, "body": {"mode": "raw", "raw": "{\n  \"day_of_week\": 1,\n  \"start_time\": \"09:00:00\",\n  \"end_time\": \"10:30:00\",\n  \"location\": \"Room 102\"\n}"}, "description": "Update an existing schedule with new location information"}, "response": []}, {"name": "Get Schedule with Location", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/thread/schedules/:schedule_id", "host": ["{{base_url}}"], "path": ["thread", "schedules", ":schedule_id"], "variable": [{"key": "schedule_id", "value": "1", "description": "ID of the schedule to retrieve"}]}, "description": "Get details of a specific schedule including location information"}, "response": []}, {"name": "List Thread Schedules with Locations", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/thread/:thread_id/schedules", "host": ["{{base_url}}"], "path": ["thread", ":thread_id", "schedules"], "variable": [{"key": "thread_id", "value": "1", "description": "ID of the thread to list schedules for"}]}, "description": "Get a list of all schedules for a thread including location information"}, "response": []}, {"name": "Create Thread with Schedules and Locations", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/thread/with-schedule", "host": ["{{base_url}}"], "path": ["thread", "with-schedule"]}, "body": {"mode": "raw", "raw": "{\n  \"title\": \"Math 101\",\n  \"course_id\": 1,\n  \"semester_id\": 1,\n  \"teacher_id\": 1,\n  \"max_students\": 30,\n  \"schedules\": [\n    {\n      \"day_of_week\": 1,\n      \"start_time\": \"09:00:00\",\n      \"end_time\": \"10:30:00\",\n      \"location\": \"Room 101\"\n    },\n    {\n      \"day_of_week\": 3,\n      \"start_time\": \"09:00:00\",\n      \"end_time\": \"10:30:00\",\n      \"location\": \"Room 102\"\n    }\n  ]\n}"}, "description": "Create a new thread with multiple schedules and locations in a single request"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}]}