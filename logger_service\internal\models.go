package internal

import (
	"time"
)

// LogEntry represents a log entry stored in MongoDB
type LogEntry struct {
	Level     string                 `bson:"level" json:"level"`
	Message   string                 `bson:"message" json:"message"`
	Service   string                 `bson:"service" json:"service"`
	Data      map[string]interface{} `bson:"data" json:"data"`
	Datetime  time.Time              `bson:"datetime" json:"datetime"`
}

// LogFilter represents filter criteria for fetching logs
type LogFilter struct {
	Level     string    `json:"level,omitempty"`
	Service   string    `json:"service,omitempty"`
	StartDate time.Time `json:"start_date,omitempty"`
	EndDate   time.Time `json:"end_date,omitempty"`
	Limit     int64     `json:"limit,omitempty"`
	Skip      int64     `json:"skip,omitempty"`
}
