-- Add location column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'thread_schedules'
        AND column_name = 'location'
    ) THEN
        ALTER TABLE thread_schedules
        ADD COLUMN location VARCHAR(255);
    END IF;
END $$;

-- Update thread_schedules to set location based on location_id
UPDATE thread_schedules ts
SET location = l.name
FROM locations l
WHERE ts.location_id = l.id
AND ts.location IS NULL;

-- Create a trigger to keep location in sync with location_id
CREATE OR REPLACE FUNCTION sync_location_name()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.location_id IS NOT NULL THEN
        SELECT name INTO NEW.location FROM locations WHERE id = NEW.location_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for insert and update
DROP TRIGGER IF EXISTS trg_sync_location_name ON thread_schedules;
CREATE TRIGGER trg_sync_location_name
    BEFORE INSERT OR UPDATE ON thread_schedules
    FOR EACH ROW
    EXECUTE FUNCTION sync_location_name();
