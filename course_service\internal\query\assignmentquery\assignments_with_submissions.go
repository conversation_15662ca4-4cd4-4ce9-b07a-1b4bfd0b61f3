// internal/assignmentquery/repo.go
package assignmentquery

import (
	"context"
	"fmt"

	"github.com/olzzhas/edunite-server/course_service/internal/dto"

	"github.com/jackc/pgx/v4/pgxpool"
)

type Repo struct{ db *pgxpool.Pool }

func NewRepo(db *pgxpool.Pool) *Repo { return &Repo{db: db} }

// ListAssignmentsWithSubmission возвращает все задания потока и, если есть, submission указанного user_id
func (r *Repo) ListAssignmentsWithSubmission(ctx context.Context, threadID, userID int64) ([]*dto.AssignmentWithSubmissionDTO, error) {
	const q = `
		SELECT
		  a.id         AS ass_id,
		  w.id         AS week_id,
		  a.title,
		  a.description,
		  a.due_date,
		  a.max_points,
		  a.assignment_group_id,
		  a.type,
		  a.created_at AS ass_created,
		  a.updated_at AS ass_updated,

		  s.id         AS sub_id,
		  s.user_id,
		  s.submitted_at,
		  s.file_urls,
		  s.comment,
		  s.score,
		  s.feedback,
		  s.created_at AS sub_created,
		  s.updated_at AS sub_updated
		FROM assignments a
		JOIN weeks w
		  ON a.week_id = w.id
		LEFT JOIN assignment_submissions s
		  ON s.assignment_id = a.id
		  AND s.user_id      = $2
		WHERE w.thread_id = $1
		ORDER BY w.week_number, a.id;
	`
	rows, err := r.db.Query(ctx, q, threadID, userID)
	if err != nil {
		return nil, fmt.Errorf("query assignments with submission: %w", err)
	}
	defer rows.Close()

	var out []*dto.AssignmentWithSubmissionDTO
	for rows.Next() {
		var d dto.AssignmentWithSubmissionDTO
		if err := rows.Scan(
			&d.AssID, &d.WeekID, &d.Title, &d.Description, &d.DueDate, &d.MaxPoints, &d.AssignmentGroupID, &d.Type,
			&d.AssCreatedAt, &d.AssUpdatedAt,
			&d.SubID, &d.UserID, &d.SubmittedAt, &d.FileURLs, &d.Comment, &d.Score, &d.Feedback,
			&d.SubCreatedAt, &d.SubUpdatedAt,
		); err != nil {
			return nil, fmt.Errorf("scan row: %w", err)
		}
		out = append(out, &d)
	}
	return out, rows.Err()
}

// ListAssignmentsWithoutSubmission возвращает все задания потока, для которых у указанного user_id нет submission
func (r *Repo) ListAssignmentsWithoutSubmission(ctx context.Context, threadID, userID int64) ([]*dto.AssignmentWithSubmissionDTO, error) {
	const q = `
		SELECT
		  a.id         AS ass_id,
		  w.id         AS week_id,
		  a.title,
		  a.description,
		  a.due_date,
		  a.max_points,
		  a.assignment_group_id,
		  a.type,
		  a.created_at AS ass_created,
		  a.updated_at AS ass_updated,
		  NULL         AS sub_id,
		  NULL         AS user_id,
		  NULL         AS submitted_at,
		  NULL         AS file_urls,
		  NULL         AS comment,
		  NULL         AS score,
		  NULL         AS feedback,
		  NULL         AS sub_created,
		  NULL         AS sub_updated
		FROM assignments a
		JOIN weeks w ON a.week_id = w.id
		LEFT JOIN assignment_submissions s ON s.assignment_id = a.id AND s.user_id = $2
		WHERE w.thread_id = $1 AND s.id IS NULL
		ORDER BY w.week_number, a.id;
	`
	rows, err := r.db.Query(ctx, q, threadID, userID)
	if err != nil {
		return nil, fmt.Errorf("query assignments without submission: %w", err)
	}
	defer rows.Close()

	var out []*dto.AssignmentWithSubmissionDTO
	for rows.Next() {
		var d dto.AssignmentWithSubmissionDTO
		var fileURLs []string // Empty array for NULL file_urls
		if err := rows.Scan(
			&d.AssID, &d.WeekID, &d.Title, &d.Description, &d.DueDate, &d.MaxPoints, &d.AssignmentGroupID, &d.Type,
			&d.AssCreatedAt, &d.AssUpdatedAt,
			&d.SubID, &d.UserID, &d.SubmittedAt, &fileURLs, &d.Comment, &d.Score, &d.Feedback,
			&d.SubCreatedAt, &d.SubUpdatedAt,
		); err != nil {
			return nil, fmt.Errorf("scan row: %w", err)
		}
		d.FileURLs = fileURLs
		out = append(out, &d)
	}
	return out, rows.Err()
}
