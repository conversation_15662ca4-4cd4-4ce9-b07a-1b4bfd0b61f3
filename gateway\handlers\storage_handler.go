package handlers

import (
	"net/http"
	"os"
	"path/filepath"

	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
)

type StorageHandler struct {
	RabbitLogPublisher clients.LogPublisher
	StorageClient      *clients.StorageClient
}

// UploadFileHandler загружает файл в MinIO через gRPC StorageService.
// Пример: POST /storage/upload?bucket=mybucket&object=myfolder/example.jpg
// Передаём файл в multipart form под именем "file".
func (h *StorageHandler) UploadFileHandler(c *gin.Context) {
	bucketName := c.Query("bucket")
	if bucketName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "bucket is required"})
		return
	}
	objectName := c.Query("object")
	if objectName == "" {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "object is required"})
		return
	}

	// Получаем файл из multipart/form-data
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "file is required"})
		return
	}
	defer file.Close()

	// Можно извлечь MIME-тип из заголовка (примерный способ)
	contentType := header.Header.Get("Content-Type")
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	// Читаем файл в память
	fileData := make([]byte, header.Size)
	_, err = file.Read(fileData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to read file"})
		return
	}

	// Вызываем gRPC-клиент StorageClient.UploadFile
	resp, err := h.StorageClient.UploadFile(
		c.Request.Context(),
		bucketName,
		objectName,
		contentType,
		fileData,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	h.RabbitLogPublisher.PublishLog("Info", "File uploaded", "Storage", map[string]any{
		"bucket": bucketName,
		"object": objectName,
	})

	c.JSON(http.StatusOK, gin.H{"message": "file uploaded", "file_url": resp.FileUrl})
}

// DownloadFileHandler скачивает файл из MinIO через gRPC StorageService.
// GET /storage/download/:bucket/:object
func (h *StorageHandler) DownloadFileHandler(c *gin.Context) {
	bucketName := c.Param("bucket")
	objectName := c.Param("object")

	if bucketName == "" || objectName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "bucketName and objectName are required"})
		return
	}

	resp, err := h.StorageClient.DownloadFile(c.Request.Context(), bucketName, objectName)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// Прямо отдаём файл в виде octet-stream или resp.ContentType
	c.Data(http.StatusOK, resp.ContentType, resp.FileData)
}

// DeleteFileHandler удаляет файл из MinIO через gRPC StorageService.
// DELETE /storage/:bucket/:object
func (h *StorageHandler) DeleteFileHandler(c *gin.Context) {
	bucketName := c.Param("bucket")
	objectName := c.Param("object")

	if bucketName == "" || objectName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "bucket and object are required"})
		return
	}

	if err := h.StorageClient.DeleteFile(c.Request.Context(), bucketName, objectName); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	h.RabbitLogPublisher.PublishLog("Info", "File deleted", "Storage", map[string]any{
		"bucket": bucketName,
		"object": objectName,
	})

	c.JSON(http.StatusOK, gin.H{"message": "file deleted"})
}

// GetPhotoHandler скачивает файл (фото) из MinIO через gRPC StorageService
// или из локальной файловой системы
func (h *StorageHandler) GetPhotoHandler(c *gin.Context) {
	bucketName := c.Param("bucket")
	objectName := c.Param("object")

	if bucketName == "" || objectName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "bucket and object are required"})
		return
	}

	// Try to serve the file from the local file system first
	localFilePath := filepath.Join("/app/storage", bucketName, objectName)
	if _, err := os.Stat(localFilePath); err == nil {
		// File exists, serve it directly
		c.File(localFilePath)
		return
	}

	// If the file doesn't exist locally, try to get it from the storage service
	resp, err := h.StorageClient.DownloadFile(c.Request.Context(), bucketName, objectName)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// Возвращаем байты картинки и MIME-тип из gRPC-ответа
	c.Data(http.StatusOK, resp.ContentType, resp.FileData)
}
