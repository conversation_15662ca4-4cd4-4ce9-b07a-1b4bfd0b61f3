package thread

import (
	"context"
	"errors"
	"sort"
	"time"

	"github.com/olzzhas/edunite-server/course_service/internal/database"
	assignmentpb "github.com/olzzhas/edunite-server/course_service/pb/assignment"
	threadpb "github.com/olzzhas/edunite-server/course_service/pb/thread"

	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (s *Service) CreateWeek(ctx context.Context, req *threadpb.WeekRequest) (*threadpb.WeekResponse, error) {
	w := &database.Week{
		ThreadID:    req.GetThreadId(),
		WeekNumber:  req.GetWeekNumber(),
		Type:        req.GetType(),
		Title:       req.GetTitle(),
		Description: req.GetDescription(),
	}

	v := validator.New()
	database.ValidateWeek(v, w)
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.<PERSON>rrors)
	}

	err := s.weekRepo.CreateWeek(ctx, w)
	if err != nil {
		switch {
		case errors.Is(err, database.ErrWeekConflict):
			return nil, status.Errorf(codes.AlreadyExists, err.Error())
		default:
			return nil, status.Errorf(codes.Internal, "could not create week: %v", err)
		}
	}

	return weekToPB(w), nil
}

func (s *Service) GetWeekByID(ctx context.Context, req *threadpb.WeekByID) (*threadpb.WeekResponse, error) {
	w, err := s.weekRepo.GetWeek(ctx, req.GetWeekId())
	if err != nil {
		switch {
		case errors.Is(err, database.ErrWeekNotFound):
			return nil, status.Errorf(codes.NotFound, err.Error())
		default:
			return nil, status.Errorf(codes.Internal, "error fetching week: %v", err)
		}
	}
	return weekToPB(w), nil
}

func (s *Service) ListWeeksForThread(ctx context.Context, req *threadpb.WeeksForThreadRequest) (*threadpb.WeeksResponse, error) {
	list, err := s.weekRepo.GetWeeksByThreadID(ctx, req.GetThreadId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not list weeks: %v", err)
	}

	return weeksToPB(list), nil
}

func (s *Service) UpdateWeekByID(ctx context.Context, req *threadpb.WeekUpdateRequest) (*threadpb.WeekResponse, error) {
	w, err := s.weekRepo.GetWeek(ctx, req.GetWeekId())
	if err != nil {
		switch {
		case errors.Is(err, database.ErrWeekNotFound):
			return nil, status.Errorf(codes.NotFound, err.Error())
		default:
			return nil, status.Errorf(codes.Internal, "error fetching week: %v", err)
		}
	}

	// Only update fields that are provided in the request (partial update)
	if req.WeekNumber != 0 {
		w.WeekNumber = req.GetWeekNumber()
	}
	if req.Type != "" {
		w.Type = req.GetType()
	}
	if req.Title != "" {
		w.Title = req.GetTitle()
	}
	if req.Description != "" {
		w.Description = req.GetDescription()
	}

	v := validator.New()
	database.ValidateWeek(v, w)
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	if err := s.weekRepo.UpdateWeek(ctx, w); err != nil {
		return nil, status.Errorf(codes.Internal, "could not update week: %v", err)
	}
	return weekToPB(w), nil
}

func (s *Service) DeleteWeekByID(ctx context.Context, req *threadpb.WeekByID) (*threadpb.ThreadEmptyResponse, error) {
	if err := s.weekRepo.DeleteWeek(ctx, req.GetWeekId()); err != nil {
		switch {
		case errors.Is(err, database.ErrWeekNotFound):
			return nil, status.Errorf(codes.NotFound, err.Error())
		default:
			return nil, status.Errorf(codes.Internal, "could not delete week: %v", err)
		}
	}
	return &threadpb.ThreadEmptyResponse{}, nil
}

// UpdateWeek implements the UpdateWeek method from the ThreadServiceServer interface
// It calls the existing UpdateWeekByID method
// The week_id is extracted from the URL path parameter, not from the request body
func (s *Service) UpdateWeek(ctx context.Context, req *threadpb.WeekUpdateRequest) (*threadpb.WeekResponse, error) {
	// The week_id in the request body is ignored, as it should come from the URL path parameter
	// We use the week_id that's already in the request (set by the handler from the URL path)
	return s.UpdateWeekByID(ctx, req)
}

func weekToPB(w *database.Week) *threadpb.WeekResponse {
	if w == nil {
		return &threadpb.WeekResponse{}
	}
	return &threadpb.WeekResponse{
		Id:          w.ID,
		ThreadId:    w.ThreadID,
		WeekNumber:  w.WeekNumber,
		Type:        w.Type,
		Title:       w.Title,
		Description: w.Description,
		CreatedAt:   timestamppb.New(w.CreatedAt),
		UpdatedAt:   timestamppb.New(w.UpdatedAt),
	}
}

func weeksToPB(weeks []*database.Week) *threadpb.WeeksResponse {
	pbList := make([]*threadpb.WeekResponse, 0, len(weeks))
	for _, w := range weeks {
		pbList = append(pbList, weekToPB(w))
	}
	return &threadpb.WeeksResponse{Weeks: pbList}
}

func (s *Service) ListWeeksWithHomework(
	ctx context.Context, req *threadpb.WeeksWithHwRequest,
) (*threadpb.WeeksWithHwResponse, error) {

	if req.GetThreadId() == 0 || req.GetUserId() == 0 {
		return nil, status.Error(codes.InvalidArgument, "thread_id and user_id required")
	}

	rows, err := s.threadQueryRepo. // ← инжектируем этот repo
					ListWeeksWithAssignmentsAndSubmission(ctx,
			req.GetThreadId(), req.GetUserId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "query: %v", err)
	}

	// ── группируем по week_id ───────────────────────────────
	type agg struct {
		week        *threadpb.WeekResponse
		assignments []*threadpb.AssignmentWithSubmission
	}
	bucket := map[int64]*agg{}

	for _, r := range rows {
		ag, ok := bucket[r.WeekID]
		if !ok {
			ag = &agg{
				week: &threadpb.WeekResponse{
					Id:          r.WeekID,
					ThreadId:    r.ThreadID,
					WeekNumber:  uint32(r.WeekNumber),
					Type:        r.Type,
					Title:       r.WeekTitle,
					Description: r.WeekDescr,
					CreatedAt:   timestamppb.New(r.WeekCreated),
					UpdatedAt:   timestamppb.New(r.WeekUpdated),
				},
			}
			bucket[r.WeekID] = ag
		}

		// assignment может быть NULL
		if r.AssID != nil {
			ass := &assignmentpb.AssignmentResponse{
				Id:          *r.AssID,
				WeekId:      r.WeekID,
				Title:       ptrString(r.AssTitle),
				Description: ptrString(r.AssDescr),
				DueDate:     ptrTimestamp(r.AssDueDate),
				MaxPoints:   int32(ptrInt(r.AssMaxPoints)),
				Type:        ptrString(r.AssType),
			}

			// submission (может быть NULL)
			var sub *assignmentpb.AssignmentSubmissionResponse
			if r.SubID != nil {
				sub = &assignmentpb.AssignmentSubmissionResponse{
					Id:           *r.SubID,
					AssignmentId: *r.AssID,
					UserId:       req.GetUserId(),
					SubmittedAt:  ptrTimestamp(r.SubSubmitted),
					FileUrls:     r.SubFileURLs,
					Score:        ptrInt32(r.SubScore),
					Comment:      ptrString(r.SubComment),
					Feedback:     ptrString(r.SubFeedback),
					CreatedAt:    ptrTimestamp(r.SubCreated),
					UpdatedAt:    ptrTimestamp(r.SubUpdated),
				}
			}

			ag.assignments = append(ag.assignments,
				&threadpb.AssignmentWithSubmission{
					Assignment: ass,
					Submission: sub,
				})
		}
	}

	// ── формируем ответ ────────────────────────────────────
	out := &threadpb.WeeksWithHwResponse{}
	for _, ag := range bucket {
		out.Weeks = append(out.Weeks, &threadpb.WeekWithHw{
			Week:        ag.week,
			Assignments: ag.assignments,
		})
	}
	// (опционально) сортировка по WeekNumber, если важен порядок
	sort.Slice(out.Weeks, func(i, j int) bool {
		return out.Weeks[i].Week.WeekNumber < out.Weeks[j].Week.WeekNumber
	})

	return out, nil
}

/* ===== helpers for nil‑safe convert ===== */
func ptrString(p *string) string {
	if p == nil {
		return ""
	}
	return *p
}
func ptrInt(p *int) int32 {
	if p == nil {
		return 0
	}
	return int32(*p)
}
func ptrInt32(p *int32) int32 {
	if p == nil {
		return 0
	}
	return *p
}
func ptrTimestamp(p *time.Time) *timestamppb.Timestamp {
	if p == nil {
		return nil
	}
	return timestamppb.New(*p)
}
