// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.2
// source: pb/semester/semester.proto

package semesterpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SemesterService_CreateSemester_FullMethodName      = "/semesterpb.SemesterService/CreateSemester"
	SemesterService_GetSemesterByID_FullMethodName     = "/semesterpb.SemesterService/GetSemesterByID"
	SemesterService_GetAllSemesters_FullMethodName     = "/semesterpb.SemesterService/GetAllSemesters"
	SemesterService_UpdateSemester_FullMethodName      = "/semesterpb.SemesterService/UpdateSemester"
	SemesterService_Delete_FullMethodName              = "/semesterpb.SemesterService/Delete"
	SemesterService_AddSemesterBreak_FullMethodName    = "/semesterpb.SemesterService/AddSemesterBreak"
	SemesterService_RemoveSemesterBreak_FullMethodName = "/semesterpb.SemesterService/RemoveSemesterBreak"
	SemesterService_ListSemesterBreaks_FullMethodName  = "/semesterpb.SemesterService/ListSemesterBreaks"
)

// SemesterServiceClient is the client API for SemesterService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SemesterServiceClient interface {
	// semester entity
	CreateSemester(ctx context.Context, in *SemesterRequest, opts ...grpc.CallOption) (*SemesterResponse, error)
	GetSemesterByID(ctx context.Context, in *SemesterByID, opts ...grpc.CallOption) (*SemesterResponse, error)
	GetAllSemesters(ctx context.Context, in *SemesterEmptyRequest, opts ...grpc.CallOption) (*SemestersResponse, error)
	UpdateSemester(ctx context.Context, in *SemesterUpdateRequest, opts ...grpc.CallOption) (*SemesterResponse, error)
	Delete(ctx context.Context, in *SemesterByID, opts ...grpc.CallOption) (*SemesterEmptyResponse, error)
	AddSemesterBreak(ctx context.Context, in *SemesterBreakRequest, opts ...grpc.CallOption) (*SemesterBreakResponse, error)
	RemoveSemesterBreak(ctx context.Context, in *SemesterBreakByID, opts ...grpc.CallOption) (*SemesterBreakResponse, error)
	ListSemesterBreaks(ctx context.Context, in *SemesterByID, opts ...grpc.CallOption) (*SemesterBreaksResponse, error)
}

type semesterServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSemesterServiceClient(cc grpc.ClientConnInterface) SemesterServiceClient {
	return &semesterServiceClient{cc}
}

func (c *semesterServiceClient) CreateSemester(ctx context.Context, in *SemesterRequest, opts ...grpc.CallOption) (*SemesterResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SemesterResponse)
	err := c.cc.Invoke(ctx, SemesterService_CreateSemester_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterServiceClient) GetSemesterByID(ctx context.Context, in *SemesterByID, opts ...grpc.CallOption) (*SemesterResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SemesterResponse)
	err := c.cc.Invoke(ctx, SemesterService_GetSemesterByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterServiceClient) GetAllSemesters(ctx context.Context, in *SemesterEmptyRequest, opts ...grpc.CallOption) (*SemestersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SemestersResponse)
	err := c.cc.Invoke(ctx, SemesterService_GetAllSemesters_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterServiceClient) UpdateSemester(ctx context.Context, in *SemesterUpdateRequest, opts ...grpc.CallOption) (*SemesterResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SemesterResponse)
	err := c.cc.Invoke(ctx, SemesterService_UpdateSemester_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterServiceClient) Delete(ctx context.Context, in *SemesterByID, opts ...grpc.CallOption) (*SemesterEmptyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SemesterEmptyResponse)
	err := c.cc.Invoke(ctx, SemesterService_Delete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterServiceClient) AddSemesterBreak(ctx context.Context, in *SemesterBreakRequest, opts ...grpc.CallOption) (*SemesterBreakResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SemesterBreakResponse)
	err := c.cc.Invoke(ctx, SemesterService_AddSemesterBreak_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterServiceClient) RemoveSemesterBreak(ctx context.Context, in *SemesterBreakByID, opts ...grpc.CallOption) (*SemesterBreakResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SemesterBreakResponse)
	err := c.cc.Invoke(ctx, SemesterService_RemoveSemesterBreak_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *semesterServiceClient) ListSemesterBreaks(ctx context.Context, in *SemesterByID, opts ...grpc.CallOption) (*SemesterBreaksResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SemesterBreaksResponse)
	err := c.cc.Invoke(ctx, SemesterService_ListSemesterBreaks_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SemesterServiceServer is the server API for SemesterService service.
// All implementations must embed UnimplementedSemesterServiceServer
// for forward compatibility.
type SemesterServiceServer interface {
	// semester entity
	CreateSemester(context.Context, *SemesterRequest) (*SemesterResponse, error)
	GetSemesterByID(context.Context, *SemesterByID) (*SemesterResponse, error)
	GetAllSemesters(context.Context, *SemesterEmptyRequest) (*SemestersResponse, error)
	UpdateSemester(context.Context, *SemesterUpdateRequest) (*SemesterResponse, error)
	Delete(context.Context, *SemesterByID) (*SemesterEmptyResponse, error)
	AddSemesterBreak(context.Context, *SemesterBreakRequest) (*SemesterBreakResponse, error)
	RemoveSemesterBreak(context.Context, *SemesterBreakByID) (*SemesterBreakResponse, error)
	ListSemesterBreaks(context.Context, *SemesterByID) (*SemesterBreaksResponse, error)
	mustEmbedUnimplementedSemesterServiceServer()
}

// UnimplementedSemesterServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSemesterServiceServer struct{}

func (UnimplementedSemesterServiceServer) CreateSemester(context.Context, *SemesterRequest) (*SemesterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSemester not implemented")
}
func (UnimplementedSemesterServiceServer) GetSemesterByID(context.Context, *SemesterByID) (*SemesterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSemesterByID not implemented")
}
func (UnimplementedSemesterServiceServer) GetAllSemesters(context.Context, *SemesterEmptyRequest) (*SemestersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllSemesters not implemented")
}
func (UnimplementedSemesterServiceServer) UpdateSemester(context.Context, *SemesterUpdateRequest) (*SemesterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSemester not implemented")
}
func (UnimplementedSemesterServiceServer) Delete(context.Context, *SemesterByID) (*SemesterEmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedSemesterServiceServer) AddSemesterBreak(context.Context, *SemesterBreakRequest) (*SemesterBreakResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddSemesterBreak not implemented")
}
func (UnimplementedSemesterServiceServer) RemoveSemesterBreak(context.Context, *SemesterBreakByID) (*SemesterBreakResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveSemesterBreak not implemented")
}
func (UnimplementedSemesterServiceServer) ListSemesterBreaks(context.Context, *SemesterByID) (*SemesterBreaksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSemesterBreaks not implemented")
}
func (UnimplementedSemesterServiceServer) mustEmbedUnimplementedSemesterServiceServer() {}
func (UnimplementedSemesterServiceServer) testEmbeddedByValue()                         {}

// UnsafeSemesterServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SemesterServiceServer will
// result in compilation errors.
type UnsafeSemesterServiceServer interface {
	mustEmbedUnimplementedSemesterServiceServer()
}

func RegisterSemesterServiceServer(s grpc.ServiceRegistrar, srv SemesterServiceServer) {
	// If the following call pancis, it indicates UnimplementedSemesterServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SemesterService_ServiceDesc, srv)
}

func _SemesterService_CreateSemester_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SemesterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterServiceServer).CreateSemester(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterService_CreateSemester_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterServiceServer).CreateSemester(ctx, req.(*SemesterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterService_GetSemesterByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SemesterByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterServiceServer).GetSemesterByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterService_GetSemesterByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterServiceServer).GetSemesterByID(ctx, req.(*SemesterByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterService_GetAllSemesters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SemesterEmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterServiceServer).GetAllSemesters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterService_GetAllSemesters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterServiceServer).GetAllSemesters(ctx, req.(*SemesterEmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterService_UpdateSemester_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SemesterUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterServiceServer).UpdateSemester(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterService_UpdateSemester_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterServiceServer).UpdateSemester(ctx, req.(*SemesterUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterService_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SemesterByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterServiceServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterService_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterServiceServer).Delete(ctx, req.(*SemesterByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterService_AddSemesterBreak_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SemesterBreakRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterServiceServer).AddSemesterBreak(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterService_AddSemesterBreak_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterServiceServer).AddSemesterBreak(ctx, req.(*SemesterBreakRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterService_RemoveSemesterBreak_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SemesterBreakByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterServiceServer).RemoveSemesterBreak(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterService_RemoveSemesterBreak_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterServiceServer).RemoveSemesterBreak(ctx, req.(*SemesterBreakByID))
	}
	return interceptor(ctx, in, info, handler)
}

func _SemesterService_ListSemesterBreaks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SemesterByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SemesterServiceServer).ListSemesterBreaks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SemesterService_ListSemesterBreaks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SemesterServiceServer).ListSemesterBreaks(ctx, req.(*SemesterByID))
	}
	return interceptor(ctx, in, info, handler)
}

// SemesterService_ServiceDesc is the grpc.ServiceDesc for SemesterService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SemesterService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "semesterpb.SemesterService",
	HandlerType: (*SemesterServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSemester",
			Handler:    _SemesterService_CreateSemester_Handler,
		},
		{
			MethodName: "GetSemesterByID",
			Handler:    _SemesterService_GetSemesterByID_Handler,
		},
		{
			MethodName: "GetAllSemesters",
			Handler:    _SemesterService_GetAllSemesters_Handler,
		},
		{
			MethodName: "UpdateSemester",
			Handler:    _SemesterService_UpdateSemester_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _SemesterService_Delete_Handler,
		},
		{
			MethodName: "AddSemesterBreak",
			Handler:    _SemesterService_AddSemesterBreak_Handler,
		},
		{
			MethodName: "RemoveSemesterBreak",
			Handler:    _SemesterService_RemoveSemesterBreak_Handler,
		},
		{
			MethodName: "ListSemesterBreaks",
			Handler:    _SemesterService_ListSemesterBreaks_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/semester/semester.proto",
}
