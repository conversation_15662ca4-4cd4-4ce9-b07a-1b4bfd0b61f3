// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/physical_education.proto

package sportpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Get available sport types request
type GetAvailableSportTypesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAvailableSportTypesRequest) Reset() {
	*x = GetAvailableSportTypesRequest{}
	mi := &file_proto_physical_education_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAvailableSportTypesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableSportTypesRequest) ProtoMessage() {}

func (x *GetAvailableSportTypesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_physical_education_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableSportTypesRequest.ProtoReflect.Descriptor instead.
func (*GetAvailableSportTypesRequest) Descriptor() ([]byte, []int) {
	return file_proto_physical_education_proto_rawDescGZIP(), []int{0}
}

func (x *GetAvailableSportTypesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAvailableSportTypesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Get available facilities request
type GetAvailableFacilitiesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAvailableFacilitiesRequest) Reset() {
	*x = GetAvailableFacilitiesRequest{}
	mi := &file_proto_physical_education_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAvailableFacilitiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableFacilitiesRequest) ProtoMessage() {}

func (x *GetAvailableFacilitiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_physical_education_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableFacilitiesRequest.ProtoReflect.Descriptor instead.
func (*GetAvailableFacilitiesRequest) Descriptor() ([]byte, []int) {
	return file_proto_physical_education_proto_rawDescGZIP(), []int{1}
}

func (x *GetAvailableFacilitiesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAvailableFacilitiesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Get available schedules request
type GetAvailableSchedulesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FacilityId    int64                  `protobuf:"varint,1,opt,name=facility_id,json=facilityId,proto3" json:"facility_id,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Page          int32                  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAvailableSchedulesRequest) Reset() {
	*x = GetAvailableSchedulesRequest{}
	mi := &file_proto_physical_education_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAvailableSchedulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableSchedulesRequest) ProtoMessage() {}

func (x *GetAvailableSchedulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_physical_education_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableSchedulesRequest.ProtoReflect.Descriptor instead.
func (*GetAvailableSchedulesRequest) Descriptor() ([]byte, []int) {
	return file_proto_physical_education_proto_rawDescGZIP(), []int{2}
}

func (x *GetAvailableSchedulesRequest) GetFacilityId() int64 {
	if x != nil {
		return x.FacilityId
	}
	return 0
}

func (x *GetAvailableSchedulesRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetAvailableSchedulesRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *GetAvailableSchedulesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAvailableSchedulesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Book session request
type BookSessionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ScheduleId    int64                  `protobuf:"varint,2,opt,name=schedule_id,json=scheduleId,proto3" json:"schedule_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BookSessionRequest) Reset() {
	*x = BookSessionRequest{}
	mi := &file_proto_physical_education_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BookSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookSessionRequest) ProtoMessage() {}

func (x *BookSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_physical_education_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookSessionRequest.ProtoReflect.Descriptor instead.
func (*BookSessionRequest) Descriptor() ([]byte, []int) {
	return file_proto_physical_education_proto_rawDescGZIP(), []int{3}
}

func (x *BookSessionRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *BookSessionRequest) GetScheduleId() int64 {
	if x != nil {
		return x.ScheduleId
	}
	return 0
}

// Get user bookings request
type GetUserBookingsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Page          int32                  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserBookingsRequest) Reset() {
	*x = GetUserBookingsRequest{}
	mi := &file_proto_physical_education_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserBookingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserBookingsRequest) ProtoMessage() {}

func (x *GetUserBookingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_physical_education_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserBookingsRequest.ProtoReflect.Descriptor instead.
func (*GetUserBookingsRequest) Descriptor() ([]byte, []int) {
	return file_proto_physical_education_proto_rawDescGZIP(), []int{4}
}

func (x *GetUserBookingsRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetUserBookingsRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetUserBookingsRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *GetUserBookingsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetUserBookingsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Check user can book LFK request
type CheckUserCanBookLFKRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUserCanBookLFKRequest) Reset() {
	*x = CheckUserCanBookLFKRequest{}
	mi := &file_proto_physical_education_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUserCanBookLFKRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserCanBookLFKRequest) ProtoMessage() {}

func (x *CheckUserCanBookLFKRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_physical_education_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserCanBookLFKRequest.ProtoReflect.Descriptor instead.
func (*CheckUserCanBookLFKRequest) Descriptor() ([]byte, []int) {
	return file_proto_physical_education_proto_rawDescGZIP(), []int{5}
}

func (x *CheckUserCanBookLFKRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// Check user can book LFK response
type CheckUserCanBookLFKResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CanBook       bool                   `protobuf:"varint,1,opt,name=can_book,json=canBook,proto3" json:"can_book,omitempty"`
	Reason        string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUserCanBookLFKResponse) Reset() {
	*x = CheckUserCanBookLFKResponse{}
	mi := &file_proto_physical_education_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUserCanBookLFKResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserCanBookLFKResponse) ProtoMessage() {}

func (x *CheckUserCanBookLFKResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_physical_education_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserCanBookLFKResponse.ProtoReflect.Descriptor instead.
func (*CheckUserCanBookLFKResponse) Descriptor() ([]byte, []int) {
	return file_proto_physical_education_proto_rawDescGZIP(), []int{6}
}

func (x *CheckUserCanBookLFKResponse) GetCanBook() bool {
	if x != nil {
		return x.CanBook
	}
	return false
}

func (x *CheckUserCanBookLFKResponse) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

var File_proto_physical_education_proto protoreflect.FileDescriptor

const file_proto_physical_education_proto_rawDesc = "" +
	"\n" +
	"\x1eproto/physical_education.proto\x12\asportpb\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x16proto/sport_type.proto\x1a\x14proto/facility.proto\x1a\x14proto/schedule.proto\x1a\x13proto/booking.proto\x1a\x1fproto/medical_certificate.proto\x1a\x1aproto/semester_limit.proto\"P\n" +
	"\x1dGetAvailableSportTypesRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\"P\n" +
	"\x1dGetAvailableFacilitiesRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\"\xe2\x01\n" +
	"\x1cGetAvailableSchedulesRequest\x12\x1f\n" +
	"\vfacility_id\x18\x01 \x01(\x03R\n" +
	"facilityId\x129\n" +
	"\n" +
	"start_date\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\x12\x12\n" +
	"\x04page\x18\x04 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x05 \x01(\x05R\bpageSize\"N\n" +
	"\x12BookSessionRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1f\n" +
	"\vschedule_id\x18\x02 \x01(\x03R\n" +
	"scheduleId\"\xd4\x01\n" +
	"\x16GetUserBookingsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x129\n" +
	"\n" +
	"start_date\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\x12\x12\n" +
	"\x04page\x18\x04 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x05 \x01(\x05R\bpageSize\"5\n" +
	"\x1aCheckUserCanBookLFKRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\"P\n" +
	"\x1bCheckUserCanBookLFKResponse\x12\x19\n" +
	"\bcan_book\x18\x01 \x01(\bR\acanBook\x12\x16\n" +
	"\x06reason\x18\x02 \x01(\tR\x06reason2\xb2\b\n" +
	"\x18PhysicalEducationService\x12c\n" +
	"\x16GetAvailableSportTypes\x12&.sportpb.GetAvailableSportTypesRequest\x1a\x1f.sportpb.ListSportTypesResponse\"\x00\x12c\n" +
	"\x16GetAvailableFacilities\x12&.sportpb.GetAvailableFacilitiesRequest\x1a\x1f.sportpb.ListFacilitiesResponse\"\x00\x12`\n" +
	"\x15GetAvailableSchedules\x12%.sportpb.GetAvailableSchedulesRequest\x1a\x1e.sportpb.ListSchedulesResponse\"\x00\x12F\n" +
	"\vBookSession\x12\x1b.sportpb.BookSessionRequest\x1a\x18.sportpb.BookingResponse\"\x00\x12J\n" +
	"\rCancelBooking\x12\x1d.sportpb.CancelBookingRequest\x1a\x18.sportpb.BookingResponse\"\x00\x12d\n" +
	"\x18UploadMedicalCertificate\x12!.sportpb.UploadCertificateRequest\x1a#.sportpb.MedicalCertificateResponse\"\x00\x12S\n" +
	"\x0fGetUserBookings\x12\x1f.sportpb.GetUserBookingsRequest\x1a\x1d.sportpb.ListBookingsResponse\"\x00\x12b\n" +
	"\x14GetUserSemesterStats\x12$.sportpb.GetUserSemesterStatsRequest\x1a\".sportpb.UserSemesterStatsResponse\"\x00\x12b\n" +
	"\x13CheckUserCanBookLFK\x12#.sportpb.CheckUserCanBookLFKRequest\x1a$.sportpb.CheckUserCanBookLFKResponse\"\x00\x12e\n" +
	"\x15GetSemesterSportLimit\x12*.sportpb.GetSemesterLimitBySemesterRequest\x1a\x1e.sportpb.SemesterLimitResponse\"\x00\x12l\n" +
	"\x14GetDailyBookingLimit\x12..sportpb.GetDailyBookingLimitBySemesterRequest\x1a\".sportpb.DailyBookingLimitResponse\"\x00B<Z:github.com/olzzhas/edunite-server/sport_service/pb/sportpbb\x06proto3"

var (
	file_proto_physical_education_proto_rawDescOnce sync.Once
	file_proto_physical_education_proto_rawDescData []byte
)

func file_proto_physical_education_proto_rawDescGZIP() []byte {
	file_proto_physical_education_proto_rawDescOnce.Do(func() {
		file_proto_physical_education_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_physical_education_proto_rawDesc), len(file_proto_physical_education_proto_rawDesc)))
	})
	return file_proto_physical_education_proto_rawDescData
}

var file_proto_physical_education_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_proto_physical_education_proto_goTypes = []any{
	(*GetAvailableSportTypesRequest)(nil),         // 0: sportpb.GetAvailableSportTypesRequest
	(*GetAvailableFacilitiesRequest)(nil),         // 1: sportpb.GetAvailableFacilitiesRequest
	(*GetAvailableSchedulesRequest)(nil),          // 2: sportpb.GetAvailableSchedulesRequest
	(*BookSessionRequest)(nil),                    // 3: sportpb.BookSessionRequest
	(*GetUserBookingsRequest)(nil),                // 4: sportpb.GetUserBookingsRequest
	(*CheckUserCanBookLFKRequest)(nil),            // 5: sportpb.CheckUserCanBookLFKRequest
	(*CheckUserCanBookLFKResponse)(nil),           // 6: sportpb.CheckUserCanBookLFKResponse
	(*timestamppb.Timestamp)(nil),                 // 7: google.protobuf.Timestamp
	(*CancelBookingRequest)(nil),                  // 8: sportpb.CancelBookingRequest
	(*UploadCertificateRequest)(nil),              // 9: sportpb.UploadCertificateRequest
	(*GetUserSemesterStatsRequest)(nil),           // 10: sportpb.GetUserSemesterStatsRequest
	(*GetSemesterLimitBySemesterRequest)(nil),     // 11: sportpb.GetSemesterLimitBySemesterRequest
	(*GetDailyBookingLimitBySemesterRequest)(nil), // 12: sportpb.GetDailyBookingLimitBySemesterRequest
	(*ListSportTypesResponse)(nil),                // 13: sportpb.ListSportTypesResponse
	(*ListFacilitiesResponse)(nil),                // 14: sportpb.ListFacilitiesResponse
	(*ListSchedulesResponse)(nil),                 // 15: sportpb.ListSchedulesResponse
	(*BookingResponse)(nil),                       // 16: sportpb.BookingResponse
	(*MedicalCertificateResponse)(nil),            // 17: sportpb.MedicalCertificateResponse
	(*ListBookingsResponse)(nil),                  // 18: sportpb.ListBookingsResponse
	(*UserSemesterStatsResponse)(nil),             // 19: sportpb.UserSemesterStatsResponse
	(*SemesterLimitResponse)(nil),                 // 20: sportpb.SemesterLimitResponse
	(*DailyBookingLimitResponse)(nil),             // 21: sportpb.DailyBookingLimitResponse
}
var file_proto_physical_education_proto_depIdxs = []int32{
	7,  // 0: sportpb.GetAvailableSchedulesRequest.start_date:type_name -> google.protobuf.Timestamp
	7,  // 1: sportpb.GetAvailableSchedulesRequest.end_date:type_name -> google.protobuf.Timestamp
	7,  // 2: sportpb.GetUserBookingsRequest.start_date:type_name -> google.protobuf.Timestamp
	7,  // 3: sportpb.GetUserBookingsRequest.end_date:type_name -> google.protobuf.Timestamp
	0,  // 4: sportpb.PhysicalEducationService.GetAvailableSportTypes:input_type -> sportpb.GetAvailableSportTypesRequest
	1,  // 5: sportpb.PhysicalEducationService.GetAvailableFacilities:input_type -> sportpb.GetAvailableFacilitiesRequest
	2,  // 6: sportpb.PhysicalEducationService.GetAvailableSchedules:input_type -> sportpb.GetAvailableSchedulesRequest
	3,  // 7: sportpb.PhysicalEducationService.BookSession:input_type -> sportpb.BookSessionRequest
	8,  // 8: sportpb.PhysicalEducationService.CancelBooking:input_type -> sportpb.CancelBookingRequest
	9,  // 9: sportpb.PhysicalEducationService.UploadMedicalCertificate:input_type -> sportpb.UploadCertificateRequest
	4,  // 10: sportpb.PhysicalEducationService.GetUserBookings:input_type -> sportpb.GetUserBookingsRequest
	10, // 11: sportpb.PhysicalEducationService.GetUserSemesterStats:input_type -> sportpb.GetUserSemesterStatsRequest
	5,  // 12: sportpb.PhysicalEducationService.CheckUserCanBookLFK:input_type -> sportpb.CheckUserCanBookLFKRequest
	11, // 13: sportpb.PhysicalEducationService.GetSemesterSportLimit:input_type -> sportpb.GetSemesterLimitBySemesterRequest
	12, // 14: sportpb.PhysicalEducationService.GetDailyBookingLimit:input_type -> sportpb.GetDailyBookingLimitBySemesterRequest
	13, // 15: sportpb.PhysicalEducationService.GetAvailableSportTypes:output_type -> sportpb.ListSportTypesResponse
	14, // 16: sportpb.PhysicalEducationService.GetAvailableFacilities:output_type -> sportpb.ListFacilitiesResponse
	15, // 17: sportpb.PhysicalEducationService.GetAvailableSchedules:output_type -> sportpb.ListSchedulesResponse
	16, // 18: sportpb.PhysicalEducationService.BookSession:output_type -> sportpb.BookingResponse
	16, // 19: sportpb.PhysicalEducationService.CancelBooking:output_type -> sportpb.BookingResponse
	17, // 20: sportpb.PhysicalEducationService.UploadMedicalCertificate:output_type -> sportpb.MedicalCertificateResponse
	18, // 21: sportpb.PhysicalEducationService.GetUserBookings:output_type -> sportpb.ListBookingsResponse
	19, // 22: sportpb.PhysicalEducationService.GetUserSemesterStats:output_type -> sportpb.UserSemesterStatsResponse
	6,  // 23: sportpb.PhysicalEducationService.CheckUserCanBookLFK:output_type -> sportpb.CheckUserCanBookLFKResponse
	20, // 24: sportpb.PhysicalEducationService.GetSemesterSportLimit:output_type -> sportpb.SemesterLimitResponse
	21, // 25: sportpb.PhysicalEducationService.GetDailyBookingLimit:output_type -> sportpb.DailyBookingLimitResponse
	15, // [15:26] is the sub-list for method output_type
	4,  // [4:15] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_proto_physical_education_proto_init() }
func file_proto_physical_education_proto_init() {
	if File_proto_physical_education_proto != nil {
		return
	}
	file_proto_sport_type_proto_init()
	file_proto_facility_proto_init()
	file_proto_schedule_proto_init()
	file_proto_booking_proto_init()
	file_proto_medical_certificate_proto_init()
	file_proto_semester_limit_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_physical_education_proto_rawDesc), len(file_proto_physical_education_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_physical_education_proto_goTypes,
		DependencyIndexes: file_proto_physical_education_proto_depIdxs,
		MessageInfos:      file_proto_physical_education_proto_msgTypes,
	}.Build()
	File_proto_physical_education_proto = out.File
	file_proto_physical_education_proto_goTypes = nil
	file_proto_physical_education_proto_depIdxs = nil
}
