syntax = "proto3";

package sportpb;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

option go_package = "github.com/olzzhas/edunite-server/sport_service/pb/sportpb";

service FacilityService {
  // Create a new facility
  rpc CreateFacility(CreateFacilityRequest) returns (FacilityResponse) {}
  
  // Get a facility by ID
  rpc GetFacility(GetFacilityRequest) returns (FacilityResponse) {}
  
  // Update an existing facility
  rpc UpdateFacility(UpdateFacilityRequest) returns (FacilityResponse) {}
  
  // Delete a facility
  rpc DeleteFacility(DeleteFacilityRequest) returns (google.protobuf.Empty) {}
  
  // List facilities with filtering
  rpc ListFacilities(ListFacilitiesRequest) returns (ListFacilitiesResponse) {}
}

// Create facility request
message CreateFacilityRequest {
  string title = 1;
  string description = 2;
  int32 max_capacity = 3;
}

// Get facility request
message GetFacilityRequest {
  int64 id = 1;
}

// Update facility request
message UpdateFacilityRequest {
  int64 id = 1;
  string title = 2;
  string description = 3;
  int32 max_capacity = 4;
  int32 version = 5;
}

// Delete facility request
message DeleteFacilityRequest {
  int64 id = 1;
}

// List facilities request
message ListFacilitiesRequest {
  string title = 1;
  int32 page = 2;
  int32 page_size = 3;
}

// Facility response
message FacilityResponse {
  int64 id = 1;
  string title = 2;
  string description = 3;
  int32 max_capacity = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  int32 version = 7;
}

// List facilities response
message ListFacilitiesResponse {
  repeated FacilityResponse facilities = 1;
  int32 total = 2;
  int32 page = 3;
  int32 page_size = 4;
}
