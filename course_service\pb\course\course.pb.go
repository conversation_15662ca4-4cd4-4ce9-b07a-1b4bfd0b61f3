// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.2
// source: pb/course/course.proto

package coursepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// -------------------------------//
//
//	Course Messages        //
//
// -------------------------------//
type CreateCourseRequest struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Title                 string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Description           string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	BannerImageUrl        string                 `protobuf:"bytes,3,opt,name=banner_image_url,json=bannerImageUrl,proto3" json:"banner_image_url,omitempty"`
	PrerequisiteCourseIds []int64                `protobuf:"varint,4,rep,packed,name=prerequisite_course_ids,json=prerequisiteCourseIds,proto3" json:"prerequisite_course_ids,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *CreateCourseRequest) Reset() {
	*x = CreateCourseRequest{}
	mi := &file_pb_course_course_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCourseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCourseRequest) ProtoMessage() {}

func (x *CreateCourseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_course_course_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCourseRequest.ProtoReflect.Descriptor instead.
func (*CreateCourseRequest) Descriptor() ([]byte, []int) {
	return file_pb_course_course_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCourseRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CreateCourseRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateCourseRequest) GetBannerImageUrl() string {
	if x != nil {
		return x.BannerImageUrl
	}
	return ""
}

func (x *CreateCourseRequest) GetPrerequisiteCourseIds() []int64 {
	if x != nil {
		return x.PrerequisiteCourseIds
	}
	return nil
}

type GetCourseByIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCourseByIDRequest) Reset() {
	*x = GetCourseByIDRequest{}
	mi := &file_pb_course_course_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCourseByIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCourseByIDRequest) ProtoMessage() {}

func (x *GetCourseByIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_course_course_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCourseByIDRequest.ProtoReflect.Descriptor instead.
func (*GetCourseByIDRequest) Descriptor() ([]byte, []int) {
	return file_pb_course_course_proto_rawDescGZIP(), []int{1}
}

func (x *GetCourseByIDRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateCourseByIDRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title          string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description    string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	BannerImageUrl string                 `protobuf:"bytes,4,opt,name=banner_image_url,json=bannerImageUrl,proto3" json:"banner_image_url,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UpdateCourseByIDRequest) Reset() {
	*x = UpdateCourseByIDRequest{}
	mi := &file_pb_course_course_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCourseByIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCourseByIDRequest) ProtoMessage() {}

func (x *UpdateCourseByIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_course_course_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCourseByIDRequest.ProtoReflect.Descriptor instead.
func (*UpdateCourseByIDRequest) Descriptor() ([]byte, []int) {
	return file_pb_course_course_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateCourseByIDRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCourseByIDRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdateCourseByIDRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateCourseByIDRequest) GetBannerImageUrl() string {
	if x != nil {
		return x.BannerImageUrl
	}
	return ""
}

type DeleteCourseByIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCourseByIDRequest) Reset() {
	*x = DeleteCourseByIDRequest{}
	mi := &file_pb_course_course_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCourseByIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCourseByIDRequest) ProtoMessage() {}

func (x *DeleteCourseByIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_course_course_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCourseByIDRequest.ProtoReflect.Descriptor instead.
func (*DeleteCourseByIDRequest) Descriptor() ([]byte, []int) {
	return file_pb_course_course_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteCourseByIDRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type CourseResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title          string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description    string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	BannerImageUrl string                 `protobuf:"bytes,4,opt,name=banner_image_url,json=bannerImageUrl,proto3" json:"banner_image_url,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CourseResponse) Reset() {
	*x = CourseResponse{}
	mi := &file_pb_course_course_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CourseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CourseResponse) ProtoMessage() {}

func (x *CourseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_course_course_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CourseResponse.ProtoReflect.Descriptor instead.
func (*CourseResponse) Descriptor() ([]byte, []int) {
	return file_pb_course_course_proto_rawDescGZIP(), []int{4}
}

func (x *CourseResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CourseResponse) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CourseResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CourseResponse) GetBannerImageUrl() string {
	if x != nil {
		return x.BannerImageUrl
	}
	return ""
}

func (x *CourseResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CourseResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type CoursesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Courses       []*CourseResponse      `protobuf:"bytes,1,rep,name=courses,proto3" json:"courses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CoursesResponse) Reset() {
	*x = CoursesResponse{}
	mi := &file_pb_course_course_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CoursesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoursesResponse) ProtoMessage() {}

func (x *CoursesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_course_course_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoursesResponse.ProtoReflect.Descriptor instead.
func (*CoursesResponse) Descriptor() ([]byte, []int) {
	return file_pb_course_course_proto_rawDescGZIP(), []int{5}
}

func (x *CoursesResponse) GetCourses() []*CourseResponse {
	if x != nil {
		return x.Courses
	}
	return nil
}

// -------------------------------//
//
//	Empty Messages          //
//
// -------------------------------//
type EmptyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmptyRequest) Reset() {
	*x = EmptyRequest{}
	mi := &file_pb_course_course_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRequest) ProtoMessage() {}

func (x *EmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_course_course_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRequest.ProtoReflect.Descriptor instead.
func (*EmptyRequest) Descriptor() ([]byte, []int) {
	return file_pb_course_course_proto_rawDescGZIP(), []int{6}
}

type EmptyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	mi := &file_pb_course_course_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_course_course_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_pb_course_course_proto_rawDescGZIP(), []int{7}
}

var File_pb_course_course_proto protoreflect.FileDescriptor

const file_pb_course_course_proto_rawDesc = "" +
	"\n" +
	"\x16pb/course/course.proto\x12\bcoursepb\x1a\x1fgoogle/protobuf/timestamp.proto\"\xaf\x01\n" +
	"\x13CreateCourseRequest\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12(\n" +
	"\x10banner_image_url\x18\x03 \x01(\tR\x0ebannerImageUrl\x126\n" +
	"\x17prerequisite_course_ids\x18\x04 \x03(\x03R\x15prerequisiteCourseIds\"&\n" +
	"\x14GetCourseByIDRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\x8b\x01\n" +
	"\x17UpdateCourseByIDRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12(\n" +
	"\x10banner_image_url\x18\x04 \x01(\tR\x0ebannerImageUrl\")\n" +
	"\x17DeleteCourseByIDRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\xf8\x01\n" +
	"\x0eCourseResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12(\n" +
	"\x10banner_image_url\x18\x04 \x01(\tR\x0ebannerImageUrl\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"E\n" +
	"\x0fCoursesResponse\x122\n" +
	"\acourses\x18\x01 \x03(\v2\x18.coursepb.CourseResponseR\acourses\"\x0e\n" +
	"\fEmptyRequest\"\x0f\n" +
	"\rEmptyResponse2\x8a\x03\n" +
	"\rCourseService\x12I\n" +
	"\fCreateCourse\x12\x1d.coursepb.CreateCourseRequest\x1a\x18.coursepb.CourseResponse\"\x00\x12K\n" +
	"\rGetCourseByID\x12\x1e.coursepb.GetCourseByIDRequest\x1a\x18.coursepb.CourseResponse\"\x00\x12D\n" +
	"\rGetAllCourses\x12\x16.coursepb.EmptyRequest\x1a\x19.coursepb.CoursesResponse\"\x00\x12M\n" +
	"\fUpdateCourse\x12!.coursepb.UpdateCourseByIDRequest\x1a\x18.coursepb.CourseResponse\"\x00\x12L\n" +
	"\fDeleteCourse\x12!.coursepb.DeleteCourseByIDRequest\x1a\x17.coursepb.EmptyResponse\"\x00BEZCgithub.com/olzzhas/edunite-server/course_service/pb/course;coursepbb\x06proto3"

var (
	file_pb_course_course_proto_rawDescOnce sync.Once
	file_pb_course_course_proto_rawDescData []byte
)

func file_pb_course_course_proto_rawDescGZIP() []byte {
	file_pb_course_course_proto_rawDescOnce.Do(func() {
		file_pb_course_course_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pb_course_course_proto_rawDesc), len(file_pb_course_course_proto_rawDesc)))
	})
	return file_pb_course_course_proto_rawDescData
}

var file_pb_course_course_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_pb_course_course_proto_goTypes = []any{
	(*CreateCourseRequest)(nil),     // 0: coursepb.CreateCourseRequest
	(*GetCourseByIDRequest)(nil),    // 1: coursepb.GetCourseByIDRequest
	(*UpdateCourseByIDRequest)(nil), // 2: coursepb.UpdateCourseByIDRequest
	(*DeleteCourseByIDRequest)(nil), // 3: coursepb.DeleteCourseByIDRequest
	(*CourseResponse)(nil),          // 4: coursepb.CourseResponse
	(*CoursesResponse)(nil),         // 5: coursepb.CoursesResponse
	(*EmptyRequest)(nil),            // 6: coursepb.EmptyRequest
	(*EmptyResponse)(nil),           // 7: coursepb.EmptyResponse
	(*timestamppb.Timestamp)(nil),   // 8: google.protobuf.Timestamp
}
var file_pb_course_course_proto_depIdxs = []int32{
	8, // 0: coursepb.CourseResponse.created_at:type_name -> google.protobuf.Timestamp
	8, // 1: coursepb.CourseResponse.updated_at:type_name -> google.protobuf.Timestamp
	4, // 2: coursepb.CoursesResponse.courses:type_name -> coursepb.CourseResponse
	0, // 3: coursepb.CourseService.CreateCourse:input_type -> coursepb.CreateCourseRequest
	1, // 4: coursepb.CourseService.GetCourseByID:input_type -> coursepb.GetCourseByIDRequest
	6, // 5: coursepb.CourseService.GetAllCourses:input_type -> coursepb.EmptyRequest
	2, // 6: coursepb.CourseService.UpdateCourse:input_type -> coursepb.UpdateCourseByIDRequest
	3, // 7: coursepb.CourseService.DeleteCourse:input_type -> coursepb.DeleteCourseByIDRequest
	4, // 8: coursepb.CourseService.CreateCourse:output_type -> coursepb.CourseResponse
	4, // 9: coursepb.CourseService.GetCourseByID:output_type -> coursepb.CourseResponse
	5, // 10: coursepb.CourseService.GetAllCourses:output_type -> coursepb.CoursesResponse
	4, // 11: coursepb.CourseService.UpdateCourse:output_type -> coursepb.CourseResponse
	7, // 12: coursepb.CourseService.DeleteCourse:output_type -> coursepb.EmptyResponse
	8, // [8:13] is the sub-list for method output_type
	3, // [3:8] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_pb_course_course_proto_init() }
func file_pb_course_course_proto_init() {
	if File_pb_course_course_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pb_course_course_proto_rawDesc), len(file_pb_course_course_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_course_course_proto_goTypes,
		DependencyIndexes: file_pb_course_course_proto_depIdxs,
		MessageInfos:      file_pb_course_course_proto_msgTypes,
	}.Build()
	File_pb_course_course_proto = out.File
	file_pb_course_course_proto_goTypes = nil
	file_pb_course_course_proto_depIdxs = nil
}
