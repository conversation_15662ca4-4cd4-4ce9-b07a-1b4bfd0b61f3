# Edunite API Documentation

This directory contains API documentation for the Edunite platform.

## API Endpoints

The API is organized into the following categories:

- **Auth**: Authentication operations
- **Users**: User management operations
- **Courses**: Course management operations
- **Threads**: Thread (course instance) operations
- **Schedules**: Thread schedule operations
- **Weeks**: Week management operations
- **Assignments**: Assignment management operations
- **Attendance**: Attendance tracking operations
- **Storage**: File storage operations
- **Semesters**: Semester management operations

## Thread Schedule API

The Thread Schedule API allows you to manage schedules for threads (course instances) in the Edunite platform.

### Basic Schedule Operations

- **Create Schedule**: `POST /thread/{thread_id}/schedules`
- **List Schedules**: `GET /thread/{thread_id}/schedules`
- **Get Schedule**: `GET /thread/schedules/{schedule_id}`
- **Update Schedule**: `PUT /thread/schedules/{schedule_id}`
- **Delete Schedule**: `DELETE /thread/schedules/{schedule_id}`

### Schedule Location Operations

The following endpoints are specifically for managing the location field of a schedule:

- **Get Schedule Location**: `GET /thread/schedules/{schedule_id}/location`
- **Update Schedule Location**: `PUT /thread/schedules/{schedule_id}/location`

#### Example: Get Schedule Location

Request:
```http
GET /thread/schedules/123/location
```

Response:
```json
{
  "location": "Room 101"
}
```

#### Example: Update Schedule Location

Request:
```http
PUT /thread/schedules/123/location
Content-Type: application/json

{
  "location": "Room 202"
}
```

Response:
```json
{
  "message": "schedule location updated successfully"
}
```

## Accessing the Swagger UI

The API is documented using the OpenAPI (Swagger) specification. You can access the interactive Swagger UI in several ways:

1. **Direct URL**: Navigate to `/docs/swagger-ui/` in your browser
2. **Swagger Redirect**: Navigate to `/swagger` in your browser
3. **Documentation Home**: Navigate to `/docs/` in your browser

The Swagger UI provides an interactive interface for exploring and testing the API endpoints.

## Integration Guide

### Client-Side Integration

When integrating with the Thread Schedule API, follow these steps:

1. Use the basic schedule operations for creating, listing, updating, and deleting schedules.
2. For managing the location field specifically, use the dedicated location endpoints.

### Example: Creating a Schedule with Location

Since the location field is not part of the main schedule creation endpoint, you need to:

1. Create the schedule first:
   ```javascript
   // Create schedule
   const scheduleData = {
     day_of_week: 1,
     start_time: "09:00:00",
     end_time: "10:30:00"
   };

   const response = await fetch(`/thread/1/schedules`, {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify(scheduleData)
   });

   const newSchedule = await response.json();
   const scheduleId = newSchedule.id;
   ```

2. Then set the location using the location-specific endpoint:
   ```javascript
   // Set location
   await fetch(`/thread/schedules/${scheduleId}/location`, {
     method: 'PUT',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({ location: "Room 101" })
   });
   ```

### Example: Updating a Schedule with Location

Similarly, when updating a schedule:

1. Update the main schedule properties:
   ```javascript
   // Update schedule
   const updateData = {
     day_of_week: 2,
     start_time: "10:00:00",
     end_time: "11:30:00"
   };

   await fetch(`/thread/schedules/${scheduleId}`, {
     method: 'PUT',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify(updateData)
   });
   ```

2. Update the location separately if needed:
   ```javascript
   // Update location
   await fetch(`/thread/schedules/${scheduleId}/location`, {
     method: 'PUT',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({ location: "Room 202" })
   });
   ```
