package database

import (
	"context"
	"fmt"
	"log"

	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/auth_service/internal/config"
)

// ConnectDBCockroach connects to the CockroachDB database
func ConnectDBCockroach(cfg *config.Config) *pgxpool.Pool {
	var connStr string
	if cfg.Database.URL != "" {
		connStr = cfg.Database.URL
	} else {
		connStr = fmt.Sprintf("postgres://%s:%s@%s:%s/%s",
			cfg.Database.User,
			cfg.Database.Password,
			cfg.Database.Host,
			cfg.Database.Port,
			cfg.Database.Name,
		)
	}

	dbpool, err := pgxpool.Connect(context.Background(), connStr)
	if err != nil {
		log.Fatalf("Unable to connect to database: %v", err)
	}

	// Verify connection
	if err := dbpool.Ping(context.Background()); err != nil {
		log.Fatalf("Unable to ping database: %v", err)
	}

	log.Println("Connected to CockroachDB successfully.")
	return dbpool
}
