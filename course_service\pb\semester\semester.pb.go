// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.2
// source: pb/semester/semester.proto

package semesterpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// -------------------------------//
//
//	Semester Messages       //
//
// -------------------------------//
type SemesterRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SemesterRequest) Reset() {
	*x = SemesterRequest{}
	mi := &file_pb_semester_semester_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SemesterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SemesterRequest) ProtoMessage() {}

func (x *SemesterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_semester_semester_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SemesterRequest.ProtoReflect.Descriptor instead.
func (*SemesterRequest) Descriptor() ([]byte, []int) {
	return file_pb_semester_semester_proto_rawDescGZIP(), []int{0}
}

func (x *SemesterRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SemesterRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *SemesterRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

type SemesterUpdateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SemesterUpdateRequest) Reset() {
	*x = SemesterUpdateRequest{}
	mi := &file_pb_semester_semester_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SemesterUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SemesterUpdateRequest) ProtoMessage() {}

func (x *SemesterUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_semester_semester_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SemesterUpdateRequest.ProtoReflect.Descriptor instead.
func (*SemesterUpdateRequest) Descriptor() ([]byte, []int) {
	return file_pb_semester_semester_proto_rawDescGZIP(), []int{1}
}

func (x *SemesterUpdateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SemesterUpdateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SemesterUpdateRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *SemesterUpdateRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

type SemesterResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	StartDate     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SemesterResponse) Reset() {
	*x = SemesterResponse{}
	mi := &file_pb_semester_semester_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SemesterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SemesterResponse) ProtoMessage() {}

func (x *SemesterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_semester_semester_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SemesterResponse.ProtoReflect.Descriptor instead.
func (*SemesterResponse) Descriptor() ([]byte, []int) {
	return file_pb_semester_semester_proto_rawDescGZIP(), []int{2}
}

func (x *SemesterResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SemesterResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SemesterResponse) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *SemesterResponse) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *SemesterResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SemesterResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type SemesterByID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SemesterByID) Reset() {
	*x = SemesterByID{}
	mi := &file_pb_semester_semester_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SemesterByID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SemesterByID) ProtoMessage() {}

func (x *SemesterByID) ProtoReflect() protoreflect.Message {
	mi := &file_pb_semester_semester_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SemesterByID.ProtoReflect.Descriptor instead.
func (*SemesterByID) Descriptor() ([]byte, []int) {
	return file_pb_semester_semester_proto_rawDescGZIP(), []int{3}
}

func (x *SemesterByID) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SemestersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Semesters     []*SemesterResponse    `protobuf:"bytes,1,rep,name=semesters,proto3" json:"semesters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SemestersResponse) Reset() {
	*x = SemestersResponse{}
	mi := &file_pb_semester_semester_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SemestersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SemestersResponse) ProtoMessage() {}

func (x *SemestersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_semester_semester_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SemestersResponse.ProtoReflect.Descriptor instead.
func (*SemestersResponse) Descriptor() ([]byte, []int) {
	return file_pb_semester_semester_proto_rawDescGZIP(), []int{4}
}

func (x *SemestersResponse) GetSemesters() []*SemesterResponse {
	if x != nil {
		return x.Semesters
	}
	return nil
}

// -------------------------------//
//
//	Semester Break Messages    //
//
// -------------------------------//
type SemesterBreakByID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SemesterBreakByID) Reset() {
	*x = SemesterBreakByID{}
	mi := &file_pb_semester_semester_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SemesterBreakByID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SemesterBreakByID) ProtoMessage() {}

func (x *SemesterBreakByID) ProtoReflect() protoreflect.Message {
	mi := &file_pb_semester_semester_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SemesterBreakByID.ProtoReflect.Descriptor instead.
func (*SemesterBreakByID) Descriptor() ([]byte, []int) {
	return file_pb_semester_semester_proto_rawDescGZIP(), []int{5}
}

func (x *SemesterBreakByID) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SemesterBreakRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SemesterId    int64                  `protobuf:"varint,1,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	BreakDate     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=break_date,json=breakDate,proto3" json:"break_date,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SemesterBreakRequest) Reset() {
	*x = SemesterBreakRequest{}
	mi := &file_pb_semester_semester_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SemesterBreakRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SemesterBreakRequest) ProtoMessage() {}

func (x *SemesterBreakRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_semester_semester_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SemesterBreakRequest.ProtoReflect.Descriptor instead.
func (*SemesterBreakRequest) Descriptor() ([]byte, []int) {
	return file_pb_semester_semester_proto_rawDescGZIP(), []int{6}
}

func (x *SemesterBreakRequest) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *SemesterBreakRequest) GetBreakDate() *timestamppb.Timestamp {
	if x != nil {
		return x.BreakDate
	}
	return nil
}

func (x *SemesterBreakRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type SemesterBreaksResponse struct {
	state          protoimpl.MessageState   `protogen:"open.v1"`
	SemesterBreaks []*SemesterBreakResponse `protobuf:"bytes,1,rep,name=semester_breaks,json=semesterBreaks,proto3" json:"semester_breaks,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SemesterBreaksResponse) Reset() {
	*x = SemesterBreaksResponse{}
	mi := &file_pb_semester_semester_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SemesterBreaksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SemesterBreaksResponse) ProtoMessage() {}

func (x *SemesterBreaksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_semester_semester_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SemesterBreaksResponse.ProtoReflect.Descriptor instead.
func (*SemesterBreaksResponse) Descriptor() ([]byte, []int) {
	return file_pb_semester_semester_proto_rawDescGZIP(), []int{7}
}

func (x *SemesterBreaksResponse) GetSemesterBreaks() []*SemesterBreakResponse {
	if x != nil {
		return x.SemesterBreaks
	}
	return nil
}

type SemesterBreakResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SemesterId    int64                  `protobuf:"varint,2,opt,name=semester_id,json=semesterId,proto3" json:"semester_id,omitempty"`
	BreakDate     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=break_date,json=breakDate,proto3" json:"break_date,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SemesterBreakResponse) Reset() {
	*x = SemesterBreakResponse{}
	mi := &file_pb_semester_semester_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SemesterBreakResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SemesterBreakResponse) ProtoMessage() {}

func (x *SemesterBreakResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_semester_semester_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SemesterBreakResponse.ProtoReflect.Descriptor instead.
func (*SemesterBreakResponse) Descriptor() ([]byte, []int) {
	return file_pb_semester_semester_proto_rawDescGZIP(), []int{8}
}

func (x *SemesterBreakResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SemesterBreakResponse) GetSemesterId() int64 {
	if x != nil {
		return x.SemesterId
	}
	return 0
}

func (x *SemesterBreakResponse) GetBreakDate() *timestamppb.Timestamp {
	if x != nil {
		return x.BreakDate
	}
	return nil
}

func (x *SemesterBreakResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SemesterBreakResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SemesterBreakResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type SemesterEmptyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SemesterEmptyRequest) Reset() {
	*x = SemesterEmptyRequest{}
	mi := &file_pb_semester_semester_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SemesterEmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SemesterEmptyRequest) ProtoMessage() {}

func (x *SemesterEmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_semester_semester_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SemesterEmptyRequest.ProtoReflect.Descriptor instead.
func (*SemesterEmptyRequest) Descriptor() ([]byte, []int) {
	return file_pb_semester_semester_proto_rawDescGZIP(), []int{9}
}

type SemesterEmptyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SemesterEmptyResponse) Reset() {
	*x = SemesterEmptyResponse{}
	mi := &file_pb_semester_semester_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SemesterEmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SemesterEmptyResponse) ProtoMessage() {}

func (x *SemesterEmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_semester_semester_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SemesterEmptyResponse.ProtoReflect.Descriptor instead.
func (*SemesterEmptyResponse) Descriptor() ([]byte, []int) {
	return file_pb_semester_semester_proto_rawDescGZIP(), []int{10}
}

var File_pb_semester_semester_proto protoreflect.FileDescriptor

const file_pb_semester_semester_proto_rawDesc = "" +
	"\n" +
	"\x1apb/semester/semester.proto\x12\n" +
	"semesterpb\x1a\x1fgoogle/protobuf/timestamp.proto\"\x97\x01\n" +
	"\x0fSemesterRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x129\n" +
	"\n" +
	"start_date\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\"\xad\x01\n" +
	"\x15SemesterUpdateRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x129\n" +
	"\n" +
	"start_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\"\x9e\x02\n" +
	"\x10SemesterResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x129\n" +
	"\n" +
	"start_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartDate\x125\n" +
	"\bend_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\aendDate\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x1e\n" +
	"\fSemesterByID\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"O\n" +
	"\x11SemestersResponse\x12:\n" +
	"\tsemesters\x18\x01 \x03(\v2\x1c.semesterpb.SemesterResponseR\tsemesters\"#\n" +
	"\x11SemesterBreakByID\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\x94\x01\n" +
	"\x14SemesterBreakRequest\x12\x1f\n" +
	"\vsemester_id\x18\x01 \x01(\x03R\n" +
	"semesterId\x129\n" +
	"\n" +
	"break_date\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tbreakDate\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\"d\n" +
	"\x16SemesterBreaksResponse\x12J\n" +
	"\x0fsemester_breaks\x18\x01 \x03(\v2!.semesterpb.SemesterBreakResponseR\x0esemesterBreaks\"\x9b\x02\n" +
	"\x15SemesterBreakResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vsemester_id\x18\x02 \x01(\x03R\n" +
	"semesterId\x129\n" +
	"\n" +
	"break_date\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tbreakDate\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x16\n" +
	"\x14SemesterEmptyRequest\"\x17\n" +
	"\x15SemesterEmptyResponse2\xad\x05\n" +
	"\x0fSemesterService\x12M\n" +
	"\x0eCreateSemester\x12\x1b.semesterpb.SemesterRequest\x1a\x1c.semesterpb.SemesterResponse\"\x00\x12K\n" +
	"\x0fGetSemesterByID\x12\x18.semesterpb.SemesterByID\x1a\x1c.semesterpb.SemesterResponse\"\x00\x12T\n" +
	"\x0fGetAllSemesters\x12 .semesterpb.SemesterEmptyRequest\x1a\x1d.semesterpb.SemestersResponse\"\x00\x12S\n" +
	"\x0eUpdateSemester\x12!.semesterpb.SemesterUpdateRequest\x1a\x1c.semesterpb.SemesterResponse\"\x00\x12G\n" +
	"\x06Delete\x12\x18.semesterpb.SemesterByID\x1a!.semesterpb.SemesterEmptyResponse\"\x00\x12Y\n" +
	"\x10AddSemesterBreak\x12 .semesterpb.SemesterBreakRequest\x1a!.semesterpb.SemesterBreakResponse\"\x00\x12Y\n" +
	"\x13RemoveSemesterBreak\x12\x1d.semesterpb.SemesterBreakByID\x1a!.semesterpb.SemesterBreakResponse\"\x00\x12T\n" +
	"\x12ListSemesterBreaks\x12\x18.semesterpb.SemesterByID\x1a\".semesterpb.SemesterBreaksResponse\"\x00BIZGgithub.com/olzzhas/edunite-server/course_service/pb/semester;semesterpbb\x06proto3"

var (
	file_pb_semester_semester_proto_rawDescOnce sync.Once
	file_pb_semester_semester_proto_rawDescData []byte
)

func file_pb_semester_semester_proto_rawDescGZIP() []byte {
	file_pb_semester_semester_proto_rawDescOnce.Do(func() {
		file_pb_semester_semester_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pb_semester_semester_proto_rawDesc), len(file_pb_semester_semester_proto_rawDesc)))
	})
	return file_pb_semester_semester_proto_rawDescData
}

var file_pb_semester_semester_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_pb_semester_semester_proto_goTypes = []any{
	(*SemesterRequest)(nil),        // 0: semesterpb.SemesterRequest
	(*SemesterUpdateRequest)(nil),  // 1: semesterpb.SemesterUpdateRequest
	(*SemesterResponse)(nil),       // 2: semesterpb.SemesterResponse
	(*SemesterByID)(nil),           // 3: semesterpb.SemesterByID
	(*SemestersResponse)(nil),      // 4: semesterpb.SemestersResponse
	(*SemesterBreakByID)(nil),      // 5: semesterpb.SemesterBreakByID
	(*SemesterBreakRequest)(nil),   // 6: semesterpb.SemesterBreakRequest
	(*SemesterBreaksResponse)(nil), // 7: semesterpb.SemesterBreaksResponse
	(*SemesterBreakResponse)(nil),  // 8: semesterpb.SemesterBreakResponse
	(*SemesterEmptyRequest)(nil),   // 9: semesterpb.SemesterEmptyRequest
	(*SemesterEmptyResponse)(nil),  // 10: semesterpb.SemesterEmptyResponse
	(*timestamppb.Timestamp)(nil),  // 11: google.protobuf.Timestamp
}
var file_pb_semester_semester_proto_depIdxs = []int32{
	11, // 0: semesterpb.SemesterRequest.start_date:type_name -> google.protobuf.Timestamp
	11, // 1: semesterpb.SemesterRequest.end_date:type_name -> google.protobuf.Timestamp
	11, // 2: semesterpb.SemesterUpdateRequest.start_date:type_name -> google.protobuf.Timestamp
	11, // 3: semesterpb.SemesterUpdateRequest.end_date:type_name -> google.protobuf.Timestamp
	11, // 4: semesterpb.SemesterResponse.start_date:type_name -> google.protobuf.Timestamp
	11, // 5: semesterpb.SemesterResponse.end_date:type_name -> google.protobuf.Timestamp
	11, // 6: semesterpb.SemesterResponse.created_at:type_name -> google.protobuf.Timestamp
	11, // 7: semesterpb.SemesterResponse.updated_at:type_name -> google.protobuf.Timestamp
	2,  // 8: semesterpb.SemestersResponse.semesters:type_name -> semesterpb.SemesterResponse
	11, // 9: semesterpb.SemesterBreakRequest.break_date:type_name -> google.protobuf.Timestamp
	8,  // 10: semesterpb.SemesterBreaksResponse.semester_breaks:type_name -> semesterpb.SemesterBreakResponse
	11, // 11: semesterpb.SemesterBreakResponse.break_date:type_name -> google.protobuf.Timestamp
	11, // 12: semesterpb.SemesterBreakResponse.created_at:type_name -> google.protobuf.Timestamp
	11, // 13: semesterpb.SemesterBreakResponse.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 14: semesterpb.SemesterService.CreateSemester:input_type -> semesterpb.SemesterRequest
	3,  // 15: semesterpb.SemesterService.GetSemesterByID:input_type -> semesterpb.SemesterByID
	9,  // 16: semesterpb.SemesterService.GetAllSemesters:input_type -> semesterpb.SemesterEmptyRequest
	1,  // 17: semesterpb.SemesterService.UpdateSemester:input_type -> semesterpb.SemesterUpdateRequest
	3,  // 18: semesterpb.SemesterService.Delete:input_type -> semesterpb.SemesterByID
	6,  // 19: semesterpb.SemesterService.AddSemesterBreak:input_type -> semesterpb.SemesterBreakRequest
	5,  // 20: semesterpb.SemesterService.RemoveSemesterBreak:input_type -> semesterpb.SemesterBreakByID
	3,  // 21: semesterpb.SemesterService.ListSemesterBreaks:input_type -> semesterpb.SemesterByID
	2,  // 22: semesterpb.SemesterService.CreateSemester:output_type -> semesterpb.SemesterResponse
	2,  // 23: semesterpb.SemesterService.GetSemesterByID:output_type -> semesterpb.SemesterResponse
	4,  // 24: semesterpb.SemesterService.GetAllSemesters:output_type -> semesterpb.SemestersResponse
	2,  // 25: semesterpb.SemesterService.UpdateSemester:output_type -> semesterpb.SemesterResponse
	10, // 26: semesterpb.SemesterService.Delete:output_type -> semesterpb.SemesterEmptyResponse
	8,  // 27: semesterpb.SemesterService.AddSemesterBreak:output_type -> semesterpb.SemesterBreakResponse
	8,  // 28: semesterpb.SemesterService.RemoveSemesterBreak:output_type -> semesterpb.SemesterBreakResponse
	7,  // 29: semesterpb.SemesterService.ListSemesterBreaks:output_type -> semesterpb.SemesterBreaksResponse
	22, // [22:30] is the sub-list for method output_type
	14, // [14:22] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_pb_semester_semester_proto_init() }
func file_pb_semester_semester_proto_init() {
	if File_pb_semester_semester_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pb_semester_semester_proto_rawDesc), len(file_pb_semester_semester_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_semester_semester_proto_goTypes,
		DependencyIndexes: file_pb_semester_semester_proto_depIdxs,
		MessageInfos:      file_pb_semester_semester_proto_msgTypes,
	}.Build()
	File_pb_semester_semester_proto = out.File
	file_pb_semester_semester_proto_goTypes = nil
	file_pb_semester_semester_proto_depIdxs = nil
}
