-- Create facilities table
CREATE TABLE IF NOT EXISTS facilities (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    max_capacity INT NOT NULL DEFAULT 30,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version INT NOT NULL DEFAULT 1,
    UNIQUE(title)
);

-- Create trigger for updated_at
CREATE TRIGGER trg_facilities_updated
    BEFORE UPDATE ON facilities
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();
